package cn.genn.lock.local;

import cn.genn.lock.base.AbstractLockExecutor;
import cn.genn.lock.base.LockException;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.util.Objects;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <p>基于{@link ReentrantLock}实现的单机本地锁执行器，用于提供加锁和解锁功能。
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class LocalLockExecutor extends AbstractLockExecutor<LocalLockExecutor.LocalLock> {

    /**
     * 是否为公平锁
     */
    private boolean fair = true;

    private final ConcurrentMap<String, LocalLock> lockMap = new ConcurrentReferenceHashMap<>(
            32, ConcurrentReferenceHashMap.ReferenceType.WEAK
    );

    @Override
    public LocalLock acquire(String lockKey, long expire, long acquireTimeout) {
        LocalLock lock = lockMap.compute(lockKey, (key, current) ->
                Objects.isNull(current) || current.isExpired() ? new LocalLock(fair, expire) : current
        );
        try {
            if (lock.tryLock(acquireTimeout, TimeUnit.MILLISECONDS)) {
                // 不管是重入还是新加锁，都需要重置过期时间
                lock.resetExpire(expire);
                return lock;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("lock fail", e);
            throw new LockException();
        }
        return null;
    }

    /**
     * 尝试针对 key 解锁：
     *
     * @param key          加锁key
     * @param lockInstance 锁实例
     * @return 是否释放成功
     */
    @Override
    public boolean releaseLock(String key, LocalLock lockInstance) {
        // 当用户直接通过锁实例进行解锁时，则此时锁可能已经被释放
        if (!lockInstance.isHeldByCurrentThread()) {
            return false;
        }
        boolean expired = lockInstance.isExpired();
        // 进入此步骤，说明当前线程仍然持有锁，不过可能已经过期，也无法保证在列表中依然存在
        lockMap.computeIfPresent(key, (k, current) -> {
            // 若锁已经在列表中不存在，说明其可能已因过期而被其他线程移除，故不做任何处理
            if (current != lockInstance) {
                return current;
            }
            // 若锁仍然存在，且可重入数量大于1，则不做任何处理，等待后续重入
            if (current.getHoldCount() > 1) {
                return current;
            }
            // 锁仍然存在，且已经是最后一次重入，
            // 此时，除非有其他线程已经获取到该实例并进行竞争，否则将其从列表移除
            return current.getQueueLength() > 0 ? current : null;
        });
        // 无论如何都进行一次解锁操作，因此除非锁已经过期，否则总是认为解锁成功
        lockInstance.unlock();
        return !expired;
    }

    /**
     * 基于{@link ReentrantLock}实现的本地锁,
     * 当创建时需要指定锁的有效时间，
     * 如果有效时间小于0则表示永不过期。
     *
     * <AUTHOR>
     */
    public static class LocalLock extends ReentrantLock {

        /**
         * 永不过期
         */
        public static final long NEVER_EXPIRE = -1L;

        LocalLock(boolean fair) {
            super(fair);
            this.expireTime = NEVER_EXPIRE;
        }

        /**
         * 构造器
         *
         * @param fair       是否为公平锁
         * @param expireTime 锁的有效时间
         */
        LocalLock(boolean fair, long expireTime) {
            super(fair);
            this.expireTime = expireTime;
        }

        /**
         * 过期时间点
         */
        private long expireTime;

        /**
         * 当前锁是否已经过期
         *
         * @return 是否
         */
        public boolean isExpired() {
            return expireTime != NEVER_EXPIRE
                    && System.currentTimeMillis() > expireTime;
        }

        /**
         * 更新过期时间
         *
         * @param expire 锁的有效时间
         */
        public void resetExpire(long expire) {
            this.expireTime = expire < 0 ?
                    NEVER_EXPIRE : System.currentTimeMillis() + expire;
        }
    }
}
