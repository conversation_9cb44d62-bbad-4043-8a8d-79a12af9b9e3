/*
 *  Copyright (c) 2018-2022, b<PERSON><PERSON><PERSON><PERSON> (<EMAIL>).
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package cn.genn.lock.base;


/**
 * 分布式锁核心处理器
 *
 * <AUTHOR>
 */
public interface LockExecutor<T> {

    /**
     * 加锁
     *
     * @param lockKey        锁标识
     * @param expire         锁有效时间
     * @param acquireTimeout 获取锁超时时间
     */
    T acquire(String lockKey, long expire, long acquireTimeout);

    /**
     * 解锁
     *
     * @param key          加锁key
     * @param lockInstance 锁实例
     * @return 是否释放成功
     */
    boolean releaseLock(String key, T lockInstance);

}
