/*
 *  Copyright (c) 2018-2022, b<PERSON><PERSON><PERSON><PERSON> (<EMAIL>).
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package cn.genn.lock.base.properties;

import cn.genn.lock.base.LockExecutor;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class LockInfo {

    /**
     * 锁名称
     */
    private String lockKey;

    /**
     * 过期时间
     */
    private Long expire;

    /**
     * 获取锁超时时间
     */
    private Long acquireTimeout;

    /**
     * 锁实例
     */
    private Object lockInstance;

    /**
     * 锁执行器
     */
    private LockExecutor lockExecutor;
}
