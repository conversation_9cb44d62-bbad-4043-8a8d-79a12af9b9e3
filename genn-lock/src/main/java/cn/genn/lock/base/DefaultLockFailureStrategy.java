package cn.genn.lock.base;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * <AUTHOR>
 */
public class DefaultLockFailureStrategy implements LockFailureStrategy {

    protected static String DEFAULT_MESSAGE = "操作频繁,请稍后再试";

    @Override
    public void onLockFailure(String key, ProceedingJoinPoint joinPoint) {
        throw new BaseException(CommonCode.LOCK_ERROR.buildCode(), DEFAULT_MESSAGE);
    }
}
