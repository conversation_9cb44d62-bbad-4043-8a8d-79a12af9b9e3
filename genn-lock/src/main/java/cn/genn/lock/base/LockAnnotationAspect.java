package cn.genn.lock.base;

import cn.genn.core.utils.SpelUtils;
import cn.genn.lock.base.annotation.Lock;
import cn.genn.lock.base.properties.LockInfo;
import cn.genn.lock.base.properties.LockProperties;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.expression.spel.support.StandardEvaluationContext;

/**
 * <AUTHOR>
 */
@Aspect
@Slf4j
@Order(1)
public class LockAnnotationAspect {

    private final LockTemplate lockTemplate;
    private final LockProperties lockProperties;

    public LockAnnotationAspect(LockTemplate lockTemplate, LockProperties properties) {
        this.lockTemplate = lockTemplate;
        this.lockProperties = properties;
    }


    @Around("@annotation(lock)")
    public Object logCost(ProceedingJoinPoint joinPoint, Lock lock) throws Throwable {
        Class<? extends LockExecutor<?>> executor = lock.executor();
        StandardEvaluationContext spelContext = SpelUtils.initSpelContext(joinPoint);
        String fieldKey = SpelUtils.parseSpelExpression(spelContext, lock.fieldKey(), String.class);
        String key = generateLockKey(joinPoint, lock.name(), fieldKey);
        LockInfo lockInfo = null;
        try {
            lockInfo = lockTemplate.lock(key,
                    lock.expireTimeMillis() == -1 ? lockProperties.getExpire() : lock.expireTimeMillis(),
                    lock.acquireTimeout() == -1 ? lockProperties.getAcquireTimeout() : lock.acquireTimeout(),
                    executor);
            if (lockInfo != null) {
                return joinPoint.proceed();
            }
            Class<? extends LockFailureStrategy> failStrategy = lock.failStrategy();
            LockFailureStrategy failureStrategy = SpringUtil.getBean(failStrategy);
            if (failureStrategy == null) {
                throw new LockException();
            }
            failureStrategy.onLockFailure(key, joinPoint);
        } finally {
            if (lockInfo != null) {
                final boolean releaseLock = lockTemplate.releaseLock(lockInfo);
                if (!releaseLock) {
                    log.error("releaseLock fail,lockKey={}", lockInfo.getLockKey());
                }
            }
        }
        return null;

    }

    private String generateLockKey(ProceedingJoinPoint joinPoint, String name, String fieldKey) {
        if (StrUtil.isEmpty(name)) {
            //使用默认的包名+类名+方法名
            name = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();
        }
        return name + (fieldKey == null ? "" : (":" + fieldKey));
    }
}
