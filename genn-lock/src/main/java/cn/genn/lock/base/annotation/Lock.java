package cn.genn.lock.base.annotation;

import cn.genn.lock.base.DefaultLockFailureStrategy;
import cn.genn.lock.base.LockExecutor;
import cn.genn.lock.base.LockFailureStrategy;
import cn.genn.lock.redisson.RedissonLockExecutor;

import java.lang.annotation.*;

/**
 * 注解式分布式锁
 *
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Lock {

    /**
     * name,锁的名称,为空默认为 key前缀+包名+类名+方法名
     */
    String name() default "";

    /**
     * field key
     * @return
     */
    String fieldKey() default "";

    /**
     * 锁的执行器,默认为redisson分布式锁
     * @return
     */
    Class<? extends LockExecutor<?>> executor() default RedissonLockExecutor.class;

    /**
     * 过期时间,-1取配置文件中默认值
     *
     * @return
     */
    int expireTimeMillis() default -1;

    /**
     * 获取锁超时时间
     *
     * @return
     */
    int acquireTimeout() default -1;

    /**
     * 失败策略
     *
     * @return LockFailureStrategy
     */
    Class<? extends LockFailureStrategy> failStrategy() default DefaultLockFailureStrategy.class;
}
