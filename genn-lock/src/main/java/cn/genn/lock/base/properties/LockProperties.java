/*
 *  Copyright (c) 2018-2022, b<PERSON><PERSON><PERSON><PERSON> (<EMAIL>).
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package cn.genn.lock.base.properties;

import cn.genn.lock.base.DefaultLockFailureStrategy;
import cn.genn.lock.base.LockExecutor;
import cn.genn.lock.base.LockFailureStrategy;
import cn.genn.lock.redisson.RedissonLockExecutor;
import lombok.Getter;
import lombok.Setter;

/**
 * lock配置
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class LockProperties {

    private boolean enabled = true;

    /**
     * 过期时间 单位：毫秒
     */
    private Long expire = 5000L;

    /**
     * 获取锁超时时间 单位：毫秒
     */
    private Long acquireTimeout = 5000L;

    /**
     * 默认执行器，不设置为redisson实现
     */
    private Class<? extends LockExecutor> primaryExecutor = RedissonLockExecutor.class;
    /**
     * 默认失败策略
     */
    private Class<? extends LockFailureStrategy> primaryFailureStrategy = DefaultLockFailureStrategy.class;

    /**
     * 锁key前缀
     */
    private String lockKeyPrefix = "GENN:LOCK";
}
