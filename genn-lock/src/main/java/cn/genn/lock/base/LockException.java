package cn.genn.lock.base;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.exception.MessageCodeWrap;

/**
 * 锁异常
 * <AUTHOR>
 */
public class LockException extends BaseException {

    private static final long serialVersionUID = -9208455795609363051L;

    public LockException() {
        super(CommonCode.LOCK_ERROR);
    }

    public LockException(String message) {
        super(CommonCode.LOCK_ERROR, message);
    }

    public LockException(String code, String message) {
        super(code, message);
    }

    public LockException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public LockException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
