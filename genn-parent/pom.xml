<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.nova</groupId>
        <artifactId>genn-project-nova</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <packaging>pom</packaging>
    <artifactId>genn-parent</artifactId>

    <properties>
        <skipTests>true</skipTests>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <maven.wagon.http.ssl.insecure>true</maven.wagon.http.ssl.insecure>
        <maven.wagon.http.ssl.allowall>true</maven.wagon.http.ssl.allowall>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- 依赖包版本定义 -->
        <genn-project-nova.version>1.0.0-SNAPSHOT</genn-project-nova.version>
        <genn-spring-boot-project.version>1.0.0-SNAPSHOT</genn-spring-boot-project.version>

        <hutool.version>5.8.35</hutool.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <jackson.version>2.18.1</jackson.version>
        <jackson-bom.version>2.18.1</jackson-bom.version>
        <sl4j.version>2.0.16</sl4j.version>
        <feishu.version>2.4.12</feishu.version>

        <spring.version>6.2.0</spring.version>
        <spring-boot.version>3.4.0</spring-boot.version>
        <spring-cloud.commons.version>4.2.0</spring-cloud.commons.version>
        <spring-cloud-openfeign.version>4.2.0</spring-cloud-openfeign.version>
        <jakarta.servlet-api.version>6.0.0</jakarta.servlet-api.version>
        <jakarta.validation-api.version>3.0.2</jakarta.validation-api.version>
        <swagger-v3.version>2.2.8</swagger-v3.version>
        <spring-ai.version>1.0.0</spring-ai.version>

        <mybatis-plus.version>3.5.10.1</mybatis-plus.version>

        <hibernate-validator.version>8.0.1.Final</hibernate-validator.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <skywalking.version>9.1.0</skywalking.version>
        <redisson.version>3.17.6</redisson.version>
        <caffeine.version>3.1.8</caffeine.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <cron-utils.version>9.2.0</cron-utils.version>
        <okhttp.verson>4.12.0</okhttp.verson>
        <cola-statemachine.version>4.3.2</cola-statemachine.version>
        <jasypt.version>1.9.2</jasypt.version>
        <httpclient.version>5.4.1</httpclient.version>
        <bucket4j.version>8.10.1</bucket4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--        Spring相关        -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${spring-cloud-openfeign.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-loadbalancer</artifactId>
                <version>${spring-cloud.commons.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-commons</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-openai</artifactId>
                <version>${spring-ai.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-deepseek</artifactId>
                <version>${spring-ai.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-client-chat</artifactId>
                <version>${spring-ai.version}</version>
            </dependency>


            <!--            数据库相关-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <!--  自定义内部依赖-->
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-core</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-web</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-security</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-database</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-monitor</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-cache</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-lock</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-job</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-bridge</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-feishu</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-ai</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.genn.nova</groupId>
                <artifactId>genn-graph</artifactId>
                <version>${genn-project-nova.version}</version>
            </dependency>


            <!--  其他依赖            -->
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger-v3.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bucket4j</groupId>
                <artifactId>bucket4j_jdk8-core</artifactId>
                <version>${bucket4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-statemachine</artifactId>
                <version>${cola-statemachine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cronutils</groupId>
                <artifactId>cron-utils</artifactId>
                <version>${cron-utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.verson}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp.verson}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-parameter-names</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${sl4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.larksuite.oapi</groupId>
                <artifactId>oapi-sdk</artifactId>
                <version>${feishu.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <!-- 子项目默认引入,不需要声明的包    -->
    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
    </dependencies>

</project>