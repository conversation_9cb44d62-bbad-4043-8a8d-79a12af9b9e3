package cn.genn.core.exception;

/**
 * 业务异常定义
 * <AUTHOR>
 */
public class BusinessException extends BaseException{

    private static final long serialVersionUID = -3702323089829534951L;

    public BusinessException() {
        super(CommonCode.FAIL);
    }

    public BusinessException(String message) {
        super(CommonCode.FAIL.buildCode(), message);
    }

    public BusinessException(String code, String message) {
        super(code, message);
    }

    public BusinessException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public BusinessException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
