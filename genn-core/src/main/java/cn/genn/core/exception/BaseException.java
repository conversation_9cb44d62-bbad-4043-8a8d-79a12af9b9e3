package cn.genn.core.exception;


import lombok.Getter;

import java.text.MessageFormat;

/**
 * 通用状态码异常定义
 * <AUTHOR>
 */
public class BaseException extends RuntimeException {

    private static final long serialVersionUID = -1541694218156118072L;

    @Getter
    private final String code;

    public BaseException() {
        super(CommonCode.FAIL.getDescription());
        this.code = CommonCode.FAIL.buildCode();
    }

    public BaseException(String message) {
        super(message);
        this.code = CommonCode.FAIL.buildCode();
    }

    public BaseException(String code, String message) {
        super(message);
        this.code = code;
    }

    public BaseException(String code, String message, Throwable throwable) {
        super(message, throwable);
        this.code = code;
    }

    public BaseException(MessageCodeWrap messageCode, Object... args) {
        super(MessageFormat.format(messageCode.getDescription(), args));
        this.code = messageCode.buildCode();
    }
}
