package cn.genn.core.exception;

/**
 * 错误码总共是9位：产品/终端类型（2位）+服务类型（2位）+ 业务类型(2位) + 业务错误码（3位）
 * 其中产品类型和服务类型由框架层定义，业务类型和业务错误码由各子系统、服务、模块定义。
 *
 * <AUTHOR>
 */
public interface MessageCodeWrap {

    String DEFAULT_BIZ_CODE = "00";

    /**
     * 获取两位业务类型编码
     * 业务聚合类型,由服务自己定义
     * 例如 00:通用
     * 01:司机
     * 02:订单
     * 等等
     * @return
     */
    default String getBizCode() {
        return DEFAULT_BIZ_CODE;
    }

    /**
     * 获取后三位业务错误码
     * 201~899各自定义
     * @return
     */
    String getCode();

    /**
     * 获取错误描述
     * @return
     */
    String getDescription();

    /**
     * 返回完整拼接的错误码
     */
    default String buildCode() {
        return getBizCode() + getCode();
    }


}
