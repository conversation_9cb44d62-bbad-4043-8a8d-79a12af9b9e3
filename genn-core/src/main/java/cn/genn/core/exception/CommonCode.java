package cn.genn.core.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CommonCode implements MessageCodeWrap {

    SUCCESS("200", "成功"),
    UNSUPPORTED_ENCODING("001", "不支持的编码"),
    LOCK_ERROR("002", "操作频繁，请稍后再试！"),
    SIGN_ERROR("003", "非法请求，不允许访问！"),
    VALIDATOR_NOT_FOUND("004", "校验器未找到"),
    METHOD_ARGUMENT_NOT_VALID("400", "参数校验失败，请联系相关人员处理！"),
    METHOD_ARGUMENT_NOT_NULL("400", "参数{0}不允许为空！"),
    HTTP_METHOD_NOT_SUPPORTED("405", "无效请求！"),
    DUPLICATE_KEY("409", "您的操作重复，请确认后重试！"),
    MAX_UPLOAD_SIZE_EXCEEDED("413", "您上传的文件大小超过限制，请修改后重试！"),
    TOO_MANY_REQUESTS("429", "请求过于频繁，请稍后再试！"),
    FAIL("500", "此功能出现内部处理错误，请联系相关人员处理！"),
    FEIGN_ERROR("900", "远程服务维护中，请稍后再试！"),
    CONNECT_ERROR("901", "远程服务维护中，请稍后再试！"),
    ;

    private final String code;
    private final String description;
    CommonCode(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
