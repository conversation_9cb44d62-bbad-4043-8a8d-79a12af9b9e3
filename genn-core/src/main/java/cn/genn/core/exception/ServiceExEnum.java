package cn.genn.core.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ServiceExEnum {

    COMMON("00", "通用"),
    IAM("01", "网关服务"),
    OPS("02", "OPS编排"),
    TMS("03", "TMS编排"),
    BMS("04", "BMS核心服务"),
    DEVELOP_SERVICE("05", "开发者中心服务"),
    API_MANAGER("07", "API管理服务"),
    UPM("09", "权限中心服务"),
    MONITOR("10", "监控中心服务"),
    PMS("11", "平台管理中心"),
    CORE_CARRIER("12", "运力中心"),
    CORE_ORDER("13", "订单中心"),
    PARTNER("14", "合作伙伴"),
    SUPPORT_MDM("15", "主数据"),
    SUPPORT_FMS("16", "文件服务"),
    SUPPORT_NCS("17", "消息中心"),
    CORE_SIF("18", "结算中心"),
    FSP("19", "保理服务"),
    SUPPORT_ESM("20", "电签服务"),
    DATA("21", "数据服务"),
    HOS("22", "零售管理服务"),
    BOS("23", "站端管理服务"),
    FCS("24", "车队卡管理服务"),
    INVOICE("25", "发票服务"),
    CORE_STATION("26", "站端中心"),
    CORE_STOCK("27", "库存中心"),
    CORE_CUST("28", "会员中心"),
    CORE_MARKETING("29", "营销中心"),
    MCS("30", "会员卡管理服务"),
    POS("31", "POS管理服务"),
    CORE_SERVE("32", "履约中心"),
    CORE_TRADE("33", "交易中心"),
    ORCH_MEETING("34", "会议管理系统"),
    AI_HUB("35", "AI中台"),
    AI_ENTERPRISE_BRAIN("36", "企业大脑"),

    ;

    private final String code;
    private final String desc;

    ServiceExEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ServiceExEnum getByCode(String code) {
        for (ServiceExEnum value : ServiceExEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
