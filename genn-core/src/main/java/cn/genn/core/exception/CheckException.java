package cn.genn.core.exception;

/**
 * 校验异常
 * <AUTHOR>
 */
public class CheckException extends BaseException{

    private static final long serialVersionUID = -9208455795609363051L;

    public CheckException() {
        super(CommonCode.FAIL);
    }

    public CheckException(String message) {
        super(CommonCode.FAIL.buildCode(), message);
    }

    public CheckException(String code, String message) {
        super(code, message);
    }

    public CheckException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public CheckException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
