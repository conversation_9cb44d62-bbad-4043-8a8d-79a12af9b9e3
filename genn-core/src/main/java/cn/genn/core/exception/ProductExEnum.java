package cn.genn.core.exception;

import lombok.Getter;

/**
 * 产品类型异常码定义
 *
 * <AUTHOR>
 */
@Getter
public enum ProductExEnum {

    /**
     * 定义产品端类型
     */
    COMMON("00", "通用"),
    API_OPEN("01","API开放平台"),
    ESG( "02","零碳物流平台"),
    DATA_HUB("03","数据中台"),
    RETAIL("04", "补能平台"),
    PF("05", "鹏飞平台"),
    AI("06", "AI平台"),
    ;

    private final String code;
    private final String desc;

    ProductExEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductExEnum getByCode(String code) {
        for (ProductExEnum value : ProductExEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
