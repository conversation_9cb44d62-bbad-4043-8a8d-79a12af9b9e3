package cn.genn.core.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * <AUTHOR>
 */
public class RmbUtils {

    public static String fen2yuan(double value) {
        return new DecimalFormat("#.##").format(MathUtils.div(value, 100));
    }

    /**
     * 分 转换成 元
     *
     * @param value
     * @return
     */
    public static String fen2yuan(long value) {
        return new DecimalFormat("#.##").format(MathUtils.div(value, 100));
    }

    /**
     * 分 转换成 元
     * @param value
     * @return
     */
    public static Double fen2yuanNumber(long value) {
        return MathUtils.div(value, 100, 2, true);
    }

    /**
     * 元 转 字符串
     * @param value
     * @return
     */
    public static String yuan(double value) {
        return new DecimalFormat("#.##").format(value);
    }

    /**
     * 元 转换成 分
     *
     * @param value
     * @return
     */
    public static Long yuan2fen(String value) {
        return yuan2fen(Double.valueOf(value));
    }

    /**
     * 元 转换成 分
     *
     * @param value
     * @return
     */
    public static Long yuan2fen(Double value) {
        try {
            return Long.parseLong(new DecimalFormat("#").format(BigDecimal
                    .valueOf(value)
                    .multiply(BigDecimal.valueOf(100)).doubleValue()));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 元 转换成 分
     * @param value
     * @return
     */
    public static Integer yuan2fenLow(Double value) {
        try {
            return Integer.parseInt(new DecimalFormat("#").format(BigDecimal
                    .valueOf(value)
                    .multiply(BigDecimal.valueOf(100)).doubleValue()));
        } catch (Exception e) {
            return null;
        }
    }
}
