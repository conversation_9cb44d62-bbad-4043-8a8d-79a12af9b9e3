package cn.genn.core.utils;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class LogUtils {

    /**
     * 指定日志每行最大长度，防止日志超长
     *
     * @param logger
     * @param content
     * @param maxLengthPerLine
     */
    public static void warn(Logger logger, String content, int maxLengthPerLine) {
        if(content == null) {
            return;
        }
        if (maxLengthPerLine <= 0) {
            logger.warn(content);
            return;
        }
        String[] arr = StrUtil.split(content, maxLengthPerLine);
        Arrays.stream(arr).forEach(logger::warn);
    }

    /**
     * 指定日志每行最大长度，防止日志超长
     *
     * @param logger
     * @param content
     * @param maxLengthPerLine
     */
    public static void info(Logger logger, String content, int maxLengthPerLine) {
        if(content == null) {
            return;
        }
        if (maxLengthPerLine <= 0) {
            logger.info(content);
            return;
        }
        String[] arr = StrUtil.split(content, maxLengthPerLine);
        Arrays.stream(arr).forEach(logger::info);
    }

}
