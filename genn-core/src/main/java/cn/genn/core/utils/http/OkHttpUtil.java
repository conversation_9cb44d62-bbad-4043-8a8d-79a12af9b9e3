package cn.genn.core.utils.http;

import cn.genn.core.utils.http.model.HttpResponse;
import cn.genn.core.utils.http.model.NamedInputStream;
import cn.genn.core.utils.http.okhttp.HttpClient;
import cn.genn.core.utils.http.okhttp.OkHttpResponse;
import cn.genn.core.utils.http.okhttp.OkHttpTools;
import okhttp3.Callback;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.http.MediaType;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OkHttpUtil implements HttpUtil {

    private HttpClient httpClient;

    private static OkHttpUtil instance;

    private OkHttpUtil() {
        this(new HttpClient());
    }

    public OkHttpUtil(HttpClient httpClient) {
        this.httpClient = httpClient;
    }

    private static class SingletonInstance {
        private static final OkHttpUtil INSTANCE = new OkHttpUtil();
    }

    public static void setDefaultInstance(OkHttpUtil okHttpUtil) {
        instance = okHttpUtil;
    }

    public static OkHttpUtil getInstance() {
        if (instance != null) {
            return instance;
        }
        return SingletonInstance.INSTANCE;
    }

    @Override
    public OkHttpResponse get(String url, Map<String, String> headers, Map<String, String> params) {
        Request.Builder builder = new Request.Builder()
                .url(OkHttpTools.buildHttpUrl(url, params))
                .headers(OkHttpTools.buildHeaders(headers))
                .get();
        try {
            Response response = httpClient.sync(builder.build());
            return new OkHttpResponse(response);
        } catch (IOException e) {
            throw new RuntimeException("http get request execute failed", e);
        }
    }

    public void get(String url, Map<String, String> headers, Map<String, String> params, Callback responseCallback) {
        Request.Builder builder = new Request.Builder()
                .url(OkHttpTools.buildHttpUrl(url, params))
                .headers(OkHttpTools.buildHeaders(headers))
                .get();
        httpClient.async(builder.build(), responseCallback);
    }

    @Override
    public HttpResponse postForm(String url, Map<String, String> headers, Map<String, String> params, Map<String, String> formData) {
        return post(url, headers, params, MediaType.APPLICATION_FORM_URLENCODED, OkHttpTools.buildFormBody(formData));
    }

    @Override
    public HttpResponse postFile(String url, Map<String, String> headers, Map<String, String> params, Map<String, String> formData, Map<String, File> files) {
        return post(url, headers, params, MediaType.MULTIPART_FORM_DATA, OkHttpTools.buildMultipartBody(formData, files, new HashMap<>()));
    }

    @Override
    public HttpResponse postInputStream(String url, Map<String, String> headers, Map<String, String> params, Map<String, String> formData, Map<String, NamedInputStream> inputStreams) {
        return post(url, headers, params, MediaType.MULTIPART_FORM_DATA, OkHttpTools.buildMultipartBody(formData, new HashMap<>(), inputStreams));
    }

    @Override
    public HttpResponse postBody(String url, Map<String, String> headers, Map<String, String> params, MediaType contentType, String content) {
        return post(url, headers, params, contentType, RequestBody.create(OkHttpTools.getMediaType(contentType), content));
    }

    public HttpResponse post(String url, MediaType contentType, RequestBody requestBody) {
        return post(url, new HashMap<>(), new HashMap<>(), contentType, requestBody);
    }

    public HttpResponse post(String url,
                             Map<String, String> headers,
                             Map<String, String> params,
                             MediaType contentType,
                             RequestBody requestBody) {
        headers = OkHttpTools.setContentType(headers, contentType);
        Request.Builder builder = new Request.Builder()
                .url(OkHttpTools.buildHttpUrl(url, params))
                .headers(OkHttpTools.buildHeaders(headers))
                .post(requestBody);
        try {
            Response response = httpClient.sync(builder.build());
            return new OkHttpResponse(response);
        }catch (IOException e) {
            throw new RuntimeException("http post request execute failed", e);
        }
    }

    public void post(String url,
                     Map<String, String> headers,
                     Map<String, String> params,
                     MediaType contentType,
                     RequestBody requestBody,
                     Callback responseCallback) {
        headers = OkHttpTools.setContentType(headers, contentType);
        Request.Builder builder = new Request.Builder()
                .url(OkHttpTools.buildHttpUrl(url, params))
                .headers(OkHttpTools.buildHeaders(headers))
                .post(requestBody);
        httpClient.async(builder.build(), responseCallback);
    }
}
