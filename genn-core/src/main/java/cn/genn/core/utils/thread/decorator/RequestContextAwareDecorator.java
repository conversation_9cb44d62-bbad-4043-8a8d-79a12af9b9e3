package cn.genn.core.utils.thread.decorator;

import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 线程装饰器, 用于在线程池中传递request上下文
 * 会有问题，request内部的属性无法传递
 *
 * <AUTHOR>
 */
@Deprecated
public class RequestContextAwareDecorator extends BaseThreadDecorator {

    public RequestContextAwareDecorator() {
        super();
    }

    public RequestContextAwareDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }

    @Override
    protected Object beforeExecOnCurrThread() {
        return RequestContextHolder.getRequestAttributes();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof RequestAttributes) {
            RequestContextHolder.setRequestAttributes((RequestAttributes) object);
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        RequestContextHolder.resetRequestAttributes();
    }
}
