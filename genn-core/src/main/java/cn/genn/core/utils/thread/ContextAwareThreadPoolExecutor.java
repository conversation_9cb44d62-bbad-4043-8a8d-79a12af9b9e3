package cn.genn.core.utils.thread;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
public class ContextAwareThreadPoolExecutor extends ThreadPoolExecutor {

    private final ThreadDecorator threadDecorator;

    public ContextAwareThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadDecorator threadDecorator) {
        this(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, Executors.defaultThreadFactory(), threadDecorator);
    }

    public ContextAwareThreadPoolExecutor(int corePoolSize,
                                          int maximumPoolSize,
                                          long keepAliveTime,
                                          TimeUnit unit,
                                          BlockingQueue<Runnable> workQueue,
                                          ThreadFactory threadFactory,
                                          ThreadDecorator threadDecorator) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
        this.threadDecorator = threadDecorator;
    }

    @Override
    public void execute(Runnable runnable) {
        runnable = wrapRunnable(runnable);
        super.execute(runnable);
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        task = wrapCallable(task);
        return super.submit(task);
    }

    private Runnable wrapRunnable(Runnable runnable) {
        return threadDecorator.decorate(runnable);
    }

    private <T> Callable<T> wrapCallable(Callable<T> task) {
        return threadDecorator.decorate(task);
    }
}
