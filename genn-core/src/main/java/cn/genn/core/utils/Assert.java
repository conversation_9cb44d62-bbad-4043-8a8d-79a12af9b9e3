package cn.genn.core.utils;


import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CheckException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.exception.MessageCodeWrap;
import cn.hutool.core.collection.CollUtil;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class Assert {
    /**
     * 断言对象不为空
     * @param arg 对象
     * @param argName 对象名称
     */
    public static void nonNull(Object arg, String argName) {
        if (arg == null) {
            throw new BaseException(CommonCode.METHOD_ARGUMENT_NOT_NULL, argName);
        }
    }

    /**
     * 断言对象不为空
     * @param arg 对象
     * @param messageCode 错误码
     */
    public static void nonNull(Object arg, MessageCodeWrap messageCode) {
        if (arg == null) {
            throw new CheckException(messageCode);
        }
    }

    /**
     * 断言集合不为空
     * @param arg 对象
     */
    public static <T> void nonEmpty(List<T> arg, String argName) {
        if (CollUtil.isEmpty(arg)) {
            throw new BaseException(CommonCode.METHOD_ARGUMENT_NOT_NULL, argName);
        }
    }

    /**
     * 断言集合不为空
     * @param arg 对象
     * @param messageCode 错误码
     */
    public static <T> void nonEmpty(List<T> arg, MessageCodeWrap messageCode) {
        if (CollUtil.isEmpty(arg)) {
            throw new CheckException(messageCode);
        }
    }

    /**
     * 断言表达式为真
     * @param expression 表达式
     * @param messageCode 错误码
     */
    public static void isTrue(boolean expression, MessageCodeWrap messageCode) {
        if (!expression) {
            throw new CheckException(messageCode);
        }
    }
}
