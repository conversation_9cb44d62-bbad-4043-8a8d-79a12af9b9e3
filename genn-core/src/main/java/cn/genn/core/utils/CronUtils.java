package cn.genn.core.utils;

import cn.hutool.core.date.DateUtil;
import com.cronutils.builder.CronBuilder;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.field.expression.FieldExpression;
import com.cronutils.model.field.expression.FieldExpressionFactory;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.Optional;

import static com.cronutils.model.field.expression.FieldExpressionFactory.*;

/**
 * cron 表达式是基于时间的，而不是基于间隔的。这意味着 cron 表达式指定的是在哪些时间点执行任务，而不是执行任务的时间间隔。
 * <p>
 * 例如每7s执行一次
 * <p>
 * 在每分钟的第 0 秒时，任务将会被执行。然后，在接下来的每个 7 秒（第 7 秒，第 14 秒，第 21 秒，第 28 秒，第 35 秒，第 42 秒，第 49 秒，第 56 秒），任务也会被执行。
 * <p>
 * 如果需要严格的每7s执行一次，需要使用xxl-job提供的固定间隔功能
 *
 * <AUTHOR>
 */
public class CronUtils {

    /**
     * 生成指定秒的cron表达式,每隔seconds执行一次
     *
     * @param seconds 每隔多少秒
     * @return cron表达式
     */
    public static String genSecondsCron(Integer seconds) {
        if (seconds > 59 || seconds < 1) {
            throw new IllegalArgumentException("seconds must less than 60 and greater than 0");
        }
        Cron cron = CronBuilder.cron(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ))
                .withYear(always())
                .withDoW(questionMark())
                .withMonth(always())
                .withDoM(always())
                .withHour(always())
                .withMinute(always())
                .withSecond(every(seconds))
                .instance();
        return cron.asString();
    }

    /**
     * 生成指定分的cron表达式,每隔minutes执行一次
     */
    public static String genMinutesCron(Integer minutes) {
        if (minutes > 59 || minutes < 1) {
            throw new IllegalArgumentException("minutes must less than 60 and greater than 0");
        }
        Cron cron = CronBuilder.cron(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ))
                .withYear(always())
                .withDoW(questionMark())
                .withMonth(always())
                .withDoM(always())
                .withHour(always())
                .withMinute(every(minutes))
                .withSecond(on(0))
                .instance();
        return cron.asString();
    }

    /**
     * 生成指定小时的cron表达式,每隔hours执行一次
     */
    public static String genHoursCron(Integer hours) {
        if (hours > 23 || hours < 1) {
            throw new IllegalArgumentException("hours must less than 24 and greater than 0");
        }
        Cron cron = CronBuilder.cron(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ))
                .withYear(always())
                .withDoW(questionMark())
                .withMonth(always())
                .withDoM(always())
                .withHour(every(hours))
                .withMinute(on(0))
                .withSecond(on(0))
                .instance();
        return cron.asString();
    }

    /**
     * 判断是否是合法的cron表达式
     *
     * @param cronExpression cron表达式
     * @return 是否合法
     */
    public static boolean isValidCronExpression(String cronExpression) {
        CronDefinition cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ);
        CronParser parser = new CronParser(cronDefinition);
        try {
            parser.parse(cronExpression);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 将时间转换为cron表达式,表示每天某某时某某分执行
     *
     * @param time HH:mm:ss
     * @return cron表达式
     */
    public static String timeToCron(String time) {
        try {
            new SimpleDateFormat("HH:mm:ss").parse(time);
        } catch (ParseException e) {
            throw new IllegalArgumentException("time format must be HH:mm:ss");
        }
        String[] timeArr = time.split(":");
        FieldExpression hour = FieldExpressionFactory.on(Integer.parseInt(timeArr[0]));
        FieldExpression minutes = FieldExpressionFactory.on(Integer.parseInt(timeArr[1]));
        FieldExpression second = FieldExpressionFactory.on(Integer.parseInt(timeArr[2]));
        Cron cron = CronBuilder.cron(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ))
                .withYear(always())
                .withDoW(questionMark())
                .withMonth(always())
                .withDoM(always())
                .withHour(hour)
                .withMinute(minutes)
                .withSecond(second)
                .instance();
        return cron.asString();
    }


    /**
     * 将日期时间转换为cron表达式
     *
     * @param time yyyy-MM-dd HH:mm:ss
     * @return cron表达式
     */
    public static String dateTimeToCron(String time) {
        Date date;
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(time);
        } catch (ParseException e) {
            throw new IllegalArgumentException("time format must be yyyy-MM-dd HH:mm:ss");
        }
        Cron cron = CronBuilder.cron(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ))
                .withYear(on(DateUtil.year(date)))
                .withDoW(questionMark())
                .withMonth(on(DateUtil.month(date) + 1))
                .withDoM(on(DateUtil.dayOfMonth(date)))
                .withHour(on(DateUtil.hour(date, true)))
                .withMinute(on(DateUtil.minute(date)))
                .withSecond(on(DateUtil.second(date)))
                .instance();
        return cron.asString();
    }

    /**
     * 根据cron表达式和指定时间计算下一次执行时间
     *
     * @param cronExpression cron表达式
     * @param dateTime       指定时间
     * @return 下一次执行时间
     */
    public static LocalDateTime nextExecutionTime(String cronExpression, LocalDateTime dateTime) {
        CronDefinition cronDefinition = CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ);
        CronParser parser = new CronParser(cronDefinition);
        Cron cron = parser.parse(cronExpression);

        ExecutionTime executionTime = ExecutionTime.forCron(cron);
        ZonedDateTime zonedDateTime = dateTime.atZone(ZoneId.of("Asia/Shanghai"));
        Optional<ZonedDateTime> nextExecution = executionTime.nextExecution(zonedDateTime);

        return nextExecution
                .map(ZonedDateTime::toLocalDateTime)
                .orElseThrow(() -> new IllegalArgumentException("No valid execution time found for the given cron expression."));
    }

}
