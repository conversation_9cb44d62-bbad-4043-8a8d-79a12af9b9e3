package cn.genn.core.utils.valid;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class ValidatorChain<T> {

    private final List<Validator<T>> validators = new ArrayList<>();

    public ValidatorChain<T> addValidator(Validator<T>... validator) {
        validators.addAll(Arrays.asList(validator));
        return this;
    }

    public ValidatorResult validate(T entity) {
        ValidatorResult result = new ValidatorResult();
        for (Validator<T> validator : validators) {
            ValidatorResult currResult = validator.validate(entity);
            if (!currResult.isPass()) {
                result.setPass(false);
                result.setNoPassValidatorName(validator.getClass().getName());
                result.setMessage(currResult.getMessage());
                return result;
            }
        }
        result.setPass(true);
        return result;
    }

    /**
     * 如果校验器需要按order排序,则调用此方法.否则按照addValidator的顺序执行
     */
    public void sort() {
        validators.sort(Comparator.comparingInt(Validator::getOrder));
    }
}
