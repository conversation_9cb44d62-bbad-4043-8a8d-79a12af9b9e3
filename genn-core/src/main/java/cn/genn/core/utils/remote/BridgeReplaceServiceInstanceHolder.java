package cn.genn.core.utils.remote;

/**
 * <AUTHOR>
 */
public class BridgeReplaceServiceInstanceHolder {

    private static final ThreadLocal<BridgeServiceInstance> INSTANCE_HOLDER = new ThreadLocal<>();

    public static void set(BridgeServiceInstance instance) {
        INSTANCE_HOLDER.set(instance);
    }

    public static BridgeServiceInstance get() {
        return INSTANCE_HOLDER.get();
    }

    public static void remove() {
        INSTANCE_HOLDER.remove();
    }
}
