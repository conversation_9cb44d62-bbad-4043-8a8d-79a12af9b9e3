package cn.genn.core.utils;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 分批处理list
 *
 * <AUTHOR>
 */
@Slf4j
public class SplitList<T> implements Iterator<List<T>> {

    /**
     * 需要切分的数组
     */
    private List<T> source;

    /**
     * 子list的长度
     */
    private final int perSize;

    private int currIndex;

    public SplitList(List<T> list, int perSize) {
        this.source = list;
        this.perSize = perSize;
    }

    @Override
    public boolean hasNext() {
        return currIndex < source.size();
    }

    @Override
    public List<T> next() {
        int nextIndex = Math.min(currIndex + perSize, source.size());
        List<T> res = source.subList(currIndex, nextIndex);
        currIndex = nextIndex;
        return res;
    }

    @Override
    public void remove() {
        throw new UnsupportedOperationException("remove not support");
    }


    public void consumerList(Consumer<List<T>> consumer) {
        this.reset();
        while (hasNext()) {
            List<T> next = next();
            consumer.accept(next);
        }
    }

    /**
     * 异步消费
     *
     * @param consumer
     * @param executorService
     */
    public void consumerListAsync(Consumer<List<T>> consumer, ExecutorService executorService) {
        this.reset();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        while (hasNext()) {
            List<T> next = next();
            CompletableFuture<Void> task = CompletableFuture.
                    runAsync(() -> consumer.accept(next), executorService)
                    .exceptionally(e -> {
                        log.error("处理数据异常", e);
                        return null;
                    });
            futureList.add(task);
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    }

    public void consumer(Consumer<T> consumer) {
        this.reset();
        while (hasNext()) {
            List<T> next = next();
            if (next != null && !next.isEmpty()) {
                for (T t : next) {
                    consumer.accept(t);
                }
            }
        }
    }

    public void consumerAsync(Consumer<T> consumer, ExecutorService executorService) {
        this.reset();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        while (hasNext()) {
            List<T> next = next();
            if (next != null && !next.isEmpty()) {
                for (T t : next) {
                    CompletableFuture<Void> task = CompletableFuture.
                            runAsync(() -> consumer.accept(t), executorService)
                            .exceptionally(e -> null);
                    futureList.add(task);
                }
            }
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    }

    public <S> List<S> map(Function<T, S> function) {
        this.reset();
        List<S> totalList = new ArrayList<>();
        while (hasNext()) {
            List<T> next = next();
            if (next != null && !next.isEmpty()) {
                for (T t : next) {
                    S s = function.apply(t);
                    totalList.add(s);
                }
            }
        }
        return totalList;
    }

    public <S> List<S> mapAsync(Function<T, S> function, ExecutorService executorService) {
        this.reset();
        List<S> totalList = new ArrayList<>();
        List<CompletableFuture<S>> futureList = new ArrayList<>();
        while (hasNext()) {
            List<T> next = next();
            if (next != null && !next.isEmpty()) {
                for (T t : next) {
                    CompletableFuture<S> task = CompletableFuture
                            .supplyAsync(() -> function.apply(t), executorService)
                            .exceptionally(e -> {
                                log.error("mapListAsync error", e);
                                return null;
                            });
                    futureList.add(task);
                }
            }
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        futureList.forEach(f -> {
            S piece = f.getNow(null);
            if (piece != null) {
                totalList.add(piece);
            }
        });
        return totalList;
    }

    public <S> List<S> mapList(Function<List<T>, List<S>> function) {
        this.reset();
        List<S> totalList = new ArrayList<>();
        while (hasNext()) {
            List<T> next = next();
            List<S> list = function.apply(next);
            if (list != null && !list.isEmpty()) {
                totalList.addAll(list);
            }
        }
        return totalList;
    }

    /**
     * 异步处理,忽略异常
     */
    public <S> List<S> mapListAsync(Function<List<T>, List<S>> function, ExecutorService executorService) {
        this.reset();
        List<S> totalList = new ArrayList<>();
        List<CompletableFuture<List<S>>> futureList = new ArrayList<>();
        while (hasNext()) {
            List<T> next = next();
            CompletableFuture<List<S>> task = CompletableFuture
                    .supplyAsync(() -> function.apply(next), executorService)
                    .exceptionally(e -> {
                        log.error("mapListAsync error", e);
                        return Collections.emptyList();
                    });
            futureList.add(task);
        }
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        futureList.forEach(f -> {
            List<S> pieceList = f.getNow(null);
            if (CollectionUtil.isNotEmpty(pieceList)) {
                totalList.addAll(pieceList);
            }
        });
        return totalList;
    }


    public void reset() {
        this.currIndex = 0;
    }
}
