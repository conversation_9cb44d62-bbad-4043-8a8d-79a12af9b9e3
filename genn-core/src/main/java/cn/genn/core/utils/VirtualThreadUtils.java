package cn.genn.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class VirtualThreadUtils {

    /**
     * 使用虚拟线程异步执行任务并获取结果
     *
     * @param supplier 任务提供者
     * @param <T>      返回结果类型
     * @return CompletableFuture封装的结果
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, Executors.newVirtualThreadPerTaskExecutor());
    }

    /**
     * 使用虚拟线程异步执行任务，无返回结果
     *
     * @param runnable 待执行的任务
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable) {
        return CompletableFuture.runAsync(runnable, Executors.newVirtualThreadPerTaskExecutor());
    }

    /**
     * 并行处理集合中的元素并收集结果
     *
     * @param collection 待处理的集合
     * @param processor  处理每个元素的函数
     * @param <T>        集合元素类型
     * @param <R>        处理结果类型
     * @return 处理结果的列表
     */
    public static <T, R> List<R> processInParallel(Collection<T> collection, Function<T, R> processor) {
        List<CompletableFuture<R>> futures = collection.stream()
                .map(item -> supplyAsync(() -> processor.apply(item)))
                .collect(Collectors.toList());

        return waitForAll(futures);
    }

    /**
     * 并行处理集合中的元素，处理过程可能抛出异常，并收集结果
     *
     * @param collection 待处理的集合
     * @param processor  可能抛出异常的处理函数
     * @param <T>        集合元素类型
     * @param <R>        处理结果类型
     * @param <E>        异常类型
     * @return 处理结果的列表，过滤掉了处理失败的元素
     */
    public static <T, R, E extends Exception> List<R> processInParallelWithExceptionHandling(
            Collection<T> collection, ThrowingFunction<T, R, E> processor) {
        List<CompletableFuture<R>> futures = collection.stream()
                .map(item -> supplyAsync(() -> {
                    try {
                        return processor.apply(item);
                    } catch (Exception e) {
                        log.warn("处理元素时发生异常: {}", item, e);
                        return null;
                    }
                }))
                .toList();

        return waitForAll(futures).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 等待所有CompletableFuture完成并收集结果
     *
     * @param futures CompletableFuture列表
     * @param <T>     结果类型
     * @return 结果列表
     */
    public static <T> List<T> waitForAll(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        return allFutures.thenApply(v -> futures.stream()
                        .map(future -> {
                            try {
                                return future.join();
                            } catch (Exception e) {
                                log.warn("获取异步结果时发生异常", e);
                                return null;
                            }
                        })
                        .collect(Collectors.toList()))
                .join();
    }

    /**
     * 批量执行Callable任务
     *
     * @param tasks Callable任务列表
     * @param <T>   返回结果类型
     * @return 所有任务的结果列表
     */
    public static <T> List<T> executeTasks(Collection<Callable<T>> tasks) {
        List<CompletableFuture<T>> futures = tasks.stream()
                .map(task -> supplyAsync(() -> {
                    try {
                        return task.call();
                    } catch (Exception e) {
                        log.warn("执行任务时发生异常", e);
                        throw new RuntimeException(e);
                    }
                }))
                .toList();

        return waitForAll(futures);
    }

    /**
     * 使用指定的超时时间执行异步任务
     *
     * @param supplier     任务提供者
     * @param timeout      超时时间(毫秒)
     * @param defaultValue 超时后的默认返回值
     * @param <T>          返回结果类型
     * @return 任务结果或超时后的默认值
     */
    public static <T> T executeWithTimeout(Supplier<T> supplier, long timeout, T defaultValue) {
        CompletableFuture<T> future = supplyAsync(supplier);
        try {
            return future.orTimeout(timeout, java.util.concurrent.TimeUnit.MILLISECONDS)
                    .exceptionally(ex -> defaultValue)
                    .join();
        } catch (Exception e) {
            log.warn("任务执行超时或发生异常", e);
            return defaultValue;
        }
    }

    /**
     * 分批并行处理大量数据
     *
     * @param collection 待处理的集合
     * @param processor  处理函数
     * @param batchSize  每批大小
     * @param <T>        集合元素类型
     * @param <R>        处理结果类型
     * @return 处理结果的列表
     */
    public static <T, R> List<R> processByBatch(Collection<T> collection, Function<T, R> processor, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        List<T> currentBatch = new ArrayList<>(batchSize);

        for (T item : collection) {
            currentBatch.add(item);
            if (currentBatch.size() >= batchSize) {
                batches.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
            }
        }

        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }

        List<CompletableFuture<List<R>>> batchFutures = batches.stream()
                .map(batch -> supplyAsync(() ->
                        batch.stream().map(processor).collect(Collectors.toList())
                ))
                .collect(Collectors.toList());

        return waitForAll(batchFutures).stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    /**
     * 带重试机制的异步任务执行
     *
     * @param supplier      任务提供者
     * @param maxRetries    最大重试次数
     * @param retryInterval 重试间隔(毫秒)
     * @param <T>           返回结果类型
     * @return CompletableFuture封装的结果
     */
    public static <T> CompletableFuture<T> supplyAsyncWithRetry(Supplier<T> supplier, int maxRetries, long retryInterval) {
        return supplyAsync(() -> {
            int attempts = 0;
            Exception lastException = null;

            while (attempts <= maxRetries) {
                try {
                    return supplier.get();
                } catch (Exception e) {
                    lastException = e;
                    attempts++;

                    if (attempts <= maxRetries) {
                        log.warn("任务执行失败，进行第{}次重试", attempts, e);
                        try {
                            Thread.sleep(retryInterval);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("重试过程被中断", ie);
                        }
                    }
                }
            }

            throw new RuntimeException("任务执行失败，已重试" + maxRetries + "次", lastException);
        });
    }

    /**
     * 按优先级执行任务
     *
     * @param tasks 任务及其优先级Map，值越小优先级越高
     * @param <T>   返回结果类型
     * @return 按任务完成顺序的结果列表
     */
    public static <T> List<T> executeByPriority(Map<Callable<T>, Integer> tasks) {
        List<Map.Entry<Callable<T>, Integer>> sortedTasks = tasks.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .toList();

        List<CompletableFuture<T>> futures = new ArrayList<>();

        for (Map.Entry<Callable<T>, Integer> entry : sortedTasks) {
            CompletableFuture<T> future = supplyAsync(() -> {
                try {
                    return entry.getKey().call();
                } catch (Exception e) {
                    log.warn("执行任务时发生异常", e);
                    throw new RuntimeException(e);
                }
            });
            futures.add(future);
        }

        return waitForAll(futures);
    }

    /**
     * 链式执行多个异步任务
     *
     * @param initialValue 初始值
     * @param functions    要依次执行的函数列表
     * @param <T>          数据类型
     * @return CompletableFuture封装的最终结果
     */
    public static <T> CompletableFuture<T> chainAsync(T initialValue, List<Function<T, T>> functions) {
        CompletableFuture<T> future = supplyAsync(() -> initialValue);

        for (Function<T, T> function : functions) {
            future = future.thenCompose(result -> supplyAsync(() -> function.apply(result)));
        }

        return future;
    }

    /**
     * 组合多个异步任务的结果
     *
     * @param futures  CompletableFuture列表
     * @param combiner 组合函数
     * @param <T>      输入结果类型
     * @param <R>      输出结果类型
     * @return 组合后的结果
     */
    public static <T, R> CompletableFuture<R> combine(List<CompletableFuture<T>> futures, Function<List<T>, R> combiner) {
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        return allFutures.thenApply(v ->
                combiner.apply(futures.stream().map(CompletableFuture::join).collect(Collectors.toList()))
        );
    }

    /**
     * 带超时的批量任务执行
     *
     * @param tasks        待执行的任务列表
     * @param timeout      超时时间(毫秒)
     * @param timeoutValue 超时后的默认返回值
     * @param <T>          返回结果类型
     * @return 处理结果列表，超时的任务将返回默认值
     */
    public static <T> List<T> executeTasksWithTimeout(Collection<Callable<T>> tasks, long timeout, T timeoutValue) {
        List<CompletableFuture<T>> futures = tasks.stream()
                .map(task -> supplyAsync(() -> {
                    try {
                        return task.call();
                    } catch (Exception e) {
                        log.warn("执行任务时发生异常", e);
                        throw new RuntimeException(e);
                    }
                }).orTimeout(timeout, java.util.concurrent.TimeUnit.MILLISECONDS)
                        .exceptionally(ex -> timeoutValue))
                .collect(Collectors.toList());

        return waitForAll(futures);
    }

    /**
     * 带有恢复策略的异步任务执行
     *
     * @param supplier        主要任务提供者
     * @param exceptionMapper 异常处理和恢复函数
     * @param <T>             返回结果类型
     * @return CompletableFuture封装的结果
     */
    public static <T> CompletableFuture<T> supplyAsyncWithRecovery(Supplier<T> supplier, Function<Throwable, T> exceptionMapper) {
        return supplyAsync(supplier).exceptionally(exceptionMapper);
    }

    /**
     * 根据异常类型执行不同恢复策略
     *
     * @param supplier           主要任务提供者
     * @param exceptionHandlers  异常处理器Map，键为异常类，值为对应的恢复处理函数
     * @param defaultHandler     默认异常处理函数
     * @param <T>                返回结果类型
     * @return CompletableFuture封装的结果
     */
    public static <T> CompletableFuture<T> supplyAsyncWithTypedRecovery(
            Supplier<T> supplier,
            Map<Class<? extends Throwable>, Function<Throwable, T>> exceptionHandlers,
            Function<Throwable, T> defaultHandler) {

        return supplyAsync(supplier).exceptionally(ex -> {
            Throwable cause = ex.getCause() != null ? ex.getCause() : ex;

            for (Map.Entry<Class<? extends Throwable>, Function<Throwable, T>> entry : exceptionHandlers.entrySet()) {
                if (entry.getKey().isInstance(cause)) {
                    return entry.getValue().apply(cause);
                }
            }

            return defaultHandler.apply(cause);
        });
    }

    /**
     * 带并发限制的批量任务执行
     *
     * @param tasks       任务列表
     * @param maxParallel 最大并发数
     * @param <T>         返回结果类型
     * @return 所有任务的结果列表
     */
    public static <T> List<T> executeTasksWithConcurrencyLimit(Collection<Callable<T>> tasks, int maxParallel) {
        List<List<Callable<T>>> batches = new ArrayList<>();
        List<Callable<T>> currentBatch = new ArrayList<>(maxParallel);

        for (Callable<T> task : tasks) {
            currentBatch.add(task);
            if (currentBatch.size() >= maxParallel) {
                batches.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
            }
        }

        if (!currentBatch.isEmpty()) {
            batches.add(currentBatch);
        }

        List<T> results = new ArrayList<>();
        for (List<Callable<T>> batch : batches) {
            List<T> batchResults = executeTasks(batch);
            results.addAll(batchResults);
        }

        return results;
    }

    /**
     * 周期性执行任务
     *
     * @param task     要执行的任务
     * @param interval 执行间隔(毫秒)
     * @return 用于取消任务的Runnable
     */
    public static Runnable scheduleAtFixedRate(Runnable task, long interval) {
        AtomicBoolean running = new AtomicBoolean(true);

        runAsync(() -> {
            while (running.get()) {
                try {
                    task.run();
                    Thread.sleep(interval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    running.set(false);
                } catch (Exception e) {
                    log.error("周期任务执行异常", e);
                }
            }
        });

        return () -> running.set(false);
    }

    /**
     * 等待条件满足后执行任务
     *
     * @param condition   条件判断函数
     * @param task        待执行的任务
     * @param maxWaitTime 最大等待时间(毫秒)
     * @param checkInterval 检查间隔(毫秒)
     * @param <T>         返回结果类型
     * @return CompletableFuture封装的结果
     */
    public static <T> CompletableFuture<T> waitAndExecute(BooleanSupplier condition, Supplier<T> task, long maxWaitTime, long checkInterval) {
        return supplyAsync(() -> {
            long startTime = System.currentTimeMillis();

            while (!condition.getAsBoolean()) {
                if (System.currentTimeMillis() - startTime > maxWaitTime) {
                    throw new RuntimeException("等待条件满足超时");
                }

                try {
                    Thread.sleep(checkInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("等待过程被中断", e);
                }
            }

            return task.get();
        });
    }

    /**
     * 批量执行不带返回值的任务
     *
     * @param tasks 要执行的任务列表
     * @return CompletableFuture<Void>，所有任务完成时完成
     */
    public static CompletableFuture<Void> runTasks(Collection<Runnable> tasks) {
        List<CompletableFuture<Void>> futures = tasks.stream()
                .map(VirtualThreadUtils::runAsync)
                .toList();

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }

    /**
     * 按顺序执行异步任务
     *
     * @param tasks 要按顺序执行的任务列表
     * @return CompletableFuture<Void>，所有任务完成时完成
     */
    public static CompletableFuture<Void> runSequentially(List<Runnable> tasks) {
        CompletableFuture<Void> future = CompletableFuture.completedFuture(null);

        for (Runnable task : tasks) {
            future = future.thenCompose(v -> runAsync(task));
        }

        return future;
    }

    /**
     * 分组执行不同优先级的任务，高优先级任务先执行完毕后再执行低优先级任务
     *
     * @param highPriorityTasks 高优先级任务
     * @param lowPriorityTasks  低优先级任务
     * @return CompletableFuture<Void>，所有任务完成时完成
     */
    public static CompletableFuture<Void> runTasksByPriority(
            Collection<Runnable> highPriorityTasks, Collection<Runnable> lowPriorityTasks) {
        CompletableFuture<Void> highPriorityFuture = runTasks(highPriorityTasks);
        return highPriorityFuture.thenCompose(v -> runTasks(lowPriorityTasks));
    }

    /**
     * 带取消功能的批量任务执行
     *
     * @param tasks 要执行的任务列表
     * @return 可取消的批处理控制器
     */
    public static BatchController executeBatchWithCancellation(Collection<Runnable> tasks) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        AtomicBoolean isCancelled = new AtomicBoolean(false);

        for (Runnable task : tasks) {
            if (isCancelled.get()) {
                break;
            }

            CompletableFuture<Void> future = runAsync(() -> {
                if (!isCancelled.get()) {
                    task.run();
                }
            });

            futures.add(future);
        }

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        return new BatchController() {
            @Override
            public void cancel() {
                isCancelled.set(true);
            }

            @Override
            public CompletableFuture<Void> getCompletionFuture() {
                return allFutures;
            }
        };
    }
    /**
     * 创建分批执行控制器
     *
     * @param batchSize 每批执行的任务数量
     * @return 分批执行控制器
     */
    public static BatchExecutor createBatchExecutor(int batchSize) {
        return new BatchExecutorImpl(batchSize);
    }

    /**
     * 带进度监控的批量任务执行
     *
     * @param tasks          要执行的任务列表
     * @param progressCallback 进度回调函数，接收完成百分比作为参数
     * @return CompletableFuture<Void>，所有任务完成时完成
     */
    public static CompletableFuture<Void> runTasksWithProgress(
            Collection<Runnable> tasks, Consumer<Double> progressCallback) {
        int totalTasks = tasks.size();
        AtomicInteger completedTasks = new AtomicInteger(0);

        List<CompletableFuture<Void>> futures = tasks.stream()
                .map(task -> runAsync(() -> {
                    try {
                        task.run();
                    } finally {
                        int completed = completedTasks.incrementAndGet();
                        double progress = (double) completed / totalTasks * 100.0;
                        progressCallback.accept(progress);
                    }
                }))
                .toList();

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }

    /**
     * 带结果通知的批量任务执行
     *
     * @param tasks           要执行的任务列表
     * @param successCallback 成功回调
     * @param failureCallback 失败回调
     * @return CompletableFuture<Void>，所有任务完成时完成
     */
    public static CompletableFuture<Void> runTasksWithCallbacks(
            Collection<Runnable> tasks,
            Runnable successCallback,
            Consumer<Throwable> failureCallback) {
        List<CompletableFuture<Void>> futures = tasks.stream()
                .map(VirtualThreadUtils::runAsync)
                .toList();

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(successCallback)
                .exceptionally(ex -> {
                    failureCallback.accept(ex);
                    return null;
                });
    }

    /**
     * 创建支持任务依赖关系的执行器
     *
     * @return 依赖关系执行器
     */
    public static DependencyExecutor createDependencyExecutor() {
        return new DependencyExecutorImpl();
    }

    /**
     * 依赖关系执行器接口
     */
    public interface DependencyExecutor {
        /**
         * 添加任务
         *
         * @param taskId       任务ID
         * @param task         要执行的任务
         * @param dependencies 依赖的任务ID列表
         */
        void addTask(String taskId, Runnable task, String... dependencies);

        /**
         * 执行所有任务，尊重依赖关系
         *
         * @return CompletableFuture<Void>，所有任务完成时完成
         */
        CompletableFuture<Void> execute();
    }

    /**
     * 依赖关系执行器实现
     */
    private static class DependencyExecutorImpl implements DependencyExecutor {
        private final Map<String, Runnable> tasks = new HashMap<>();
        private final Map<String, Set<String>> dependencies = new HashMap<>();
        private final Map<String, Set<String>> dependents = new HashMap<>();

        @Override
        public void addTask(String taskId, Runnable task, String... dependencies) {
            tasks.put(taskId, task);
            Set<String> deps = new HashSet<>(Arrays.asList(dependencies));
            this.dependencies.put(taskId, deps);

            // 更新反向依赖关系图
            for (String dep : deps) {
                this.dependents.computeIfAbsent(dep, k -> new HashSet<>()).add(taskId);
            }
        }

        @Override
        public CompletableFuture<Void> execute() {
            // 找出所有无依赖的任务
            Set<String> readyTasks = tasks.keySet().stream()
                    .filter(taskId -> dependencies.getOrDefault(taskId, Collections.emptySet()).isEmpty())
                    .collect(Collectors.toSet());

            // 创建完成的任务集合
            Set<String> completedTasks = Collections.synchronizedSet(new HashSet<>());

            // 创建最终结果
            CompletableFuture<Void> result = new CompletableFuture<>();

            // 执行任务的函数
            BiConsumer<String, Set<String>> executeTask = new BiConsumer<>() {
                @Override
                public void accept(String taskId, Set<String> readyTasksRef) {
                    runAsync(() -> {
                        try {
                            tasks.get(taskId).run();

                            // 标记任务为已完成
                            completedTasks.add(taskId);

                            // 检查是否所有任务都已完成
                            if (completedTasks.size() == tasks.size()) {
                                result.complete(null);
                                return;
                            }

                            // 找出现在可以执行的依赖任务
                            Set<String> dependentTasks = dependents.getOrDefault(taskId, Collections.emptySet());
                            for (String depTask : dependentTasks) {
                                // 检查该任务的所有依赖是否都已完成
                                Set<String> taskDeps = dependencies.get(depTask);
                                if (completedTasks.containsAll(taskDeps)) {
                                    // 所有依赖都已完成，可以执行此任务
                                    this.accept(depTask, readyTasksRef);
                                }
                            }
                        } catch (Exception e) {
                            result.completeExceptionally(e);
                        }
                    });
                }
            };

            // 开始执行无依赖的任务
            for (String taskId : readyTasks) {
                executeTask.accept(taskId, readyTasks);
            }

            return result;
        }
    }



    /**
     * 分批执行控制器接口
     */
    public interface BatchExecutor {
        /**
         * 添加要执行的任务
         *
         * @param task 任务
         */
        void addTask(Runnable task);

        /**
         * 开始执行所有任务，并在所有任务完成后通知
         *
         * @return 所有任务完成时完成的CompletableFuture
         */
        CompletableFuture<Void> execute();

        /**
         * 取消尚未执行的任务
         */
        void cancelRemainingTasks();
    }

    /**
     * 分批执行控制器实现
     */
    private static class BatchExecutorImpl implements BatchExecutor {
        private final int batchSize;
        private final List<Runnable> tasks = new ArrayList<>();
        private final AtomicBoolean isCancelled = new AtomicBoolean(false);

        public BatchExecutorImpl(int batchSize) {
            this.batchSize = batchSize;
        }

        @Override
        public void addTask(Runnable task) {
            tasks.add(task);
        }

        @Override
        public CompletableFuture<Void> execute() {
            List<List<Runnable>> batches = new ArrayList<>();
            List<Runnable> currentBatch = new ArrayList<>(batchSize);

            for (Runnable task : tasks) {
                currentBatch.add(task);
                if (currentBatch.size() >= batchSize) {
                    batches.add(new ArrayList<>(currentBatch));
                    currentBatch.clear();
                }
            }

            if (!currentBatch.isEmpty()) {
                batches.add(currentBatch);
            }

            CompletableFuture<Void> result = CompletableFuture.completedFuture(null);

            for (List<Runnable> batch : batches) {
                result = result.thenCompose(v -> {
                    if (isCancelled.get()) {
                        return CompletableFuture.completedFuture(null);
                    }

                    List<CompletableFuture<Void>> batchFutures = batch.stream()
                            .map(task -> runAsync(() -> {
                                if (!isCancelled.get()) {
                                    task.run();
                                }
                            }))
                            .collect(Collectors.toList());

                    return CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0]));
                });
            }

            return result;
        }

        @Override
        public void cancelRemainingTasks() {
            isCancelled.set(true);
        }
    }


    /**
     * 批处理控制器接口
     */
    public interface BatchController {
        void cancel();
        CompletableFuture<Void> getCompletionFuture();
    }


    /**
     * 可以抛出异常的函数接口
     */
    @FunctionalInterface
    public interface ThrowingFunction<T, R, E extends Exception> {
        R apply(T t) throws E;
    }
}