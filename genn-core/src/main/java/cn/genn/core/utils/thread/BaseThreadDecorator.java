package cn.genn.core.utils.thread;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 */
public abstract class BaseThreadDecorator implements ThreadDecorator {

    private final ThreadDecorator threadDecorator;

    protected BaseThreadDecorator() {
        this(null);
    }

    protected BaseThreadDecorator(ThreadDecorator threadDecorator) {
        this.threadDecorator = threadDecorator;
    }

    @Override
    public Runnable decorate(Runnable runnable) {
        Runnable decorated = threadDecorator != null ? threadDecorator.decorate(runnable) : runnable;
        Object object = beforeExecOnCurrThread();
        return () -> {
            try {
                doOnNewThread(object);
                decorated.run();
            } finally {
                afterExecOnNewThread();
            }
        };
    }

    @Override
    public <T> Callable<T> decorate(Callable<T> task) {
        Callable<T> decorated = threadDecorator != null ? threadDecorator.decorate(task) : task;
        Object object = beforeExecOnCurrThread();
        return () -> {
            try {
                doOnNewThread(object);
                return decorated.call();
            } finally {
                afterExecOnNewThread();
            }
        };
    }

    protected abstract Object beforeExecOnCurrThread();

    protected abstract void doOnNewThread(Object object);

    protected abstract void afterExecOnNewThread();
}
