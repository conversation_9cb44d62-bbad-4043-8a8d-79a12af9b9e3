package cn.genn.core.utils;

import cn.genn.core.exception.MessageCodeWrap;
import cn.genn.core.exception.ProductExEnum;
import cn.genn.core.exception.ServiceExEnum;
import cn.genn.core.model.KVStruct;
import cn.genn.core.model.page.PageResultDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * 至简相关工具类
 *
 * <AUTHOR>
 */
public class GennUtils {

    /**
     * 获取当前产品和服务端
     *
     * @return
     */
    public static KVStruct<ProductExEnum, ServiceExEnum> getCurrService() {
        String productCode = SpringUtil.getProperty("genn.spring.web.exception.product-code");
        String serviceCode = SpringUtil.getProperty("genn.spring.web.exception.service-code");
        return new KVStruct<>(ProductExEnum.getByCode(productCode), ServiceExEnum.getByCode(serviceCode));
    }

    /**
     * 获取9位异常码
     * @param messageCodeWrap 异常枚举定义
     * @return
     */
    public static String getExCode(MessageCodeWrap messageCodeWrap) {
        KVStruct<ProductExEnum, ServiceExEnum> currService = getCurrService();
        return currService.getKey().getCode() + currService.getValue().getCode() + messageCodeWrap.buildCode();
    }

    /**
     * 判断异常枚举和异常码是否相等
     * @param messageCodeWrap 异常枚举定义
     * @param code 9位完整的异常码
     * @return
     */
    public static boolean equalsEx(MessageCodeWrap messageCodeWrap, String code) {
        return getExCode(messageCodeWrap).equals(code);
    }

    /**
     * 判断分页结果是否为空
     */
    public static boolean pageIsEmpty(PageResultDTO<?> pageResultDTO) {
        return pageResultDTO == null || CollUtil.isEmpty(pageResultDTO.getList());
    }
}
