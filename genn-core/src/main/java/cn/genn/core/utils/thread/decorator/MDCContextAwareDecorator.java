package cn.genn.core.utils.thread.decorator;

import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;
import org.slf4j.MDC;

import java.util.Map;

/**
 * 线程装饰器, 用于在线程池中传递MDC上下文
 *
 * <AUTHOR>
 */
public class MDCContextAwareDecorator extends BaseThreadDecorator {

    public MDCContextAwareDecorator() {
        super();
    }

    public MDCContextAwareDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }

    @Override
    protected Object beforeExecOnCurrThread() {
        return MDC.getCopyOfContextMap();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof Map) {
            MDC.setContextMap((Map<String, String>) object);
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        MDC.clear();
    }
}
