package cn.genn.core.utils.query.model;

import lombok.Getter;

import java.util.Collection;
import java.util.function.BiPredicate;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
@Getter
public enum MemoryQueryType {

    EQ("eq", "等于", Object::equals),
    NOT_EQ("neq", "不等于", (value, target) -> value == null || !value.equals(target)),
    LIKE("like", "字符串包含", (value, target) -> value != null && ((String) value).contains((String) target)),
    LIKE_LEFT("like_left", "字符串左包含", (value, target) -> value != null && ((String) value).startsWith((String) target)),
    LIKE_RIGHT("like_right", "字符串右包含", (value, target) -> value != null && ((String) value).endsWith((String) target)),
    NOT_LIKE("not_like", "字符串不包含", (value, target) -> value == null || !((String) value).contains((String) target)),
    IN("in", "集合包含", (value, target) -> value != null && ((Collection<?>) value).contains(target)),
    NOT_IN("not_in", "集合不包含", (value, target) -> value == null || !((Collection<?>) value).contains(target)),
    GT("gt", "大于", (value, target) -> value != null && ((Comparable) value).compareTo(target) > 0),
    GE("ge", "大于等于", (value, target) -> value != null && ((Comparable) value).compareTo(target) >= 0),
    LT("lt", "小于", (value, target) -> value != null && ((Comparable) value).compareTo(target) < 0),
    LE("le", "小于等于", (value, target) -> value != null && ((Comparable) value).compareTo(target) <= 0),
    IS_NULL("is_null", "为空", (value, target) -> value == null),
    NOT_NULL("not_null", "不为空", (value, target) -> value != null);


    private final String code;
    private final String description;

    @Getter
    private final BiPredicate<Object, Object> predicate;

    MemoryQueryType(String code, String description, BiPredicate<Object, Object> predicate) {
        this.code = code;
        this.description = description;
        this.predicate = predicate;
    }

    /**
     * 判断是否满足条件
     * @param value 需要判断的值,在列表中需要过滤的数据
     * @param target 查询条件传入的值
     * @return
     */
    public boolean test(Object value, Object target) {
        return this.getPredicate().test(value, target);
    }
}
