package cn.genn.core.utils.jackson;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;

import java.io.IOException;
import java.util.Date;

/**
 * 使用hutool支持各种自定义日期类型
 * <AUTHOR>
 */
public class CustomDateDeserializer extends DateDeserializers.DateDeserializer {

    @Override
    protected Date _parseDate(JsonParser jp, DeserializationContext context) throws IOException {
        return DateUtil.parse(jp.getText());
    }

}
