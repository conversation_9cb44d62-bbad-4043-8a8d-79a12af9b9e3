package cn.genn.core.utils.code.biz;

import cn.genn.core.context.BaseRequestContext;
import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.utils.ConvertUtils;
import cn.genn.core.utils.code.NanoIdUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 业务单号生成工具
 *
 * <AUTHOR>
 */
@Slf4j
public class BizCodeGenUtils {

    private BizCodeGenUtils() {
    }


    /**
     * 使用自增算法 按日维度 生成业务单编号
     * 业务单号规则：业务类型(2位)+租户id(3位)+年月日(8位)+自增序列
     * 从上下文获取租户id
     *
     * @param bizType
     * @param codeLen
     * @return
     */
    public static String generate(String bizType, int codeLen) {
        Long tenantId = BaseRequestContext.get().getTenantId();
        if (tenantId == null) {
            throw new BaseException(CommonCode.METHOD_ARGUMENT_NOT_VALID, "获取租户id失败！");
        }
        return generate(bizType, StrUtil.fillBefore(String.valueOf(tenantId), '0', 3), CycleFormatEnum.YY_MM_DD, codeLen, RandomCodeRule.AUTO_INCREMENT, null);
    }

    public static String generate(String bizType, int codeLen, String suffix) {
        Long tenantId = BaseRequestContext.get().getTenantId();
        if (tenantId == null) {
            throw new BaseException(CommonCode.METHOD_ARGUMENT_NOT_VALID, "获取租户id失败！");
        }
        return generate(bizType, StrUtil.fillBefore(String.valueOf(tenantId), '0', 3), CycleFormatEnum.YY_MM_DD, codeLen, RandomCodeRule.AUTO_INCREMENT, suffix);
    }

    /**
     * 使用自增算法 按日维度 生成业务单编号
     *
     * @param bizType  业务类型,前缀,一般是2位
     * @param tenantId 租户id
     * @param codeLen  随机码长度
     * @return
     */
    public static String generate(String bizType, String tenantId, int codeLen) {
        return generate(bizType, tenantId, CycleFormatEnum.YY_MM_DD, codeLen, RandomCodeRule.AUTO_INCREMENT, null);
    }

    /**
     * 生成业务单编号
     *
     * @param bizType         业务类型
     * @param bizRuleCode     业务规则码
     * @param cycleFormatEnum 时间格式
     * @param codeLen         随机码长度
     * @param randomCodeRule  随机码生成规则
     * @return
     */
    public static String generate(String bizType, String tenantId,
                                  CycleFormatEnum cycleFormatEnum, int codeLen,
                                  RandomCodeRule randomCodeRule, String suffix) {
        return generate(bizType, tenantId, cycleFormatEnum, () -> {
            String randomCode;
            switch (randomCodeRule) {
                case LONG_TO_BASE62:
                case AUTO_INCREMENT:
                    randomCode = generateWithRedis(bizType, tenantId, cycleFormatEnum, codeLen, randomCodeRule);
                    break;
                case NANOID:
                default:
                    randomCode = NanoIdUtils.randomNanoId(codeLen);
                    break;
            }
            return randomCode;
        }, suffix);
    }

    public static String generate(String bizType,
                                  String tenantId,
                                  CycleFormatEnum cycleFormatEnum,
                                  Supplier<String> randomSupplier, String suffix) {
        StringBuilder sb = new StringBuilder();
        sb.append(bizType) //2位
                .append(tenantId)
                .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern(cycleFormatEnum.getFormat()))) //8位
                .append(randomSupplier.get());
        if (suffix != null) {
            sb.append(suffix);
        }
        return sb.toString();
    }


    private static String generateWithRedis(String bizType, String tenantId, CycleFormatEnum cycleFormat,
                                            int codeLen, RandomCodeRule randomCodeRule) {
        StringRedisTemplate stringRedisTemplate = null;
        try {
            stringRedisTemplate = SpringUtil.getBean("stringRedisTemplate");
        } catch (Exception ignored) {
            log.error(ignored.getMessage(), ignored);
        }
        if (stringRedisTemplate == null) {
            log.error("获取stringRedisTemplate失败！");
            throw new BaseException(CommonCode.FAIL, "获取stringRedisTemplate失败！");
        }
        String cycleKey = DateFormatUtils.format(new Date(), cycleFormat.getKey());
        String cacheKey = getCacheKey(bizType, tenantId, cycleKey);
        RedisAtomicInteger counter = new RedisAtomicInteger(cacheKey, stringRedisTemplate.getConnectionFactory());
        counter.expire(cycleFormat.getLife(), TimeUnit.SECONDS);
        int codeSeq = counter.incrementAndGet();
        switch (randomCodeRule) {
            case LONG_TO_BASE62:
                return ConvertUtils.longToBase62(codeSeq, codeLen);
            case AUTO_INCREMENT:
                return StrUtil.fillBefore(String.valueOf(codeSeq), '0', codeLen);
            default:
                throw new BaseException(CommonCode.FAIL, "不支持的随机码生成规则");
        }
    }


    private static String getCacheKey(String bizType, String tenantId, String cycleKey) {
        return "genn:code:" + bizType + ":" + tenantId + ":" + cycleKey;
    }
}
