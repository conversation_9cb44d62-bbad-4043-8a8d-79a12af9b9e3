package cn.genn.core.utils;

/**
 * <AUTHOR>
 */
public class ExceptionUtils {

    /**
     * 获取异常信息
     * @param throwable 异常
     * @param maxLength 最大长度
     * @return
     */
    public static String getErrorMsg(Throwable throwable, int maxLength) {
        if (maxLength == 0) {
            maxLength = 900;
        }
        if (throwable == null) {
            return null;
        }
        StringBuilder errorMsg = new StringBuilder();
        errorMsg.append(throwable);
        if (throwable.getStackTrace() != null && throwable.getStackTrace().length > 0) {
            StackTraceElement element = throwable.getStackTrace()[0];
            errorMsg.append(" (at ").append(element.getClassName()).append(".").append(element.getMethodName()).append(":").append(element.getLineNumber()).append(")");
        }
        if (errorMsg.length() > maxLength) {
            return errorMsg.substring(0, maxLength);
        }
        return errorMsg.toString();
    }

    public static String getErrorPosition(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        if (throwable.getStackTrace() != null && throwable.getStackTrace().length > 0) {
            StackTraceElement element = throwable.getStackTrace()[0];
            if (element == null) {
                return null;
            }
            return element.getClassName() + "." + element.getMethodName() + ":" + element.getLineNumber();
        }
        return null;
    }

    /**
     * 获取异常堆栈中第一个属于用户代码的元素位置信息。
     * @param e 异常对象
     * @param packageNamePrefix 用户代码的包名前缀 (如 "cn.genn")
     * @return 返回第一个符合条件的堆栈元素信息。如果找不到符合条件的元素，返回堆栈的第一个元素信息。
     */
    public static String getUserCodeLocation(Throwable e, String packageNamePrefix) {
        return getUserCodeLocation(e, packageNamePrefix, null);
    }


    /**
     * 获取异常堆栈中第一个属于用户代码的元素位置信息。
     * @param e 异常对象
     * @param packageNamePrefix 用户代码的包名前缀 (如 "cn.genn")
     * @param excludePackageNamePrefix 需要排除的包名前缀
     * @return 返回第一个符合条件的堆栈元素信息。如果找不到符合条件的元素，返回堆栈的第一个元素信息。
     */
    public static String getUserCodeLocation(Throwable e, String packageNamePrefix, String excludePackageNamePrefix) {
        StackTraceElement[] stackTrace = e.getStackTrace();
        String exceptionKey = null;

        for (StackTraceElement element : stackTrace) {
            String className = element.getClassName();
            // 通过判断类名，过滤掉非用户项目的类
            if (className.startsWith(packageNamePrefix) && (excludePackageNamePrefix == null || !className.startsWith(excludePackageNamePrefix))) {
                exceptionKey = String.format("%s.%s:%d",
                        element.getClassName(),
                        element.getMethodName(),
                        element.getLineNumber()
                );
                break; // 找到第一个符合条件的元素后退出循环
            }
        }
        if (exceptionKey == null && stackTrace.length > 0) {
            // 如果找不到项目内的代码，退而求其次使用第一个元素
            StackTraceElement firstElement = stackTrace[0];
            exceptionKey = String.format("%s.%s:%d",
                    firstElement.getClassName(),
                    firstElement.getMethodName(),
                    firstElement.getLineNumber()
            );
        }
        return exceptionKey;
    }

}
