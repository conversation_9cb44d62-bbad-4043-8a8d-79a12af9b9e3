package cn.genn.core.utils.query.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MemoryPageEntity {

    private int page;

    private int pageSize;

    public static MemoryPageEntity build(int page, int pageSize) {
        MemoryPageEntity pageEntity = new MemoryPageEntity();
        pageEntity.setPage(page);
        pageEntity.setPageSize(pageSize);
        return pageEntity;
    }
}
