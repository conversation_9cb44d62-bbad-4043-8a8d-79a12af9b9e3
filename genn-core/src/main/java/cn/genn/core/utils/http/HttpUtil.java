package cn.genn.core.utils.http;

import cn.genn.core.utils.http.model.HttpResponse;
import cn.genn.core.utils.http.model.NamedInputStream;
import cn.hutool.core.map.MapUtil;
import org.springframework.http.MediaType;

import java.io.File;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface HttpUtil {

    default HttpResponse get(String url) {
        return get(url, new HashMap<>());
    }

    default HttpResponse get(String url, Map<String, String> params) {
        return get(url, new HashMap<>(), params);
    }

    HttpResponse get(String url, Map<String, String> headers, Map<String, String> params);

    default HttpResponse postForm(String url, Map<String, String> formData) {
        return postForm(url, new HashMap<>(), new HashMap<>(), formData);
    }

    default HttpResponse postForm(String url, Map<String, String> params, Map<String, String> formData) {
        return postForm(url, new HashMap<>(), params, formData);
    }

    HttpResponse postForm(String url, Map<String, String> headers, Map<String, String> params, Map<String, String> formData);

    default HttpResponse postFile(String url, String name, File file) {
        return postFile(url, new HashMap<>(), MapUtil.of(name, file));
    }

    default HttpResponse postFile(String url, Map<String, File> files) {
        return postFile(url, new HashMap<>(), files);
    }

    default HttpResponse postFile(String url, Map<String, String> formData, Map<String, File> files) {
        return postFile(url, new HashMap<>(), new HashMap<>(), formData, files);
    }

    HttpResponse postFile(String url, Map<String, String> headers, Map<String, String> params, Map<String, String> formData, Map<String, File> files);

    default HttpResponse postInputStream(String url, String name, String filename, InputStream inputStream) {
        return postInputStream(url, MapUtil.of(name, new NamedInputStream(filename, inputStream)));
    }

    default HttpResponse postInputStream(String url, Map<String, NamedInputStream> inputStreams) {
        return postInputStream(url, new HashMap<>(), inputStreams);
    }

    default HttpResponse postInputStream(String url, Map<String, String> formData, Map<String, NamedInputStream> inputStreams) {
        return postInputStream(url, new HashMap<>(), new HashMap<>(), formData, inputStreams);
    }

    HttpResponse postInputStream(String url, Map<String, String> headers, Map<String, String> params, Map<String, String> formData, Map<String, NamedInputStream> files);

    default HttpResponse postJson(String url, String json) {
        return postJson(url, new HashMap<>(), new HashMap<>(), json);
    }

    default HttpResponse postJson(String url, Map<String, String> params, String json) {
        return postJson(url, new HashMap<>(), params, json);
    }

    default HttpResponse postJson(String url, Map<String, String> headers, Map<String, String> params, String json) {
        return postBody(url, headers, params, MediaType.APPLICATION_JSON, json);
    }

    default HttpResponse postXml(String url, String xml) {
        return postXml(url, new HashMap<>(), new HashMap<>(), xml);
    }

    default HttpResponse postXml(String url, Map<String, String> params, String xml) {
        return postXml(url, new HashMap<>(), params, xml);
    }

    default HttpResponse postXml(String url, Map<String, String> headers, Map<String, String> params, String xml) {
        return postBody(url, headers, params, MediaType.APPLICATION_XML, xml);
    }

    default HttpResponse postBody(String url, MediaType contentType, String content) {
        return postBody(url, new HashMap<>(), contentType, content);
    }

    default HttpResponse postBody(String url, Map<String, String> params, MediaType contentType, String content) {
        return postBody(url, new HashMap<>(), params, contentType, content);
    }

    HttpResponse postBody(String url, Map<String, String> headers, Map<String, String> params, MediaType contentType, String content);


}
