package cn.genn.core.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * 由于Java的简单类型不能够精确的对浮点数进行运算，这个工具类提供精
 * 确的浮点数运算，包括加减乘除和四舍五入。
 *
 * <AUTHOR>
 */
public class MathUtils {

    //默认除法运算精度
    private static final int DEF_DIV_SCALE = 10;

    //这个类不能实例化
    private MathUtils() {
    }

    /**
     * 提供精确的加法运算。
     *
     * @param vals 加数
     * @return 参数的和
     */
    public static double add(double... vals) {
        if(vals.length < 2) {
            throw new IllegalArgumentException("at least two parameters");
        }
        BigDecimal result = new BigDecimal(Double.toString(vals[0]));
        for (int i = 1; i < vals.length; i++) {
            result = result.add(new BigDecimal(Double.toString(vals[i])));
        }
        return result.doubleValue();
    }

    /**
     * 提供精确的减法运算。
     *
     * @param vals 减数
     * @return 参数的差
     */
    public static double sub(double... vals) {
        if(vals.length < 2) {
            throw new IllegalArgumentException("at least two parameters");
        }
        BigDecimal result = new BigDecimal(Double.toString(vals[0]));
        for (int i = 1; i < vals.length; i++) {
            result = result.subtract(new BigDecimal(Double.toString(vals[i])));
        }
        return result.doubleValue();
    }

    /**
     * 提供精确的乘法运算。
     *
     * @param vals 乘数
     * @return 两个参数的积
     */
    public static double mul(double... vals) {
        if(vals.length < 2) {
            throw new IllegalArgumentException("at least two parameters");
        }
        BigDecimal result = new BigDecimal(Double.toString(vals[0]));
        for (int i = 1; i < vals.length; i++) {
            result = result.multiply(new BigDecimal(Double.toString(vals[i])));
        }
        return result.doubleValue();
    }

    /**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到
     * 小数点以后10位，以后的数字四舍五入。
     *
     * @param v1 被除数
     * @param v2 除数
     * @return 两个参数的商
     */
    public static double div(double v1, double v2) {
        return div(v1, v2, DEF_DIV_SCALE, false);
    }

    /**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到
     * 小数点以后10位，以后的数字四舍五入。
     *
     * @param v1 被除数
     * @param v2 除数
     * @param safe if v2 == 0 and safe == true then set v2 = 1
     * @return 两个参数的商
     */
    public static double div(double v1, double v2, boolean safe) {
        return div(v1, v2, DEF_DIV_SCALE, safe);
    }

    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
     * 定精度，以后的数字四舍五入。
     *
     * @param v1    被除数
     * @param v2    除数
     * @param scale 表示表示需要精确到小数点以后几位。
     * @param safe if v2 == 0 and safe == true then set v2 = 1
     * @return 两个参数的商
     */
    public static double div(double v1, double v2, int scale, boolean safe) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        if(safe && v2 == 0) {
            v2 = 1;
        }
        BigDecimal b1 = new BigDecimal(Double.toString(v1));
        BigDecimal b2 = new BigDecimal(Double.toString(v2));
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 提供精确的小数位四舍五入处理，保留2位小数。
     *
     * @param v     需要四舍五入的数字
     * @return 四舍五入后的结果
     */
    public static double round(double v) {
        return round(v, 2);
    }

    /**
     * 提供精确的小数位四舍五入处理。
     *
     * @param v     需要四舍五入的数字
     * @param scale 小数点后保留几位
     * @return 四舍五入后的结果
     */
    public static double round(double v, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b = new BigDecimal(Double.toString(v));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 格式化
     *
     * @param v
     * @param pattern
     * @return
     */
    public static String format(double v, String pattern) {
        if (pattern == null || "".equals(pattern)) {
            pattern = "#";
        }
        DecimalFormat format = new DecimalFormat(pattern);
        return format.format(new BigDecimal(Double.toString(v)));
    }
}
