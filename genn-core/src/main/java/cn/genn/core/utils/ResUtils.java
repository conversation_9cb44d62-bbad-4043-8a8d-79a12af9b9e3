package cn.genn.core.utils;

import cn.genn.core.exception.*;
import cn.genn.core.model.res.ResponseResult;
import lombok.Setter;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.slf4j.MDC;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class ResUtils {

    /**
     * 指定trace提供者
     */
    @Setter
    private static Supplier<String> traceIdSupplier = () -> {
        String traceId = MDC.get("traceId");
        if (traceId == null) {
            traceId = MDC.get("tid");
        }
        if (traceId == null) {
            traceId = TraceContext.traceId();
        }
        return traceId;
    };

    /**
     * 成功返回
     */
    public static <E> ResponseResult<E> ok() {
        return ok(null);
    }

    public static <E> ResponseResult<E> ok(E result) {
        ResponseResult<E> responseResult = new ResponseResult<>();
        responseResult.setCode(ProductExEnum.COMMON.getCode() + ServiceExEnum.COMMON.getCode() + CommonCode.SUCCESS.buildCode());
        responseResult.setMsg("success");
        responseResult.setData(result);
        responseResult.setSuccess(true);
        responseResult.setTraceid(traceIdSupplier.get());
        return responseResult;
    }

    /**
     * 错误返回
     */
    public static <E> ResponseResult<E> error(String message) {
        return error(CommonCode.FAIL.buildCode(), message);
    }


    /**
     * 通过异常枚举返回错误信息
     */
    public static <E> ResponseResult<E> error(MessageCodeWrap messageCode) {
        return error(messageCode.buildCode(), messageCode.getDescription());
    }

    /**
     * 通过异常类返回错误信息
     */
    public static <E> ResponseResult<E> error(BaseException exception) {
        return error(exception.getCode(), exception.getMessage());
    }


    public static <E> ResponseResult<E> error(String code, String message) {
        return error(code, message, null);
    }


    public static <E> ResponseResult<E> error(String code, String message, E result) {
        ResponseResult<E> responseResult = new ResponseResult<>();
        responseResult.setCode(code);
        responseResult.setMsg(message);
        responseResult.setData(result);
        responseResult.setSuccess(false);
        responseResult.setTraceid(traceIdSupplier.get());
        return responseResult;
    }
}
