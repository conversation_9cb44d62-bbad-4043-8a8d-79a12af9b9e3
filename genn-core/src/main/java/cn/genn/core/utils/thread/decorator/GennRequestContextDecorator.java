package cn.genn.core.utils.thread.decorator;

import cn.genn.core.context.BaseRequestContext;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;

/**
 * <AUTHOR>
 */
public class GennRequestContextDecorator extends BaseThreadDecorator {

    public GennRequestContextDecorator() {
        super();
    }

    public GennRequestContextDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }

    @Override
    protected Object beforeExecOnCurrThread() {
        return BaseRequestContext.get();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof BaseRequestContext) {
            BaseRequestContext.set(JsonUtils.clone(object, BaseRequestContext.class));
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        BaseRequestContext.clear();
    }
}
