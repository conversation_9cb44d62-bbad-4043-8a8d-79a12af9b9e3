package cn.genn.core.utils;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.SpelNode;
import org.springframework.expression.spel.standard.SpelExpression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Set;

public class SpelUtils {

    private static final ExpressionParser parser = new SpelExpressionParser();

    public static StandardEvaluationContext initSpelContext(JoinPoint joinPoint) {
        StandardEvaluationContext spelContext = initSpelContext(joinPoint.getTarget());
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] argNames = methodSignature.getParameterNames();
        Object[] args = joinPoint.getArgs();
        if (null != argNames) {
            for (int i = 0; i < argNames.length; i++) {
                spelContext.setVariable(argNames[i], args[i]);
            }
        }
        return spelContext;
    }

    public static StandardEvaluationContext initSpelContext(Object rootObject) {
        StandardEvaluationContext spelContext = new StandardEvaluationContext();
        //todo 可拓展注册方法
        //spelContext.registerFunction();
        spelContext.setRootObject(rootObject);
        return spelContext;
    }

    public static <T> T parseSpelExpression(EvaluationContext spelContext, String spelExpression, Class<T> clazz) {
        if (!StringUtils.hasText(spelExpression)) {
            return null;
        }
        return parser.parseExpression(spelExpression).getValue(spelContext, clazz);
    }

    public static <T> T parseSpelExpression(Object object, String spelExpression, Class<T> clazz) {
        if (!StringUtils.hasText(spelExpression)) {
            return null;
        }
        return parser.parseExpression(spelExpression).getValue(object, clazz);
    }

    /**
     * 提取SpEL表达式中的变量
     *
     * @param expression SpEL表达式
     * @return 变量集合
     */
    public static Set<String> extractVariables(String expression) {
        SpelExpressionParser parser = new SpelExpressionParser();
        SpelExpression spelExpression = parser.parseRaw(expression);
        Set<String> variables = new HashSet<>();
        extractVariablesRecursively(spelExpression.getAST(), variables);
        return variables;
    }

    private static void extractVariablesRecursively(SpelNode node, Set<String> variables) {
        if (node == null) {
            return;
        }
        for (int i = 0; i < node.getChildCount(); i++) {
            SpelNode child = node.getChild(i);
            if (child.toStringAST().startsWith("#")) {
                // 检测到变量引用，添加到集合中
                variables.add(child.toStringAST());
            }
            extractVariablesRecursively(child, variables); // 递归遍历子节点
        }
    }
}
