package cn.genn.core.utils.valid;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ValidatorResult {

    private boolean pass;

    private String noPassValidatorName;

    private String message;

    public static ValidatorResult success() {
        ValidatorResult result = new ValidatorResult();
        result.setPass(true);
        return result;
    }

    public static ValidatorResult fail(String message) {
        ValidatorResult result = new ValidatorResult();
        result.setPass(false);
        result.setMessage(message);
        return result;
    }
}
