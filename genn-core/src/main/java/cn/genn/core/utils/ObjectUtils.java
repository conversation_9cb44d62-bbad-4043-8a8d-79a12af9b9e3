package cn.genn.core.utils;


import cn.genn.core.utils.jackson.JsonUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ObjectUtils {

    public static <T> T stringCastToObject(String object, Class<T> clazz) {
        if (object == null) {
            return null;
        }
        if (clazz.isAssignableFrom(String.class)) {
            return (T) object;
        }
        if (clazz == Byte.TYPE || clazz == Byte.class) {
            return (T) Byte.valueOf(object);
        }
        if (clazz == Short.TYPE || clazz == Short.class) {
            return (T) Short.valueOf(object);
        }
        if (clazz == Integer.TYPE || clazz == Integer.class) {
            return (T) Integer.valueOf(object);
        }
        if (clazz == Long.TYPE || clazz == Long.class) {
            return (T) Long.valueOf(object);
        }
        if (clazz == Float.TYPE || clazz == Float.class) {
            return (T) Float.valueOf(object);
        }
        if (clazz == Double.TYPE || clazz == Double.class) {
            return (T) Double.valueOf(object);
        }
        return JsonUtils.parse(object, clazz);
    }

    public static <T> T cast(Object object, Class<T> clazz) {
        if (object == null) {
            return null;
        }
        Class objClazz = object.getClass();
        if (clazz.isAssignableFrom(objClazz)) {
            return (T) object;
        }
        if (Number.class.isAssignableFrom(objClazz)) {
            Number numberObj = (Number) object;
            if (clazz == Byte.TYPE || clazz == Byte.class) {
                return (T) Byte.valueOf(numberObj.byteValue());
            }
            if (clazz == Short.TYPE || clazz == Short.class) {
                return (T) Short.valueOf(numberObj.shortValue());
            }
            if (clazz == Integer.TYPE || clazz == Integer.class) {
                return (T) Integer.valueOf(numberObj.intValue());
            }
            if (clazz == Long.TYPE || clazz == Long.class) {
                return (T) Long.valueOf(numberObj.longValue());
            }
            if (clazz == Float.TYPE || clazz == Float.class) {
                return (T) Float.valueOf(numberObj.floatValue());
            }
            if (clazz == Double.TYPE || clazz == Double.class) {
                return (T) Double.valueOf(numberObj.doubleValue());
            }
        } else if (String.class.isAssignableFrom(objClazz)) {
            String stringObj = (String) object;
            return stringCastToObject(stringObj, clazz);
        }
        return null;
    }

    public static <T> List<T> cast(Collection<?> objects, Class<T> clazz) {
        if (objects == null) {
            return null;
        }
        return objects.stream().map(o -> cast(o, clazz)).collect(Collectors.toList());
    }

    public static <T> Set<T> castToSet(Collection<?> objects, Class<T> clazz) {
        if (objects == null) {
            return null;
        }
        return objects.stream().map(o -> cast(o, clazz)).collect(Collectors.toSet());
    }

    public static <K, T> Map<K, T> cast(Map<K, ?> map, Class<T> clazz) {
        if (map == null) {
            return null;
        }
        Map<K, T> retMap = new HashMap<>();
        map.forEach((k, v) -> retMap.put(k, cast(v, clazz)));
        return retMap;
    }
}
