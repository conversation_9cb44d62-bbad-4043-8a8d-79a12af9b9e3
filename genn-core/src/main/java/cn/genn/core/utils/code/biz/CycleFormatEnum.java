package cn.genn.core.utils.code.biz;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CycleFormatEnum {

    /**
     * 日期格式
     */
    YY_MM_DD_HH_MM_SS("yyyyMMddHHmmss", "yyyyMMddHHmm", 60+10),
    YY_MM_DD_HH_MM("yyyyMMddHHmm", "yyyyMMddHHmm", 60+60),
    YY_MM_DD_HH("yyyyMMddHH", "yyyyMMddHH", 60*60+60),
    YY_MM_DD("yyyyMMdd", "yyyyMMdd", 24*60*60+60);

    private final String format;
    private final String key;
    private final int life;

    CycleFormatEnum(String format, String key, int life) {
        this.format = format;
        this.key=key;
        this.life = life;
    }
}
