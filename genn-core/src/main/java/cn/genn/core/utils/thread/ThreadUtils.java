package cn.genn.core.utils.thread;

import cn.hutool.core.thread.ThreadUtil;

/**
 * <AUTHOR>
 */
public class ThreadUtils {

    /**
     * 直接在公共线程中执行任务
     *
     * @param runnable        任务
     * @param threadDecorator 线程装饰器
     */
    public static void execute(Runnable runnable, ThreadDecorator threadDecorator) {
        if (threadDecorator != null) {
            runnable = threadDecorator.decorate(runnable);
        }
        Runnable finalRunnable = runnable;
        ThreadUtil.execute(finalRunnable);
    }
}
