package cn.genn.core.utils.query.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MemoryQueryEntity {

    private String columnName;

    private Object value;

    private MemoryQueryType queryType;


    public static MemoryQueryEntity build(String columnName, Object value, MemoryQueryType queryType) {
        MemoryQueryEntity query = new MemoryQueryEntity();
        query.setColumnName(columnName);
        query.setValue(value);
        query.setQueryType(queryType);
        return query;
    }
}
