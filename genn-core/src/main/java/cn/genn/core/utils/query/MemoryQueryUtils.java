package cn.genn.core.utils.query;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.utils.query.model.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 内存查询工具
 * <AUTHOR>
 */
public class MemoryQueryUtils {

    public static <T> MemoryQueryWrapper<T> buildWrapper() {
        return new MemoryQueryWrapper<>();
    }


    public static <T> List<T> selectList(List<T> records, MemoryQueryWrapper<T> queryWrapper) {
        if (CollectionUtil.isEmpty(records)) {
            return new ArrayList<>();
        }
        List<MemoryQueryEntity> conditionList = queryWrapper.getQueryEntityList();
        if (CollectionUtil.isNotEmpty(conditionList)) {
            records.removeIf(record -> {
                for (MemoryQueryEntity condition : conditionList) {
                    Object target = condition.getValue();
                    MemoryQueryType queryType = condition.getQueryType();
                    Object value = ReflectUtil.getFieldValue(record, condition.getColumnName());
                    if (!queryType.test(value, target)) {
                        return true;
                    }
                }
                return false;
            });
        }
        //排序
        List<MemoryOrderEntity> orderByList = queryWrapper.getOrderByList();
        if (CollectionUtil.isNotEmpty(orderByList)) {
            records.sort((o1, o2) -> {
                for (MemoryOrderEntity order : orderByList) {
                    Object value1 = ReflectUtil.getFieldValue(o1, order.getColumn());
                    Object value2 = ReflectUtil.getFieldValue(o2, order.getColumn());
                    int comparison = ((Comparable<Object>) value1).compareTo(value2);
                    if (order.getOrderBy() == MemoryOrderType.DESC) {
                        comparison = -comparison;
                    }
                    if (comparison != 0) {
                        return comparison;
                    }
                }
                return 0;
            });
        }
        return new ArrayList<>(records);
    }

    public static <T> PageResultDTO<T> selectPage(List<T> records, MemoryQueryWrapper<T> queryWrapper) {
        records = selectList(records, queryWrapper);
        MemoryPageEntity page = queryWrapper.getPage();
        if (page == null) {
            page = MemoryPageEntity.build(1, 10);
        }
        return PageResultDTO.buildPageResult(records, page.getPage(), page.getPageSize());
    }
}
