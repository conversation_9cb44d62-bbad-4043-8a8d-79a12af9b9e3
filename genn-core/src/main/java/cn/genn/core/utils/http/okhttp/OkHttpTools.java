package cn.genn.core.utils.http.okhttp;

import cn.genn.core.utils.http.model.NamedInputStream;
import cn.hutool.core.map.MapUtil;
import okhttp3.*;
import okio.BufferedSink;
import okio.Okio;
import okio.Source;
import org.springframework.http.HttpHeaders;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OkHttpTools {

    public final static MediaType APPLICATION_OCTET_STREAM = MediaType.parse(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE);

    public static MediaType getMediaType(org.springframework.http.MediaType contentType) {
        return MediaType.parse(contentType.toString());
    }

    public static RequestBody buildRequestBody(final File file) {
        return RequestBody.create(APPLICATION_OCTET_STREAM, file);
    }

    public static RequestBody buildRequestBody(final InputStream stream) {
        return new RequestBody() {
            @Override
            public MediaType contentType() {
                // 设置body mime类型，这里以二进制流为例
                return APPLICATION_OCTET_STREAM;
            }

            @Override
            public long contentLength() throws IOException {
                // 返回-1表示body长度未知，将开启http chunk支持
                // RequestBody中默认返回也时-1
                return -1;
            }

            @Override
            public void writeTo(BufferedSink sink) throws IOException {
                try (Source source = Okio.source(stream)) {
                    sink.writeAll(source);
                }
            }
        };
    }

    public static Map<String, String> setContentType(Map<String, String> headers, org.springframework.http.MediaType contentType) {
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put(HttpHeaders.CONTENT_TYPE, contentType.toString());
        return headers;
    }


    public static HttpUrl buildHttpUrl(String url, Map<String, String> params) {
        HttpUrl.Builder newBuilder = HttpUrl.parse(url).newBuilder();
        if (MapUtil.isNotEmpty(params)) {
            params.forEach((key, value) -> {
                if (value != null) {
                    newBuilder.addQueryParameter(key, value);
                }
            });
        }
        return newBuilder.build();
    }

    public static Headers buildHeaders(Map<String, String> headers) {
        if (MapUtil.isNotEmpty(headers)) {
            return Headers.of(headers);
        }
        return new Headers.Builder().build();
    }

    public static FormBody buildFormBody(Map<String, String> params) {
        FormBody.Builder formBodyBuilder = new FormBody.Builder(StandardCharsets.UTF_8);
        if (MapUtil.isNotEmpty(params)) {
            params.forEach((key, value) -> {
                if (value != null) {
                    formBodyBuilder.add(key, value);
                }
            });
        }
        return formBodyBuilder.build();
    }

    public static MultipartBody buildMultipartBody(Map<String, String> formData, Map<String, File> files, Map<String, NamedInputStream> inputStreams) {
        MultipartBody.Builder multipartBodyBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        if (MapUtil.isNotEmpty(formData)) {
            formData.forEach(multipartBodyBuilder::addFormDataPart);
        }
        if (MapUtil.isNotEmpty(files)) {
            files.forEach((k, v) -> {
                if (v != null) {
                    RequestBody fileBody = OkHttpTools.buildRequestBody(v);
                    multipartBodyBuilder.addFormDataPart(k, v.getName(), fileBody);
                }
            });
        }
        if (MapUtil.isNotEmpty(inputStreams)) {
            inputStreams.forEach((k, v) -> {
                if (v != null) {
                    RequestBody fileBody = OkHttpTools.buildRequestBody(v.getInputStream());
                    multipartBodyBuilder.addFormDataPart(k, v.getFilename(), fileBody);
                }
            });
        }
        return multipartBodyBuilder.build();
    }
}
