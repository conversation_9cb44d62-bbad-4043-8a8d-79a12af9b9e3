package cn.genn.core.utils.http.okhttp;

import lombok.Getter;
import okhttp3.CookieJar;
import okhttp3.Interceptor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class OkHttpClientConfig {
    public static final long DEFAULT_CONNECT_TIMEOUT = 10 * 1000;
    public static final long DEFAULT_WRITE_TIMEOUT = 30 * 1000;
    public static final long DEFAULT_READ_TIMEOUT = 30 * 1000;

    /**
     * 默认okhttp最大空闲连接数（5）
     */
    public static final int DEFAULT_MAX_IDLE_CONNECTIONS = 5;
    /**
     * 默认okhttp活动链接存货时间（5分钟）
     */
    public static final long DEFAULT_KEEP_ALIVE_DURATION_MINUTES = 5;
    /**
     * 默认okhttp活动链接存货时间单位, （分钟）
     */
    public static final TimeUnit DEFAULT_KEEP_ALIVE_DURATION_TIME_UNIT = TimeUnit.MINUTES;

    @Getter
    private long connectTimeout;
    @Getter
    private long readTimeout;
    @Getter
    private long writeTimeout;
    @Getter
    private int maxIdleConnections;
    @Getter
    private long keepAliveDuration;
    @Getter
    private TimeUnit keepAliveTimeUnit;
    @Getter
    private boolean retryOnConnectionFailure;
    @Getter
    private boolean followRedirects;
    @Getter
    private boolean followSslRedirects;
    @Getter
    private CookieJar cookieJar;
    @Getter
    private List<Interceptor> interceptors;
    @Getter
    private List<Interceptor> networkInterceptors;
    @Getter
    private ExecutorService executorService;

    public OkHttpClientConfig() {
        this(DEFAULT_MAX_IDLE_CONNECTIONS, DEFAULT_KEEP_ALIVE_DURATION_MINUTES, DEFAULT_KEEP_ALIVE_DURATION_TIME_UNIT);
    }

    public OkHttpClientConfig(int maxIdleConnections, long keepAliveDuration, TimeUnit keepAliveTimeUnit) {
        this.maxIdleConnections = maxIdleConnections;
        this.keepAliveDuration = keepAliveDuration;
        this.keepAliveTimeUnit = keepAliveTimeUnit;
        this.connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        this.readTimeout = DEFAULT_READ_TIMEOUT;
        this.writeTimeout = DEFAULT_WRITE_TIMEOUT;
        this.retryOnConnectionFailure = true;
        this.followRedirects = true;
        this.followSslRedirects = true;
        this.cookieJar = CookieJar.NO_COOKIES;
    }

    public OkHttpClientConfig setFollowRedirects(boolean followRedirects) {
        this.followRedirects = followRedirects;
        this.followSslRedirects = followRedirects;
        return this;
    }

    public OkHttpClientConfig setFollowSslRedirects(boolean followSslRedirects) {
        this.followSslRedirects = followSslRedirects;
        return this;
    }

    public OkHttpClientConfig setCookieJar(CookieJar cookieJar) {
        this.cookieJar = cookieJar;
        return this;
    }

    public OkHttpClientConfig setRetryOnConnectionFailure(boolean retryOnConnectionFailure) {
        this.retryOnConnectionFailure = retryOnConnectionFailure;
        return this;
    }

    public OkHttpClientConfig setInterceptors(List<Interceptor> interceptors) {
        this.interceptors = interceptors;
        return this;
    }

    public OkHttpClientConfig addInterceptor(Interceptor interceptor) {
        if (interceptor == null) {
            return this;
        }

        if (this.interceptors == null) {
            this.interceptors = new ArrayList<>();
        }

        this.interceptors.add(interceptor);
        return this;
    }

    public OkHttpClientConfig setNetworkInterceptors(List<Interceptor> networkInterceptors) {
        this.networkInterceptors = networkInterceptors;
        return this;
    }

    public OkHttpClientConfig addNetworkInterceptor(Interceptor interceptor) {
        if (interceptor == null) {
            return this;
        }

        if (this.networkInterceptors == null) {
            this.networkInterceptors = new ArrayList<>();
        }
        this.networkInterceptors.add(interceptor);
        return this;
    }

    public OkHttpClientConfig setMaxIdleConnections(int maxIdleConnections) {
        this.maxIdleConnections = maxIdleConnections;
        return this;
    }

    public OkHttpClientConfig setKeepAliveDuration(long keepAliveDuration) {
        this.keepAliveDuration = keepAliveDuration;
        return this;
    }

    public OkHttpClientConfig setKeepAliveTimeUnit(TimeUnit keepAliveTimeUnit) {
        this.keepAliveTimeUnit = keepAliveTimeUnit;
        return this;
    }

    public OkHttpClientConfig setExecutorService(ExecutorService executorService) {
        this.executorService = executorService;
        return this;
    }

    public OkHttpClientConfig setTimeout(long connectTimeout, long readTimeout, long writeTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
        this.writeTimeout = writeTimeout;
        return this;
    }

    public OkHttpClientConfig setTimeout(long connectTimeout, long readTimeout, long writeTimeout, TimeUnit timeUnit) {
        this.connectTimeout = timeUnit.toMillis(connectTimeout);
        this.readTimeout = timeUnit.toMillis(readTimeout);
        this.writeTimeout = timeUnit.toMillis(writeTimeout);
        return this;
    }
}
