package cn.genn.core.utils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.MethodParameter;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class AnnotationUtils {

    /**
     * 查询对象类中注解的第一个Field
     *
     * @param clazz
     * @param annotationType
     * @param <T>
     * @return
     */
    public static <T extends Annotation> Optional<Field> getAnnotationField(Class<?> clazz, Class<T> annotationType) {
        return Arrays.stream(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(annotationType))
                .findFirst();
    }

    /**
     * 查询对象类中注解的所有Field
     *
     * @param clazz
     * @param annotationType
     * @param <T>
     * @return
     */
    public static <T extends Annotation> List<Field> getAnnotationFields(Class<?> clazz, Class<T> annotationType) {
        return Arrays.stream(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(annotationType))
                .collect(Collectors.toList());
    }

    /**
     * 查询对象类中注解的所有Field名称
     *
     * @param clazz
     * @param annotationType
     * @param <T>
     * @return
     */
    public static <T extends Annotation> Set<String> getAnnotationFieldNames(Class<?> clazz, Class<T> annotationType) {
        return Arrays.stream(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(annotationType))
                .map(Field::getName).collect(Collectors.toSet());
    }

    /**
     * 依次获取
     * <p>
     * 方法上的注解
     * 类上的注解
     *
     * @param parameter
     * @param annotationType
     * @param <T>
     * @return
     */
    public static <T extends Annotation> T getMethodAnnotation(MethodParameter parameter, Class<T> annotationType) {
        if (parameter.hasMethodAnnotation(annotationType)) {
            return parameter.getMethodAnnotation(annotationType);
        }
        return parameter.getContainingClass().getAnnotation(annotationType);
    }

    /**
     * 依次检查是否存在
     * 方法上的注解
     * 类上的注解
     *
     * @param parameter
     * @param annotationType
     * @param <T>
     * @return
     */
    public static <T extends Annotation> boolean hasMethodAnnotation(MethodParameter parameter, Class<T> annotationType) {
        return parameter.hasMethodAnnotation(annotationType)
                || parameter.getContainingClass().getAnnotation(annotationType) != null;
    }

    /**
     * 依次获取
     * <p>
     * 参数上的注解
     * 方法上的注解
     * 类上的注解
     *
     * @param parameter
     * @param annotationType
     * @param <T>
     * @return
     */
    public static <T extends Annotation> T getParameterAnnotation(MethodParameter parameter, Class<T> annotationType) {
        if (parameter.hasParameterAnnotation(annotationType)) {
            return parameter.getParameterAnnotation(annotationType);
        }
        if (parameter.hasMethodAnnotation(annotationType)) {
            return parameter.getMethodAnnotation(annotationType);
        }
        return parameter.getContainingClass().getAnnotation(annotationType);
    }

    /**
     * 依次检查是否存在
     * <p>
     * 参数上的注解
     * 方法上的注解
     * 类上的注解
     *
     * @param parameter
     * @param annotationType
     * @param <T>
     * @return
     */
    public static <T extends Annotation> boolean hasParameterAnnotation(MethodParameter parameter, Class<T> annotationType) {
        return parameter.hasParameterAnnotation(annotationType)
                || parameter.hasMethodAnnotation(annotationType)
                || parameter.getContainingClass().getAnnotation(annotationType) != null;
    }

    /**
     * 获取方法或类上的上的注解
     * @param point
     * @param tClass
     * @return
     * @param <T>
     */
    public static  <T extends Annotation> T getAnnotation(ProceedingJoinPoint point, Class<T> tClass) {
        T t = null;
        Signature signature = point.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        if (method != null && method.isAnnotationPresent(tClass)) {
            t = method.getAnnotation(tClass);
        }
        if (t == null && method.getDeclaringClass().isAnnotationPresent(tClass)) {
            t = method.getDeclaringClass().getAnnotation(tClass);
        }
        return t;
    }
}
