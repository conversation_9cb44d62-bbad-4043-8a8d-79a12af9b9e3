package cn.genn.core.utils;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.text.CharSequenceUtil;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;

import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class WebExchangeUtils {

    public static final String CACHED_REQUEST_BODY_KEY = "customCachedRequestBody";
    private static final AntPathMatcher antPathMatcher = new AntPathMatcher();

    /**
     * 获取缓存好的请求体,BufferedRequestWebFilter必须生效
     */
    public static String getCachedRequestBody(ServerWebExchange exchange) {
        DataBuffer cachedBody = exchange.getAttribute(CACHED_REQUEST_BODY_KEY);
        return cachedBody.toString(StandardCharsets.UTF_8);
    }

    /**
     * 获取缓存好的请求体,BufferedRequestWebFilter必须生效
     */
    public static <T> T getCachedRequestBody(ServerWebExchange exchange, Class<T> cls) {
        String requestBody = getCachedRequestBody(exchange);
        return JsonUtils.parse(requestBody, cls);
    }

    /**
     * 给exchange中放入属性,一次请求共享.
     */
    public static void putAttribute(ServerWebExchange exchange, String key, Object value) {
        exchange.getAttributes().put(key, value);
    }

    /**
     * 从exchange中获取属性
     */
    @SuppressWarnings("unchecked")
    public static <T> T getAttribute(ServerWebExchange exchange, String key) {
        Object o = exchange.getAttributes().get(key);
        if (o == null) {
            return null;
        }
        return (T) o;
    }

    /**
     * 获取请求IP
     */
    public static String getRequestIp(ServerHttpRequest request) {
        String ip = "";
        HttpHeaders requestHeaders = request.getHeaders();
        if (isEmptyOrInnerIp(ip)) {
            ip = requestHeaders.getFirst("X-Forwarded-For");
        }
        if (isEmptyOrInnerIp(ip)) {
            ip = requestHeaders.getFirst("X-Original-Forwarded-For");
        }
        if (isEmptyOrInnerIp(ip)) {
            ip = requestHeaders.getFirst("X-Hwwaf-Real-IP");
        }
        if (isEmptyOrInnerIp(ip)) {
            ip = requestHeaders.getFirst("X-Real-IP");
        }
        if (isEmptyOrInnerIp(ip)) {
            InetSocketAddress remoteAddress = request.getRemoteAddress();
            if (remoteAddress.getAddress() != null) {
                // 获取到客户端的IP地址
                ip = remoteAddress.getAddress().getHostAddress();
            }
        }
        if (CharSequenceUtil.isEmpty(ip)) {
            ip = "0.0.0.0";
        }
        return ip;
    }


    /**
     * 获取请求头
     */
    public static Map<String, String> getHeaderMap(ServerHttpRequest request) {
        Map<String, String> headerMap = new HashMap<>();
        request.getHeaders().forEach((key, value) -> {
            headerMap.put(key, value.get(0));
        });
        return headerMap;
    }

    /**
     * 获取请求参数
     */
    public static Map<String, String[]> getParameterMap(ServerHttpRequest request) {
        MultiValueMap<String, String> queryParams = request.getQueryParams();
        Map<String, String[]> parameterMap = new HashMap<>();
        queryParams.forEach((key, value) -> {
            parameterMap.put(key, value.toArray(new String[0]));
        });
        return parameterMap;
    }

    /**
     * 获取参数或请求头
     */
    public static String getParamOrHeader(ServerWebExchange webExchange, String key) {
        MultiValueMap<String, String> queryParams = webExchange.getRequest().getQueryParams();
        return queryParams.getFirst(key);
    }

    /**
     * 是否包含指定的媒体类型
     */
    public static boolean isIncludeMediaType(ServerHttpRequest request, MediaType[] supportedMediaTypes) {
        if (supportedMediaTypes == null || supportedMediaTypes.length == 0) {
            return false;
        }
        MediaType contentType = request.getHeaders().getContentType();
        return Arrays.stream(supportedMediaTypes).anyMatch(mediaType -> mediaType.includes(contentType));
    }

    /**
     * 是否包含指定的请求方法
     */
    public static boolean isIncludeRequestMethod(ServerHttpRequest request, HttpMethod[] supportedHttpMethods) {
        if (supportedHttpMethods == null || supportedHttpMethods.length == 0) {
            return false;
        }
        return Arrays.stream(supportedHttpMethods).anyMatch(method -> method.equals(request.getMethod()));
    }

    /**
     * 是否包含指定的uri
     */
    public static boolean isIncludeUrlMatch(ServerHttpRequest request, String[] includeUriPattern) {
        if (includeUriPattern == null || includeUriPattern.length == 0) {
            return false;
        }
        String url = request.getPath().value();
        return Arrays.stream(includeUriPattern).anyMatch(pattern -> antPathMatcher.match(pattern, url));
    }


    private static boolean isEmptyOrInnerIp(String ip) {
        return ip == null || CharSequenceUtil.isEmpty(ip) || ip.startsWith("192.168.") || ip.startsWith("10.") || ip.startsWith("172.16.");
    }

}
