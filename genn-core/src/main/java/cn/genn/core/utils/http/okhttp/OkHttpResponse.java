package cn.genn.core.utils.http.okhttp;

import cn.genn.core.utils.http.model.HttpResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import okhttp3.Response;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OkHttpResponse implements HttpResponse {

    private Response response;

    @Override
    public int getCode() {
        return response.code();
    }

    @Override
    public String getHeader(String name) {
        return response.headers().get(name);
    }

    @Override
    public String string() {
        try {
            if (response == null || response.body() == null) {
                return null;
            }
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException("http response read failed", e);
        } finally {
            close();
        }
    }

    @Override
    public byte[] bytes() {
        try {
            if (response == null || response.body() == null) {
                return null;
            }
            return response.body().bytes();
        } catch (IOException e) {
            throw new RuntimeException("http response read failed", e);
        } finally {
            close();
        }
    }

    @Override
    public void close() {
        response.close();
    }
}
