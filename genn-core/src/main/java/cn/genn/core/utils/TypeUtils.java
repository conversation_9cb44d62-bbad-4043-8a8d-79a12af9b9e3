package cn.genn.core.utils;

import cn.hutool.core.util.TypeUtil;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
public class TypeUtils {

    /**
     * 获取指定类的泛型类型。默认获取第一个父类的第一个泛型类型。
     * @param classType 需要获取泛型类型的类，不能为null
     * @return 指定类的泛型类型，如果没有泛型，返回null
     */
    public static Class<?> getGenericType(Class<?> classType) {
        return getGenericType(classType, 0);
    }


    /**
     * 获取指定类的泛型类型。默认获取第一个父类或者接口的泛型类型。优先以父类为主
     *
     * @param classType 需要获取泛型类型的类，不能为null
     * @param index 泛型参数的索引，从0开始
     * @return
     */
    public static Class<?> getGenericType(Class<?> classType, int index) {
        Class<?> superclass = classType.getSuperclass();
        if(superclass == null || superclass == Object.class) {
            Class<?>[] interfaces = classType.getInterfaces();
            if(interfaces.length > 0) {
                superclass = interfaces[0];
            }
        }
        if (superclass == null) {
            return null;
        }
        return getGenericType(classType, TypeUtil.getClass(superclass), index);
    }


    /**
     * 获取指定类的泛型类型。
     *
     * <p>此方法用于获取指定类的父类或接口的泛型类型。如果指定的父类或接口没有泛型，或者索引超出了泛型参数的数量，将返回null。</p>
     *
     * <pre>
     * 例如，有如下的类定义：
     * public class TestClass extends SuperClass&lt;String, Integer&gt; {}
     * public interface SuperClass&lt;T, E&gt; {}
     *
     * 使用此方法获取TestClass的SuperClass的第一个泛型参数类型：
     * Class&lt;?&gt; genericType = getGenericType(TestClass.class, SuperClass.class, 0);
     * 此时，genericType将是String.class。
     * </pre>
     *
     * @param classType 需要获取泛型类型的类，不能为null
     * @param superType 父类或接口的类型，不能为null
     * @param index 泛型参数的索引，从0开始
     * @return 指定类的父类或接口的泛型类型，如果没有泛型或索引超出范围，返回null
     */
    public static Class<?> getGenericType(Class<?> classType, Class<?> superType, int index) {
        Class<?>[] genericType = getGenericType(classType, superType);
        if (genericType != null && index < genericType.length) {
            return genericType[index];
        }
        return null;
    }

    /**
     * 获取指定类的所有泛型类型。
     * @param classType 需要获取泛型类型的类，不能为null
     * @param superType 父类或接口的类型，不能为null
     * @return
     */
    public static Class<?>[] getGenericType(Class<?> classType, Class<?> superType) {
        ParameterizedType[] generics = TypeUtil.getGenerics(classType);
        for (ParameterizedType generic : generics) {
            if (generic.getRawType().equals(superType)) {
                Type[] actualTypeArguments = generic.getActualTypeArguments();
                Class<?>[] classes = new Class[actualTypeArguments.length];
                for (int i = 0; i < actualTypeArguments.length; i++) {
                    classes[i] = TypeUtil.getClass(actualTypeArguments[i]);
                }
                return classes;
            }
        }
        return null;
    }

}
