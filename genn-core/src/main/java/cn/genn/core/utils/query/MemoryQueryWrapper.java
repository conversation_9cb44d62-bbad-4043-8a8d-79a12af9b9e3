package cn.genn.core.utils.query;

import cn.genn.core.utils.query.model.*;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public class MemoryQueryWrapper<T> {

    /**
     * 查询条件封装
     */
    private final List<MemoryQueryEntity> queryEntityList = new ArrayList<>();

    /**
     * 分页封装
     */
    private MemoryPageEntity page;

    /**
     * 排序条件封装
     */
    private final List<MemoryOrderEntity> orderByList = new ArrayList<>();

    public MemoryQueryWrapper<T> eq(String column, Object value) {
        return eq(true, column, value);
    }

    public MemoryQueryWrapper<T> eq(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.EQ));
        }
        return this;
    }

    public MemoryQueryWrapper<T> neq(String column, Object value) {
        return neq(true, column, value);
    }

    public MemoryQueryWrapper<T> neq(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.NOT_EQ));
        }
        return this;
    }

    public MemoryQueryWrapper<T> like(String column, Object value) {
        return like(true, column, value);
    }

    public MemoryQueryWrapper<T> like(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.LIKE));
        }
        return this;
    }

    public MemoryQueryWrapper<T> notLike(String column, Object value) {
        return notLike(true, column, value);
    }

    public MemoryQueryWrapper<T> notLike(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.NOT_LIKE));
        }
        return this;
    }

    public MemoryQueryWrapper<T> in(String column, Object value) {
        return in(true, column, value);
    }

    public MemoryQueryWrapper<T> in(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.IN));
        }
        return this;
    }

    public MemoryQueryWrapper<T> notIn(String column, Object value) {
        return notIn(true, column, value);
    }

    public MemoryQueryWrapper<T> notIn(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.NOT_IN));
        }
        return this;
    }

    public MemoryQueryWrapper<T> gt(String column, Object value) {
        return gt(true, column, value);
    }

    public MemoryQueryWrapper<T> gt(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.GT));
        }
        return this;
    }

    public MemoryQueryWrapper<T> ge(String column, Object value) {
        return ge(true, column, value);
    }

    public MemoryQueryWrapper<T> ge(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.GE));
        }
        return this;
    }

    public MemoryQueryWrapper<T> lt(String column, Object value) {
        return lt(true, column, value);
    }

    public MemoryQueryWrapper<T> lt(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.LT));
        }
        return this;
    }

    public MemoryQueryWrapper<T> le(String column, Object value) {
        return le(true, column, value);
    }

    public MemoryQueryWrapper<T> le(boolean condition, String column, Object value) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, value, MemoryQueryType.LE));
        }
        return this;
    }

    public MemoryQueryWrapper<T> isNull(String column) {
        return isNull(true, column);
    }

    public MemoryQueryWrapper<T> isNull(boolean condition, String column) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, null, MemoryQueryType.IS_NULL));
        }
        return this;
    }

    public MemoryQueryWrapper<T> notNull(String column) {
        return notNull(true, column);
    }

    public MemoryQueryWrapper<T> notNull(boolean condition, String column) {
        if (condition) {
            queryEntityList.add(MemoryQueryEntity.build(column, null, MemoryQueryType.NOT_NULL));
        }
        return this;
    }

    public MemoryQueryWrapper<T> page(int page, int pageSize) {
        this.page = MemoryPageEntity.build(page, pageSize);
        return this;
    }

    public MemoryQueryWrapper<T> orderBy(String column, MemoryOrderType orderType) {
        orderByList.add(MemoryOrderEntity.build(column, orderType));
        return this;
    }

}
