package cn.genn.core.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TreeUtils {

    /**
     * 构建树形结构，默认根节点为0，id字段为id，父节点字段为pid，子节点字段为children
     * @param allData 所有数据
     * @return
     * @param <T> 数据类型
     */
    public static <T> List<T> buildTree(List<T> allData) {
        return buildTree(allData, 0L);
    }

    /**
     * 构建树形结构，默认id字段为id，父节点字段为pid，子节点字段为children
     * @param allData 所有数据
     * @param rootParentId 最外层父节点
     * @param <T> 数据类型
     * @param <E> 父节点类型
     */
    public static <T, E> List<T> buildTree(List<T> allData, E rootParentId) {
        return buildTree(allData, rootParentId, null, null, null);
    }

    /**
     * 构建树形结构
     * @param allData 所有数据
     * @param rootParentId 最外层父节点
     * @param <T> 数据类型
     * @param <E> 父节点类型
     */
    @SuppressWarnings("unchecked")
    public static <T, E> List<T> buildTree(List<T> allData, E rootParentId, String idField, String parentIdField, String childrenField) {
        if (CollUtil.isEmpty(allData)) {
            return Collections.emptyList();
        }
        idField = Optional.ofNullable(idField).orElse("id");
        parentIdField = Optional.ofNullable(parentIdField).orElse("pid");
        childrenField = Optional.ofNullable(childrenField).orElse("children");

        String finalParentIdField = parentIdField;
        String finalIdField = idField;
        String finalChildrenField = childrenField;

        Map<E, List<T>> parentMap = allData.stream().collect(Collectors.groupingBy(data -> {
            E parentId = (E) ReflectUtil.getFieldValue(data, finalParentIdField);
            return Optional.ofNullable(parentId).orElse(rootParentId);
        }));

        allData.forEach(data -> {
            E id = (E) ReflectUtil.getFieldValue(data, finalIdField);
            List<T> children = parentMap.getOrDefault(id, Collections.emptyList());
            ReflectUtil.setFieldValue(data, finalChildrenField, children);
        });

        // 返回根节点
        return parentMap.getOrDefault(rootParentId, Collections.emptyList());
    }

}
