package cn.genn.core.utils.jackson;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.introspect.AnnotatedMember;
import com.fasterxml.jackson.databind.introspect.JacksonAnnotationIntrospector;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class JsonUtils {

    public static final String FILTER_LOG = "log";

    private static final ConcurrentHashMap<String, ObjectMapper> filterMappers = new ConcurrentHashMap<>();

    private static final ObjectMapper mapper = createObjectMapper(false);
    private static final ObjectMapper notNullMapper = createObjectMapper(true);
    @Getter
    private static final ObjectMapper enableTypingMapper = createEnableTypingObjectMapper();


    public static String simpleJson(String key, String value) {
        Map<String, String> map = new HashMap<>();
        map.put(key, value);
        return toJson(map);
    }

    public static <T> T parse(String str, Class<T> cls) {
        return parse(str, constructJavaType(cls), true);
    }

    public static <T> T parse(String str, Class<T> cls, boolean printLog) {
        return parse(str, constructJavaType(cls), printLog);
    }

    public static <T> T parse(String jsonStr, TypeReference<?> valueTypeRef) {
        return parse(jsonStr, constructJavaType(valueTypeRef), true);
    }

    public static <T> T parse(String jsonStr, JavaType valueType, boolean printLog) {
        try {
            if (StrUtil.isNotBlank(jsonStr)) {
                return mapper.readValue(jsonStr, valueType);
            }
        } catch (Exception e) {
            if (printLog) {
                log.error("parse failed, jsonStr=" + jsonStr, e);
            }
        }
        return null;
    }

    /**
     * 克隆对象，深拷贝
     */
    public static <T> T clone(Object obj, Class<T> cls) {
        return parse(toJson(obj), cls);
    }

    /**
     * 克隆对象，深拷贝
     */
    @SuppressWarnings("unchecked")
    public static <T> T clone(T obj) {
        return (T) parse(toJson(obj), obj.getClass());
    }

    /**
     * 构建java简单类型
     *
     * @param cls
     * @return
     */
    public static JavaType constructJavaType(Class<?> cls) {
        if (cls == null) {
            return null;
        }
        return mapper.getTypeFactory().constructType(cls);
    }

    public static JavaType constructJavaType(Type type) {
        return mapper.getTypeFactory().constructType(type);
    }

    /**
     * 构造java类型
     */
    public static JavaType constructJavaType(TypeReference<?> typeRef) {
        return mapper.getTypeFactory().constructType(typeRef);
    }

    /**
     * 构造泛型java类型
     * 使用parse解析的时候，会自动对应泛型
     *
     * @param genericClass     泛型对象Class
     * @param parameterClasses 泛型对象的泛型，例如List<String>, 第一个参数是List.class, 第二个参数为String.class
     * @return
     */
    public static JavaType constructGenericType(Class<?> genericClass, Class<?>... parameterClasses) {
        return mapper.getTypeFactory().constructParametricType(genericClass, parameterClasses);
    }

    /**
     * json字符串转list
     */
    public static <T> List<T> parseToList(String jsonStr, Class<T> cls) {
        return parse(jsonStr, constructGenericType(List.class, cls), true);
    }

    public static <T> Set<T> parseToSet(String jsonStr, Class<T> cls) {
        return parse(jsonStr, constructGenericType(Set.class, cls), true);
    }

    public static <T> Set<T> parseToCollection(String jsonStr, Class<T> cls) {
        return parse(jsonStr, constructGenericType(Collection.class, cls), true);
    }

    public static Map<String, String> parseToMap(String str) {
        return parseToMap(str, String.class, String.class);
    }

    public static Map<String, Object> parseToObjectMap(String str) {
        return parseToMap(str, String.class, Object.class);
    }

    public static LinkedHashMap<String, Object> parseToObjectLinkedMap(String str) {
        return parseToLinkedMap(str, String.class, Object.class);
    }

    /**
     * json字符串转map
     */
    public static <K, V> Map<K, V> parseToMap(String jsonStr, Class<K> kCls, Class<V> vCls) {
        return parse(jsonStr, constructGenericType(Map.class, kCls, vCls), true);
    }

    public static <K, V> LinkedHashMap<K, V> parseToLinkedMap(String jsonStr, Class<K> kCls, Class<V> vCls) {
        return parse(jsonStr, constructGenericType(LinkedHashMap.class, kCls, vCls), true);
    }


    /**
     * object对象转换给json string
     *
     * @param obj
     * @return
     */
    public static String toJson(Object obj) {
        return toJson(obj, false);
    }

    /**
     * object对象转换给json string
     *
     * @param obj
     * @param prettyPrinter
     * @return
     */
    public static String toJson(Object obj, boolean prettyPrinter) {
        return toJson(mapper, obj, prettyPrinter);
    }

    public static String toJsonNotNull(Object obj) {
        return toJson(notNullMapper, obj, false);
    }


    public static String toJsonNotNull(Object obj, boolean prettyPrinter) {
        return toJson(notNullMapper, obj, prettyPrinter);
    }

    public static String toJsonLog(Object obj) {
        return toJson(FILTER_LOG, obj);
    }

    /**
     * object对象转换给json string
     *
     * @param filter 指定过滤filter名， 配合{@link JsonIgnoreFilter} 过滤字段
     * @param obj
     * @return
     */
    public static String toJson(String filter, Object obj) {
        return toJson(filter, obj, false);
    }

    public static String toJson(String filter, Object obj, boolean prettyPrinter) {
        ObjectMapper mapper = createFilterObjectMapper(filter);
        return toJson(mapper, obj, prettyPrinter);
    }

    /**
     * object对象转换给json string
     *
     * @param obj
     * @param prettyPrinter
     * @return
     */
    public static String toJson(ObjectMapper mapper, Object obj, boolean prettyPrinter) {
        String retStr = "";
        if (obj == null) {
            return retStr;
        }
        try {
            if (prettyPrinter) {
                retStr = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
            } else {
                retStr = mapper.writeValueAsString(obj);
            }
        } catch (Exception e) {
            log.error("Object to json string failed!", e);
        }
        return retStr;
    }

    public static <K, V> Map<K, List<V>> parseToMapList(String str, Class<K> keyClass, Class<V> valueClass) {
        return parse(str, mapper.getTypeFactory().constructMapType(Map.class, constructJavaType(keyClass), constructCollectionType(ArrayList.class, valueClass)), true);
    }

    public static JavaType constructCollectionType(Class<? extends Collection> collectionClass, Class<?> elementClass) {
        return mapper.getTypeFactory().constructCollectionType(collectionClass, elementClass);
    }

    /**
     * 构造List类型
     */
    public static JavaType constructListType(Class<?> elementClass) {
        return mapper.getTypeFactory().constructCollectionType(List.class, elementClass);
    }

    /**
     * 构造List类型
     */
    public static JavaType constructListType(JavaType elementType) {
        return mapper.getTypeFactory().constructCollectionType(List.class, elementType);
    }

    /**
     * 构造嵌套List类型
     */
    public static JavaType constructNestListType(Class<?> elementClass) {
        TypeFactory typeFactory = mapper.getTypeFactory();
        JavaType innerListType = typeFactory.constructCollectionType(List.class, elementClass);
        return typeFactory.constructCollectionType(List.class, innerListType);
    }

    /**
     * 构造嵌套List类型
     */
    public static JavaType constructNestListType(JavaType elementType) {
        TypeFactory typeFactory = mapper.getTypeFactory();
        JavaType innerListType = typeFactory.constructCollectionType(List.class, elementType);
        return typeFactory.constructCollectionType(List.class, innerListType);
    }

    /**
     * 根据表达式获取指定值
     * person
     * person.name
     * persons[3]
     * person.friends[5].name
     */
    public static String strExpression(String jsonStr, String expression) {
        JSON json = JSONUtil.parse(jsonStr);
        return json.getByPath(expression).toString();
    }


    public static <T> T strExpression(String jsonStr, String expression, Class<T> cls) {
        JSON json = JSONUtil.parse(jsonStr);
        return json.getByPath(expression, cls);
    }

    /**
     * 修改json字符串中指定表达式的值
     *
     * @param expression 表达式 beanPath
     */
    public static String setValue(String jsonStr, String expression, Object value) {
        Map<String, Object> paramMap = JsonUtils.parseToMap(jsonStr, String.class, Object.class);
        BeanUtil.setProperty(paramMap, expression, value);
        return toJson(paramMap);
    }

    public static String setValue(String jsonStr, Map<String, Object> expressionValueMap) {
        Map<String, Object> paramMap = JsonUtils.parseToMap(jsonStr, String.class, Object.class);
        expressionValueMap.forEach((k, v) -> BeanUtil.setProperty(paramMap, k, v));
        return toJson(paramMap);
    }

    public static String setValue(String jsonStr, Object... expressionValue) {
        if (expressionValue.length % 2 != 0) {
            throw new IllegalArgumentException("expressionValue length must be even");
        }
        Map<String, Object> paramMap = JsonUtils.parseToMap(jsonStr, String.class, Object.class);
        for (int i = 0; i < expressionValue.length; i += 2) {
            BeanUtil.setProperty(paramMap, expressionValue[i].toString(), expressionValue[i + 1]);
        }
        return toJson(paramMap);
    }

    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        return mapper.convertValue(map, clazz);
    }

    private static ObjectMapper createFilterObjectMapper(String filter) {
        return filterMappers.computeIfAbsent(filter, k -> {
            ObjectMapper objectMapper = createObjectMapper(true);
            objectMapper.setAnnotationIntrospector(new JacksonAnnotationIntrospector() {
                private static final long serialVersionUID = 1L;

                @Override
                public boolean hasIgnoreMarker(AnnotatedMember m) {
                    if (super.hasIgnoreMarker(m)) {
                        return true;
                    }
                    JsonIgnoreFilter ann = _findAnnotation(m, JsonIgnoreFilter.class);
                    if (ann == null) {
                        return false;
                    }
                    String[] filters = ann.value();
                    if (filters.length == 0) {
                        return true;
                    }
                    return Arrays.asList(filters).contains(filter);
                }
            });
            return objectMapper;
        });
    }

    public static ObjectMapper createObjectMapper(boolean includeNotNull) {
        ObjectMapper mapper = new ObjectMapper();
        JacksonUtils.fillDefaultProperty(mapper);
        JacksonUtils.adapterLocalDate(mapper);
        if (includeNotNull) {
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        }
        return mapper;
    }

    public static ObjectMapper createEnableTypingObjectMapper() {
        ObjectMapper objectMapper = createObjectMapper(false);
        JacksonUtils.enableDefaultTyping(objectMapper);
        return objectMapper;
    }

    /**
     * @return
     */
    public static TypeFactory getTypeFactory() {
        return mapper.getTypeFactory();
    }

    /**
     * @return
     */
    public static ObjectNode createObjectNode() {
        return mapper.createObjectNode();
    }

}
