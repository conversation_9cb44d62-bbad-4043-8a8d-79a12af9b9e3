package cn.genn.core.utils.http.model;

import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;

import java.io.Closeable;

/**
 * <AUTHOR>
 */
public interface HttpResponse extends Closeable {

    /**
     * 获取响应状态码
     *
     * @return
     */
    int getCode();

    /**
     * 获取头
     *
     * @return
     */
    String getHeader(String name);

    /**
     * 获取响应
     *
     * @return
     */
    String string();

    /**
     * 获取响应
     *
     * @return
     */
    byte[] bytes();

    default <T> T toObj(Class<T> clazz) {
        return JsonUtils.parse(string(), clazz);
    }

    default <T> T toObj(Class<?> genericClazz, Class<?> paramClazz) {
        return JsonUtils.parse(string(), JsonUtils.constructGenericType(genericClazz, paramClazz), true);
    }

    default <T> T toObj(JavaType javaType) {
        return JsonUtils.parse(string(), javaType, true);
    }

    default <T> T toObj(TypeReference<T> typeReference) {
        return JsonUtils.parse(string(), typeReference);
    }


}
