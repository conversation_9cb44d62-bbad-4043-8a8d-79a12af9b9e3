package cn.genn.core.utils.thread.decorator;

import cn.genn.core.context.BaseMethodContext;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;

/**
 * <AUTHOR>
 */
public class MethodRequestContextDecorator extends BaseThreadDecorator {

    public MethodRequestContextDecorator() {
        super();
    }

    public MethodRequestContextDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }

    @Override
    protected Object beforeExecOnCurrThread() {
        return BaseMethodContext.get();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof BaseMethodContext) {
            BaseMethodContext.set(JsonUtils.clone(object, BaseMethodContext.class));
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        BaseMethodContext.clear();
    }
}
