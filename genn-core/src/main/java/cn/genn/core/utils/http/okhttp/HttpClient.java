package cn.genn.core.utils.http.okhttp;

import lombok.Getter;
import okhttp3.*;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class HttpClient {

    @Getter
    private OkHttpClient client;

    private OkHttpClientConfig config;

    public HttpClient() {
        this(new OkHttpClientConfig());
    }

    public HttpClient(OkHttpClientConfig config) {
        if (config == null) {
            config = new OkHttpClientConfig();
        }
        this.config = config;
        init();
    }

    public HttpClient init() {
        X509TrustManager x509TrustManager = buildTrustManager();
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(config.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(config.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(config.getReadTimeout(), TimeUnit.MILLISECONDS)
                .connectionPool(new ConnectionPool(
                        config.getMaxIdleConnections(),
                        config.getKeepAliveDuration(),
                        config.getKeepAliveTimeUnit()))
                .followRedirects(config.isFollowRedirects())  //禁制OkHttp的重定向操作，我们自己处理重定向
                .followSslRedirects(config.isFollowSslRedirects())
                .cookieJar(config.getCookieJar())
                .sslSocketFactory(createSSLSocketFactory(x509TrustManager), x509TrustManager)
                .hostnameVerifier((hostName, session) -> true)
                .retryOnConnectionFailure(config.isRetryOnConnectionFailure());
        if (config.getInterceptors() != null) {
            config.getInterceptors().stream().filter(Objects::nonNull).forEach(builder::addInterceptor);
        }
        if (config.getNetworkInterceptors() != null) {
            config.getNetworkInterceptors().stream().filter(Objects::nonNull).forEach(builder::addNetworkInterceptor);
        }
        if (config.getExecutorService() != null) {
            builder.dispatcher(new Dispatcher(config.getExecutorService()));
        }
        this.client = builder.build();
        return this;
    }

    private SSLSocketFactory createSSLSocketFactory(X509TrustManager x509TrustManager) {
        SSLSocketFactory ssfFactory = null;
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, new TrustManager[]{x509TrustManager}, new SecureRandom());
            ssfFactory = sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ssfFactory;
    }

    private X509TrustManager buildTrustManager() {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
    }

    public OkHttpClient getOkHttpClient() {
        return client;
    }

    /**
     * 同步请求
     *
     * @param request
     * @return
     * @throws IOException
     */
    public Response sync(Request request) throws IOException {
        return client.newCall(request).execute();
    }

    /**
     * 异步请求
     *
     * @param request
     * @param responseCallback
     */
    public void async(Request request, Callback responseCallback) {
        client.newCall(request).enqueue(responseCallback);
    }

}
