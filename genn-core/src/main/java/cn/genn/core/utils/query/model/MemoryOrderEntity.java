package cn.genn.core.utils.query.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MemoryOrderEntity {

    private String column;

    private MemoryOrderType orderBy;

    public static MemoryOrderEntity build(String column, MemoryOrderType orderBy) {
        MemoryOrderEntity orderEntity = new MemoryOrderEntity();
        orderEntity.setColumn(column);
        orderEntity.setOrderBy(orderBy);
        return orderEntity;
    }
}
