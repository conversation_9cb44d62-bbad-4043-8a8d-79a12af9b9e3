package cn.genn.core.utils.http.okhttp;

import jakarta.validation.constraints.NotNull;
import okhttp3.Cookie;
import okhttp3.CookieJar;
import okhttp3.HttpUrl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class LocalCookieJar implements CookieJar {

    private Map<String, List<Cookie>> cookieMap = new ConcurrentHashMap<>();

    @NotNull
    @Override
    public List<Cookie> loadForRequest(@NotNull HttpUrl httpUrl) {
        return cookieMap.getOrDefault(httpUrl.host(), new ArrayList<>());
    }

    @Override
    public void saveFromResponse(@NotNull HttpUrl httpUrl, @NotNull List<Cookie> list) {
        cookieMap.put(httpUrl.host(), list);
    }
}
