package cn.genn.core.model.page;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 分页结果类
 */
@Data
@NoArgsConstructor
public class PageResultDTO<T> implements Serializable {
    private static final long serialVersionUID = 4028133477241928793L;
    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer totalPages;
    /**
     * 记录总条数
     */
    private Long total;

    /**
     * 当前页数据对象列表
     */
    private List<T> list;

    /**
     * @param pageNo        当前页码
     * @param pageSize      每页条数
     * @param total 记录总条数
     * @param list          当前页数据对象列表
     */
    public PageResultDTO(int pageNo, int pageSize, long total, List<T> list) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list == null ? new ArrayList<>() : list;
        if (this.pageSize > 0) {
            this.totalPages = (int) (total / this.pageSize + (total % this.pageSize == 0L ? 0 : 1));
        } else {
            this.totalPages = 0;
        }
    }

    /**
     * @param response
     */
    public PageResultDTO(PageResultDTO<T> pageResult) {
        this(pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal(), pageResult.getList());
    }

    /**
     *
     * @param response
     * @param list
     * @param <E>
     */
    public <E> PageResultDTO(PageResultDTO<E> response, List<T> list) {
        this(response.getPageNo(), response.getPageSize(), response.getTotal(), list);
    }

    public PageResultDTO<T> forEach(Consumer<? super T> action) {
        if (!CollUtil.isEmpty(this.list)) {
            this.list.forEach(action);
        }
        return this;
    }
    public <R> PageResultDTO<R> convert(Function<T, R> converter) {
        if (CollUtil.isEmpty(this.list)) {
            return empty(pageNo, pageSize);
        }
        return new PageResultDTO<>(pageNo, pageSize, this.total, list.stream()
                .map(converter)
                .collect(Collectors.toList()));
    }

    public <R> PageResultDTO<R> filterAndConvert(Predicate<? super T> predicate, Function<T, R> converter) {
        if (CollUtil.isEmpty(this.list)) {
            return empty(pageNo, pageSize);
        }
        return new PageResultDTO<>(pageNo, pageSize, this.total, list.stream()
                .filter(predicate)
                .map(converter)
                .collect(Collectors.toList()));
    }

    public <R> PageResultDTO<R> convertAndFilter(Function<T, R> converter, Predicate<? super R> predicate) {
        if (CollUtil.isEmpty(this.list)) {
            return empty(pageNo, pageSize);
        }
        return new PageResultDTO<>(pageNo, pageSize, this.total, list.stream()
                .map(converter)
                .filter(predicate)
                .collect(Collectors.toList()));
    }

    public static <T> PageResultDTO<T> empty() {
        return new PageResultDTO<>(0, 0, 0, new ArrayList<>());
    }

    public static <T> PageResultDTO<T> empty(int page, int size) {
        return new PageResultDTO<>(page, size, 0, new ArrayList<>());
    }

    public static <T> PageResultDTO<T> empty(PageResultDTO<T> response) {
        return new PageResultDTO<>(response.getPageNo(), response.getPageSize(), 0, new ArrayList<>());
    }

    public static <T> PageResultDTO<T> buildPageResult(List<T> records, Integer pageNo, Integer pageSize) {
        if (pageNo == null) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        int totalPage = records.size() / pageSize + (records.size() % pageSize == 0 ? 0 : 1);
        List<T> currList;
        if (pageNo > totalPage) {
            currList = Collections.emptyList();
        } else {
            currList = records.subList((pageNo - 1) * pageSize, Math.min(pageNo * pageSize, records.size()));
        }
        PageResultDTO<T> pageResultDTO = new PageResultDTO<>();
        pageResultDTO.setPageSize(pageSize);
        pageResultDTO.setPageNo(pageNo);
        pageResultDTO.setTotalPages(totalPage);
        pageResultDTO.setTotal((long) records.size());
        pageResultDTO.setList(currList);
        return pageResultDTO;
    }
}
