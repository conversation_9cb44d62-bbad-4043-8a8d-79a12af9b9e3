package cn.genn.core.model.res;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ResponseResult<E> {

    private boolean success;
    private String traceid;
    private String code;
    private String msg;
    private String err;
    private E data;
    private long timestamp = System.currentTimeMillis();

    public ResponseResult() {

    }

    public ResponseResult(String code) {
        this.code = code;
    }

    public ResponseResult(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResponseResult(String code, E data) {
        this.code = code;
        this.data = data;
    }

    public ResponseResult(String code, String msg, E data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

}
