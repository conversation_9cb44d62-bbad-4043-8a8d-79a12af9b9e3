package cn.genn.core.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum BooleanTypeEnum {

    FALSE(0, "否"),
    TRUE(1, "是");


    @EnumValue
    @JsonValue
    @Getter
    private final Integer code;

    @Getter
    private final String description;

    BooleanTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
