package cn.genn.core.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum TransProductCodeEnum {

    /**
     * 产品类型
     */
    IAM("IAM", "网关服务"),
    OPS("OPS", "OPS核心服务"),
    TMS("TMS", "TMS核心服务"),
    BMS("BMS", "BMS核心服务"),
    PMS("PMS", "平台管理中心"),
    GENN_HUB("GENN_HUB","API开放平台"),
    HOS("HOS", "零售管理系统"),
    BOS("BOS", "站端管理系统"),
    POS("POS", "POS管理系统"),
    DSM("DSM", "双屏机系统"),
    FCS("FCS", "车队卡管理系统"),
    MCS("MCS", "会员卡管理系统"),
    ;

    private final String code;
    private final String desc;

    TransProductCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TransProductCodeEnum getByCode(String code) {
        for (TransProductCodeEnum value : TransProductCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }



}
