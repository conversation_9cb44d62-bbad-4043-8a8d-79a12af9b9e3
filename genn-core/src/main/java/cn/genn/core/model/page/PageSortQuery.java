package cn.genn.core.model.page;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PageSortQuery implements Serializable {

    private static final long serialVersionUID = 441571532453795073L;

    @Parameter(description = "当前页")
    private Integer pageNo = 1;
    @Parameter(description = "每页大小")
    private Integer pageSize = 10;
    @Parameter(description = "排序字段(json格式), {\"orderBy\":{\"id\":\"asc\"}}")
    private OrderBy sort;
}
