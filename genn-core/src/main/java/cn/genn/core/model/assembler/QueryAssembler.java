package cn.genn.core.model.assembler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.genn.core.model.page.PageResultDTO;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface QueryAssembler<QUERY, PO, DTO> {

    DTO PO2DTO(PO po);

    List<DTO> PO2DTO(List<PO> poList);

    PO query2PO(QUERY query);

    List<PO> query2PO(List<QUERY> queryList);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<DTO> toPageResult(IPage<PO> poPage);
}
