package cn.genn.core.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum DeletedTypeEnum {

    NOT_DELETED(0, "未删除"),
    DELETED(1, "已删除");


    @EnumValue
    @JsonValue
    @Getter
    private final Integer code;

    @Getter
    private final String description;

    DeletedTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
