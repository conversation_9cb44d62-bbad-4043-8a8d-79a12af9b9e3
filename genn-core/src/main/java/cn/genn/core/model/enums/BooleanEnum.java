package cn.genn.core.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 已过期,建议使用 BooleanTypeEnum
 * <AUTHOR>
 */
public enum BooleanEnum {

    FALSE(0, "否"),
    TRUE(1, "是");


    @EnumValue
    @JsonValue
    @Getter
    private final int code;

    @Getter
    private final String description;

    BooleanEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, BooleanEnum> VALUES = new HashMap<>();
    static {
        for (final BooleanEnum item : BooleanEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static BooleanEnum of(int code) {
        return VALUES.get(code);
    }
}
