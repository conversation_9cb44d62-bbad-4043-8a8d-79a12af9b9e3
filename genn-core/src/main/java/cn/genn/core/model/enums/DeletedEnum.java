package cn.genn.core.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 已过期,建议使用 DeletedTypeEnum
 * <AUTHOR>
 */
public enum DeletedEnum {

    NOT_DELETED(0, "未删除"),
    DELETED(1, "已删除");


    @EnumValue
    @JsonValue
    @Getter
    private final int code;

    @Getter
    private final String description;

    DeletedEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, DeletedEnum> VALUES = new HashMap<>();
    static {
        for (final DeletedEnum item : DeletedEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static DeletedEnum of(int code) {
        return VALUES.get(code);
    }
}
