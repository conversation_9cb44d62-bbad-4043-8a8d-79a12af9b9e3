package cn.genn.core.model.page;

import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class OrderBy implements Serializable {

    private static final long serialVersionUID = -3624833082440135979L;

    private Map<String, SortOrder> orderBy;

    public OrderBy() {
        orderBy = new LinkedHashMap<>();
    }

    public OrderBy orderBy(String field, String direction) {
        orderBy.put(field, SortOrder.of(direction));
        return this;
    }

    public OrderBy orderBy(String field, SortOrder direction) {
        orderBy.put(field, direction);
        return this;
    }

    public OrderBy orderByAsc(String field) {
        orderBy.put(field, SortOrder.ASC);
        return this;
    }

    public OrderBy orderByDesc(String field) {
        orderBy.put(field, SortOrder.DESC);
        return this;
    }
}
