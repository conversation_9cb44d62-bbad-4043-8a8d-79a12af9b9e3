package cn.genn.core.model;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 简单的kv结构定义
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KVStruct<K, V> implements Serializable {
    private static final long serialVersionUID = -4577991679902688384L;

    private K key;

    private V value;

    public static <K, V> KVStruct<K, V> of(K key, V value) {
        return new KVStruct<>(key, value);
    }

    public static <T> List<KVStruct<String, String>> ofString(
            List<T> dataList,
            Function<T, Object> idMapper,
            Function<T, String> nameMapper) {
        return of(dataList,
                item -> String.valueOf(idMapper.apply(item)),
                nameMapper);
    }

    public static <T> List<KVStruct<Object, String>> ofObject(
            List<T> dataList,
            Function<T, Object> idMapper,
            Function<T, String> nameMapper) {
        return of(dataList, idMapper, nameMapper);
    }

    public static <T, K, V> List<KVStruct<K, V>> of(
            List<T> dataList,
            Function<T, K> keyMapper,
            Function<T, V> valueMapper) {
        if (CollUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        return dataList.stream()
                .map(item -> KVStruct.of(keyMapper.apply(item), valueMapper.apply(item)))
                .collect(Collectors.toList());
    }
}
