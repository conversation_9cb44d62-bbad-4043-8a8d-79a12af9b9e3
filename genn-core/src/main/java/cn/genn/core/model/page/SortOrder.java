package cn.genn.core.model.page;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 */
public enum SortOrder {

    /** 升序 */
    ASC,
    /** 降序 */
    DESC;

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static SortOrder of(String value) throws IllegalArgumentException{

        try {
            return SortOrder.valueOf(value.toUpperCase());
        } catch (Exception e) {
            throw new IllegalArgumentException(MessageFormat.format(
                    "Invalid value [{}] for orders given! Has to be either 'desc' or 'asc' (case insensitive).", value), e);
        }
    }
}
