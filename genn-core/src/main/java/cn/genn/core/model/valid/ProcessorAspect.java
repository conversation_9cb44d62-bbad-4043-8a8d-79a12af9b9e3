package cn.genn.core.model.valid;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.aop.support.AopUtils;
import org.springframework.core.ResolvableType;
import org.springframework.core.annotation.AnnotationUtils;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
@Aspect
@Slf4j
public class ProcessorAspect {

    @Pointcut("@annotation(cn.genn.core.model.valid.Processor)")
    public void processor() {
    }

    @Around("processor()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        //通过切点获取注解中的方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取注解中的方法
        Method method = signature.getMethod();
        Method targetMethod = AopUtils.getMostSpecificMethod(method, joinPoint.getTarget().getClass());
        //再获取被注解的类携带的参数
        Object[] args = joinPoint.getArgs();
        if (null == args || args.length == 0) {
            return joinPoint.proceed();
        }
        Processor annotation = AnnotationUtils.findAnnotation(targetMethod, Processor.class);
        if (annotation == null) {
            return joinPoint.proceed();
        }
        Class<? extends IProcessor>[] beforeProcessors = annotation.before();
        doBeforeProcessor(args[0], beforeProcessors);

        Object returnArgs = joinPoint.proceed();
        Class<? extends IProcessor>[] afterProcessors = annotation.after();

        doAfterProcessor(args[0], afterProcessors, returnArgs, targetMethod.getReturnType());
        return returnArgs;
    }

    private void doBeforeProcessor(Object args, Class<? extends IProcessor>[] beforeProcessors) {
        for (Class<? extends IProcessor> processor : beforeProcessors) {
            doProcessor(args, processor);
        }
    }
    private void doAfterProcessor(Object args, Class<? extends IProcessor>[] afterProcessors, Object returnArgs, Class<?> returnType) {
        for (Class<? extends IProcessor> processor : afterProcessors) {
            Class paramType = ResolvableType.forType(processor.getGenericInterfaces()[0]).as(IProcessor.class).getGeneric(0).resolve();
            if (returnArgs != null && returnType != null && paramType!=null && returnType.getTypeName().equals(paramType.getTypeName())) {
                doProcessor(returnArgs, processor);
            } else {
                doProcessor(args, processor);
            }
        }
    }

    private void doProcessor(Object args, Class<? extends IProcessor> processor) {
        IProcessor iProcessor = SpringUtil.getBean(processor);
        iProcessor.process(args);
    }

}
