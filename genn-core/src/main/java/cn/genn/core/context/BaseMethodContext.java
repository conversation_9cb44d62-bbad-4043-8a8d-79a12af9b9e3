package cn.genn.core.context;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class BaseMethodContext {

    private final Map<String, Object> attachments = new HashMap<>();

    @Getter
    @Setter
    private Long userId;

    @Getter
    @Setter
    private String userName;

    @Getter
    @Setter
    private Long tenantId;

    public Object getAttachment(String key) {
        return attachments.get(key);
    }

    public void setAttachment(String key, Object value) {
        attachments.put(key, value);
    }

    private static final ThreadLocal<BaseMethodContext> HOLDER = new ThreadLocal<>();

    public static void set(BaseMethodContext context) {
        HOLDER.set(context);
    }

    public static BaseMethodContext get() {
        return HOLDER.get();
    }

    public static void clear() {
        HOLDER.remove();
    }
}
