package cn.genn.core.context;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 请求上下文
 * 子系统继承该类,作为上下文使用
 * 生命周期为一次请求
 *
 * <AUTHOR>
 */
public class BaseRequestContext {

    @JsonIgnore
    private static final ThreadLocal<BaseRequestContext> HOLDER = new ThreadLocal<>();

    /**
     * 用户id
     */
    @Getter
    @Setter
    protected Long userId;

    /**
     * 用户名
     */
    @Getter
    @Setter
    private String userName;

    /**
     * 租户id
     */
    @Getter
    @Setter
    private Long tenantId;

    /**
     * web 环境下请求头
     */
    @Getter
    @Setter
    private Map<String, String> requestHeader = new HashMap<>();

    /**
     * gtrace链路追踪请求参数
     */
    @Getter
    @Setter
    private String gtraceId;


    protected Map<String, Object> attachment = new HashMap<>();

    public static void set(BaseRequestContext context) {
        HOLDER.set(context);
    }

    public static BaseRequestContext get() {
        return HOLDER.get();
    }

    public static void putAttachment(String key, Object value) {
        get().attachment.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getAttachment(String key, Class<T> cls) {
        Object obj = get().attachment.get(key);
        if (obj == null) {
            return null;
        }
        return (T) obj;
    }

    public static void clear() {
        HOLDER.remove();
    }


    /**
     * 在创建之前执行
     */
    public void beforeSet() {
        // 子类覆盖
    }

    /**
     * 在创建之后执行
     */
    public void afterSet() {
        // 子类覆盖
    }

    /**
     * 在清除之前执行
     */
    public void beforeClear() {
        // 子类覆盖
    }

    /**
     * 在清除之后执行
     */
    public void afterClear() {
        // 子类覆盖
    }

}
