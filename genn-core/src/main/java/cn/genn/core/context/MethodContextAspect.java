package cn.genn.core.context;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Aspect
public class MethodContextAspect {

    @Around(value = "@annotation(methodContext)")
    public Object methodContextProxy(ProceedingJoinPoint joinPoint, MethodContext methodContext) throws Throwable {
        try {
            BaseMethodContext.set(new BaseMethodContext());
            return joinPoint.proceed();
        }finally {
            BaseMethodContext.clear();
        }
    }
}
