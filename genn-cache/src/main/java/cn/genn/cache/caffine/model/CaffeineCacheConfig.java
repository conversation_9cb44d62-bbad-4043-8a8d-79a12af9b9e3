package cn.genn.cache.caffine.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Caffeine缓存配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CaffeineCacheConfig {
    /**
     * 存活时间，单位：秒，从创建时刻开始计算。
     * 即expireAfterWrite 策略,如果需要使用expireAfterAccess策略,请使用cacheSpec配置覆盖
     */
    private Integer ttl;

    /**
     * caffeine缓存的其他配置
     *
     * initialCapacity=: 缓存的初始容量。
     * maximumSize=: 缓存的最大条目数。
     * maximumWeight=: 缓存的最大权重，与weigher配置项一起使用。
     * expireAfterAccess=: 最后一次访问后经过固定时间过期。
     * expireAfterWrite=: 写入后经过固定时间过期。
     * refreshAfterWrite=: 写入后经过固定时间刷新。
     * weakKeys: 使用弱引用存储键。
     * softValues: 使用软引用存储值。
     * recordStats: 开启缓存统计。
     * duration的格式为数字后跟时间单位，支持的时间单位有d（天）、h（小时）、m（分钟）、s（秒）。
     *
     * 例如:initialCapacity=100,maximumSize=1000,expireAfterWrite=10m,recordStats
     */
    private String cacheSpec;

}