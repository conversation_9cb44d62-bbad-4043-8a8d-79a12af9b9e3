package cn.genn.cache.caffine.component;

import cn.genn.cache.caffine.model.CaffeineCacheConfig;
import cn.genn.cache.common.AbstractMultiCacheManager;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.CaffeineSpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;

import java.util.List;
import java.util.Map;

/**
 * 基于caffeine的多缓存管理器
 * <AUTHOR>
 */
@Slf4j
public class CaffeineMultiCacheManager extends AbstractMultiCacheManager<CaffeineCacheConfig> {
    private static final String KEY_EXPIRE_AFTER_WRITE = "expireAfterWrite";
    private static final String KEY_EXPIRE_AFTER_ACCESS = "expireAfterAccess";

    public CaffeineMultiCacheManager(CaffeineCacheConfig defaultCaffeineCacheConfig, Map<String, CaffeineCacheConfig> cacheConfigs) {
        super(defaultCaffeineCacheConfig);
        initialCacheManagerMap(cacheConfigs);
    }

    @Override
    protected void mergerCacheConfig(CaffeineCacheConfig cacheConfig, CaffeineCacheConfig defaultCacheConfig) {
        if (cacheConfig.getTtl() == null || cacheConfig.getTtl() < 0) {
            cacheConfig.setTtl(defaultCacheConfig.getTtl());
        }
        if (StrUtil.isEmpty(cacheConfig.getCacheSpec())) {
            cacheConfig.setCacheSpec(defaultCacheConfig.getCacheSpec());
        }
    }

    @Override
    protected boolean isValid(CaffeineCacheConfig cacheConfig) {
        return cacheConfig.getTtl() >= 0;
    }

    @Override
    protected CacheManager createCacheManager(List<String> names, CaffeineCacheConfig cacheConfig) {
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        if (names != null) {
            caffeineCacheManager.setCacheNames(names);
        }
        String cacheSpec = cacheConfig == null ? null : cacheConfig.getCacheSpec();
        Integer ttl = cacheConfig == null ? null : cacheConfig.getTtl();
        if (ttl != null) {
            if (StrUtil.isEmpty(cacheSpec)) {
                cacheSpec = getTtlSpecString(ttl);
            } else if (!cacheSpec.contains(KEY_EXPIRE_AFTER_WRITE) && !cacheSpec.contains(KEY_EXPIRE_AFTER_ACCESS)) {
                cacheSpec = cacheSpec + "," + getTtlSpecString(ttl);
            }
        }
        if (StrUtil.isNotEmpty(cacheSpec)) {
            caffeineCacheManager.setCaffeineSpec(CaffeineSpec.parse(cacheSpec));
        }
        log.info("Create CaffeineCacheManager with names={}, cacheSpec='{}', ttl={}", names, cacheSpec, ttl);
        return caffeineCacheManager;
    }

    private String getTtlSpecString(Integer ttl) {
        return KEY_EXPIRE_AFTER_WRITE + "=" + ttl + "s";
    }

}
