package cn.genn.cache.caffine.component;

import cn.genn.cache.common.CacheClient;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 基于Caffeine的缓存客户端
 * <AUTHOR>
 */
public class CaffeineCacheClient implements CacheClient {

    private final Cache<String, Object> cache;

    public CaffeineCacheClient(long duration, int maxSumSize) {
        cache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.SECONDS)
                .maximumSize(maxSumSize)
                .build();
    }

    @Override
    public void set(String key, Object value) {
        cache.put(key, value);
    }

    @Override
    public void set(String key, Object value, int expireTime, TimeUnit expireTimeUnit) {
        cache.put(key, value);
    }

    @Override
    public Object get(String key) {
        return cache.getIfPresent(key);
    }

    @Override
    public void del(String key) {
        cache.invalidate(key);
    }

    @Override
    public Long setAdd(String key, Object... values) {
        throw new UnsupportedOperationException("Caffeine不直接支持Set操作");
    }

    @Override
    public Set<Object> setMembers(String key) {
        throw new UnsupportedOperationException("Caffeine不直接支持Set操作");
    }
}
