package cn.genn.cache.common;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 支持多缓存配置的缓存管理器
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractMultiCacheManager<T> implements CacheManager {

    private ConcurrentMap<String, CacheManager> cacheManagerMap = new ConcurrentHashMap<>();

    /**
     * 默认CacheManager
     */
    private T defaultCacheConfig;

    public AbstractMultiCacheManager(T defaultCacheConfig) {
        this.defaultCacheConfig = defaultCacheConfig;
    }

    @Override
    public Cache getCache(String name) {
        if (StrUtil.isEmpty(name)) {
            return null;
        }
        CacheManager manager = cacheManagerMap.get(name);
        return manager == null ? null : manager.getCache(name);
    }

    @Override
    public Collection<String> getCacheNames() {
        return new ArrayList<>(cacheManagerMap.keySet());
    }

    /***
     * 子类调用
     * @param cacheConfigs
     */
    public void initialCacheManagerMap(Map<String, T> cacheConfigs) {
        if (CollectionUtils.isEmpty(cacheConfigs)) {
            return;
        }
        cacheConfigs.forEach((name, cacheConfig) -> {
            List<String> names = parseName(name);
            if (CollectionUtils.isEmpty(names)) {
                return;
            }
            T finalCacheConfig = getCacheConfig(cacheConfig);
            if(!isValid(finalCacheConfig)) {
                return;
            }
            CacheManager manager = createCacheManager(names, finalCacheConfig);
            if (manager == null) {
                return;
            }
            names.forEach(key -> cacheManagerMap.put(key, manager));
        });
    }

    private T getCacheConfig(T cacheConfig) {
        if (cacheConfig == null) {
            return defaultCacheConfig;
        }
        if (defaultCacheConfig != null) {
            mergerCacheConfig(cacheConfig, defaultCacheConfig);
        }
        return cacheConfig;
    }

    /**
     * 验证缓存配置的有效性,无效将不会添加到cacheManagerMap中
     */
    protected abstract boolean isValid(T cacheConfig);

    /**
     * 合并缓存配置
     * 在初始化缓存的时候,会将传入的缓存与默认缓存合并.具体合并策略取决于子类.
     */
    protected abstract void mergerCacheConfig(T cacheConfig, T defaultCacheConfig);

    /**
     * 创建CacheManager
     */
    protected abstract CacheManager createCacheManager(List<String> names, T cacheConfig);

    private static List<String> parseName(String name) {
        return Stream.of(StringUtils.commaDelimitedListToStringArray(name))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }
}
