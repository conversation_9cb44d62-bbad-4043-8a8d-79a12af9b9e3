package cn.genn.cache.common;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public interface CacheClient {

    void set(String key, Object value);

    void set(String key, Object value, int expireTime, TimeUnit expireTimeUnit);

    Object get(String key);

    void del(String key);

    Long setAdd(String key, Object... values);

    Set<Object> setMembers(String key);
}
