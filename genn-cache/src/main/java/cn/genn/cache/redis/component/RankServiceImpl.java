package cn.genn.cache.redis.component;

import cn.genn.cache.redis.model.RankScore;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class RankServiceImpl implements RankService {
    private StringRedisTemplate redisTemplate;
    private Function<String, String> keyStrategy;

    public RankServiceImpl(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.keyStrategy = k -> k;
    }

    public RankServiceImpl(StringRedisTemplate redisTemplate, Function<String, String> keyStrategy) {
        this.redisTemplate = redisTemplate;
        this.keyStrategy = keyStrategy;
    }

    @Override
    public void put(String key, String value, double score) {
        String cacheKey = getEnvCacheKey(key);
        redisTemplate.opsForZSet().add(cacheKey, value, score);
    }

    @Override
    public void put(String key, Set<ZSetOperations.TypedTuple<String>> tuples) {
        String cacheKey = getEnvCacheKey(key);
        redisTemplate.opsForZSet().add(cacheKey, tuples);
    }

    @Override
    public void remove(String key, String... values) {
        String cacheKey = getEnvCacheKey(key);
        redisTemplate.opsForZSet().remove(cacheKey, values);
    }

    @Override
    public List<RankScore> rankList(String key, long start, long end) {
        String cacheKey = getEnvCacheKey(key);
        Set<ZSetOperations.TypedTuple<String>> set = redisTemplate.opsForZSet().reverseRangeWithScores(cacheKey, start, end);
        List<RankScore> list = new ArrayList<>();
        if (set != null) {
            int i = 1;
            for (ZSetOperations.TypedTuple<String> x : set) {
                list.add(new RankScore(x.getValue(), x.getScore() == null ? 0 : x.getScore(), start + i));
                i++;
            }
        }
        return list;
    }

    @Override
    public RankScore getRank(String key, String value) {
        String cacheKey = getEnvCacheKey(key);
        Long rank = redisTemplate.opsForZSet().reverseRank(cacheKey, value);
        Double score = null;
        if (rank != null) {
            score = redisTemplate.opsForZSet().score(cacheKey, value);
        }
        return new RankScore(value, score == null ? 0.0 : score, rank == null ? null : rank + 1);
    }

    @Override
    public void removeRankAfter(String key, long maxRank) {
        String cacheKey = getEnvCacheKey(key);
        Set<String> list = redisTemplate.opsForZSet().reverseRange(cacheKey, maxRank, maxRank);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Long rank = redisTemplate.opsForZSet().rank(cacheKey, new ArrayList<>(list).get(0));
        if (rank == null) {
            return;
        }
        long batchSize = 1000;
        long end;
        do {
            end = Math.min(rank, batchSize - 1);
            redisTemplate.opsForZSet().removeRange(cacheKey, 0L, end);
            rank -= batchSize;
        } while (rank >= 0);
    }

    @Override
    public long size(String key) {
        String cacheKey = getEnvCacheKey(key);
        return redisTemplate.opsForZSet().size(cacheKey);
    }

    @Override
    public void renameKey(String oldKey, String newKey) {
        redisTemplate.rename(getEnvCacheKey(oldKey), getEnvCacheKey(newKey));
    }

    private String getEnvCacheKey(String key) {
        return keyStrategy.apply(key);
    }

}
