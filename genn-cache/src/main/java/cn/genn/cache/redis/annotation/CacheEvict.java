package cn.genn.cache.redis.annotation;

import cn.genn.cache.common.CacheClient;
import cn.genn.cache.redis.component.RedisCacheClient;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(CacheMultiEvict.class)
public @interface CacheEvict {

    /**
     * 缓存key
     *
     * @return
     */
    String value();

    /**
     * 缓存fieldKey
     *
     * @return
     */
    String fieldKey() default "";

    /**
     * 缓存条件 spel表达式
     *
     * @return
     */
    String condition() default "";

    /**
     * 是否集合
     *
     * @return
     */
    boolean collection() default false;

    /**
     * 是否分组key
     *
     * 分组key时将会将组内所有key全部删除
     *
     * @return
     */
    boolean group() default false;

    /**
     * 集合时二级键值，此处rootObject为循环对象。
     *
     * 在SpEL中访问root对象的属性时，不需要加#前缀
     * 例如：foo.bar 访问rootObject的foo属性的bar属性。
     *
     * @return
     */
    String subFieldKey() default "";

    Class<? extends CacheClient> cacheClient() default RedisCacheClient.class;
}
