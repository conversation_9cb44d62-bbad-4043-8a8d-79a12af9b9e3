package cn.genn.cache.redis.annotation;

import cn.genn.cache.common.CacheClient;
import cn.genn.cache.redis.component.RedisCacheClient;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Cache {

    /**
     * 缓存key
     *
     * @return
     */
    String value();

    /**
     * 缓存fieldKey
     *
     * @return
     */
    String fieldKey() default "";

    /**
     * 缓存条件，支持SpEL表达式，方法执行前判断
     *
     * @return
     */
    String condition() default "";

    /**
     *
     * 缓存条件，支持SpEL表达式，方法执行后判断
     *
     * 可以使用 #result 使用结果集
     *
     * @return
     */
    String unless() default "";

    /**
     * key的过期时间，单位秒  <0 使用默认过期时间 =0 永久不过期
     * @return
     */
    int expireTime() default -1;

    /**
     * 是否缓存空结果
     *
     * @return
     */
    boolean cacheNull() default false;

    /**
     * 定义缓存所属分组
     *
     * 该key缓存该分组所有key，清理时可以指定group清理
     *
     * @return
     */
    String group() default "";

    /**
     * 指定缓存客户端,默认为redis
     * @return
     */
    Class<? extends CacheClient> cacheClient() default RedisCacheClient.class;
}
