package cn.genn.cache.redis.component;

import cn.genn.cache.common.CacheClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class RedisCacheClient implements CacheClient {

    public RedisTemplate<String, Object> redisTemplate;

    public ValueOperations<String, Object> valueOps;

    public SetOperations<String, Object> setOpts;

    public RedisCacheClient(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.valueOps = redisTemplate.opsForValue();
        this.setOpts = redisTemplate.opsForSet();
    }

    @Override
    public void set(String key, Object value) {
        this.valueOps.set(key, value);
    }

    @Override
    public void set(String key, Object value, int expireTime, TimeUnit expireTimeUnit) {
        this.valueOps.set(key, value, expireTime, expireTimeUnit);
    }

    @Override
    public Object get(String key) {
        return this.valueOps.get(key);
    }

    @Override
    public void del(String key) {
        this.redisTemplate.delete(key);
    }

    @Override
    public Long setAdd(String key, Object... values) {
        return setOpts.add(key, values);
    }

    @Override
    public Set<Object> setMembers(String key) {
        return setOpts.members(key);
    }
}
