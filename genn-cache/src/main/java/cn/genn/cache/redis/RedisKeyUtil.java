package cn.genn.cache.redis;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RedisKeyUtil {

	public final static String split = ":";

	public static String buildKey(String prefix, Object... obj) {
		StringBuilder sb = new StringBuilder(prefix);
		for (Object o : obj) {
			sb.append(split);
			sb.append(o);
		}
		return sb.toString();
	}

	public static <T> Map<T, String> buildMultiKey(String prefix, List<T> list) {
		Map<T, String> keyMap = new HashMap<T, String>();
		for (T t : list) {
			StringBuilder sb = new StringBuilder();
			sb.append(prefix);
			sb.append(split);
			sb.append(t);
			keyMap.put(t, sb.toString());
		}
		return keyMap;
	}

}
