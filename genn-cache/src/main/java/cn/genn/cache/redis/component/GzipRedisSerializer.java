package cn.genn.cache.redis.component;

import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * <AUTHOR>
 * gzip压缩算法实现的redis序列化器
 * 压缩速度比lz4慢，压缩率高
 */
@Slf4j
public class GzipRedisSerializer<T> implements RedisSerializer<T> {

    public static final int BUFFER_SIZE = 4096;
    private final RedisSerializer<T> innerSerializer;

    public GzipRedisSerializer(RedisSerializer<T> innerSerializer) {
        this.innerSerializer = innerSerializer;
    }

    @Override
    public byte[] serialize(T o) throws SerializationException {
        if (o == null) {
            return new byte[0];
        }
        ByteArrayOutputStream bos = null;
        GZIPOutputStream gzip = null;
        try {
            byte[] bytes = innerSerializer.serialize(o);
            if (bytes == null) {
                return new byte[0];
            }
            log.info("Gzip serialize before size: {}", bytes.length);
            bos = new ByteArrayOutputStream();
            gzip = new GZIPOutputStream(bos);
            gzip.write(bytes);
            gzip.finish();
            byte[] res = bos.toByteArray();
            log.info("Gzip serialize after size: {}", res.length);
            return res;
        } catch (Exception e) {
            throw new SerializationException("Gzip serialize error", e);
        } finally {
            IoUtil.close(gzip);
            IoUtil.close(bos);
        }
    }

    @Override
    public T deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        ByteArrayOutputStream bos = null;
        ByteArrayInputStream bis = null;
        GZIPInputStream gzip = null;
        try {
            bos = new ByteArrayOutputStream();
            bis = new ByteArrayInputStream(bytes);
            gzip = new GZIPInputStream(bis);
            byte[] buffer = new byte[BUFFER_SIZE];
            int n;
            while ((n = gzip.read(buffer)) >= 0) {
                bos.write(buffer, 0, n);
            }
            byte[] decompressed = bos.toByteArray();
            return innerSerializer.deserialize(decompressed);
        } catch (Exception e) {
            throw new SerializationException("Gzip deserialize error", e);
        } finally {
            IoUtil.close(gzip);
            IoUtil.close(bis);
            IoUtil.close(bos);
        }
    }
}
