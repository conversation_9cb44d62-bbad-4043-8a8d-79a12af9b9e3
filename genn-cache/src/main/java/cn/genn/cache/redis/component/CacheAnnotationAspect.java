package cn.genn.cache.redis.component;

import cn.genn.cache.common.CacheClient;
import cn.genn.cache.redis.annotation.Cache;
import cn.genn.cache.redis.annotation.CacheEvict;
import cn.genn.cache.redis.annotation.CacheMultiEvict;
import cn.genn.core.utils.ObjectUtils;
import cn.genn.core.utils.SpelUtils;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Aspect
@Slf4j
public class CacheAnnotationAspect {

    private static final String NULL_STRING = "NULL";
    private static final String VARIABLE_NAME_RESULT = "result";

    @Value("${genn.cache.enabled:true}")
    private boolean cacheEnable;

    @Value("${genn.cache.default-expired-seconds:300}")
    private int cacheExpireSeconds;

    @Value("${genn.cache.cache-key-prefix:GENN:CACHE}")
    private String cacheKeyPrefix;

    @Around("@annotation(cache)")
    public Object cacheObject(ProceedingJoinPoint joinPoint, Cache cache) throws Throwable {
        if (!cacheEnable) {
            return joinPoint.proceed();
        }
        Class<? extends CacheClient> cls = cache.cacheClient();
        CacheClient cacheClient = SpringUtil.getBean(cls);
        if (cacheClient == null) {
            log.warn("CacheClient not found:{}", cls);
            return joinPoint.proceed();
        }
        StandardEvaluationContext spelContext = SpelUtils.initSpelContext(joinPoint);
        Boolean condition = SpelUtils.parseSpelExpression(spelContext, cache.condition(), Boolean.class);
        if (condition != null && !condition) {
            return joinPoint.proceed();
        }
        String preKey = cache.value();
        String cacheKey = toKey(preKey, SpelUtils.parseSpelExpression(spelContext, cache.fieldKey(), String.class));
        String groupKey = null;
        if (StringUtils.hasText(cache.group())) {
            groupKey = toKey(preKey, SpelUtils.parseSpelExpression(spelContext, cache.group(), String.class));
        }
        try {
            Object obj = cacheClient.get(cacheKey);
            if (null != obj) {
                if (cache.cacheNull() && obj.equals(NULL_STRING)) {
                    return null;
                }
                log.debug("Read from cache key={}", cacheKey);
                return ObjectUtils.cast(obj, ((MethodSignature) joinPoint.getSignature()).getReturnType());
            }
        } catch (Exception ex) {
            log.error("Get cache error:{}", ex.getMessage());
        }
        return realTimeGet(joinPoint, cache, cacheKey, groupKey, spelContext, cacheClient);
    }

    private String toKey(String preKey, String fieldKey) {
        return cacheKeyPrefix + ":" + preKey + (fieldKey == null ? "" : (":" + fieldKey));
    }

    private Object realTimeGet(ProceedingJoinPoint joinPoint, Cache cache, String cacheKey, String groupKey, StandardEvaluationContext spelContext, CacheClient cacheClient) throws Throwable {
        Object ret = joinPoint.proceed();
        if (ret != null || cache.cacheNull()) {
            spelContext.setVariable(VARIABLE_NAME_RESULT, ret);
            Boolean unless = SpelUtils.parseSpelExpression(spelContext, cache.unless(), Boolean.class);
            if (unless == null || unless) {
                putIntoCache(cache, cacheKey, groupKey, ret, cacheClient);
            }
        }
        return ret;
    }

    private void putIntoCache(Cache cache, String cacheKey, String groupKey, Object ret, CacheClient cacheClient) {
        if (ret == null) {
            ret = NULL_STRING;
        }
        try {
            int expireTime = cache.expireTime();
            if (expireTime > 0) {
                cacheClient.set(cacheKey, ret, expireTime, TimeUnit.SECONDS);
            } else if (expireTime == 0) {
                cacheClient.set(cacheKey, ret);
            } else {
                if (cacheExpireSeconds <= 0) {
                    cacheClient.set(cacheKey, ret);
                } else {
                    cacheClient.set(cacheKey, ret, cacheExpireSeconds, TimeUnit.SECONDS);
                }
            }
            if (groupKey != null) {
                cacheClient.setAdd(groupKey, cacheKey);
            }
            log.debug("Put cache key={} expire={}s", cacheKey, expireTime);
        } catch (Exception ex) {
            log.error("Put cache key={} error:{}", cacheKey, ex.getMessage());
        }
    }

    @Around("@annotation(cacheEvict)")
    public Object cacheObjectEvict(ProceedingJoinPoint joinPoint, CacheEvict cacheEvict) throws Throwable {
        Object ret = null;
        try {
            ret = joinPoint.proceed();
            return ret;
        } finally {
            clearCache(joinPoint, cacheEvict, ret);
        }
    }

    @Around("@annotation(cacheMultiEvict)")
    public Object cacheMultiEvict(ProceedingJoinPoint joinPoint, CacheMultiEvict cacheMultiEvict) throws Throwable {
        Object ret = null;
        try {
            ret = joinPoint.proceed();
            return ret;
        } finally {
            clearMultiCache(joinPoint, cacheMultiEvict, ret);
        }
    }

    private void clearMultiCache(ProceedingJoinPoint joinPoint, CacheMultiEvict cacheMultiEvict, Object ret) {
        if (!cacheEnable) {
            return;
        }
        try {
            StandardEvaluationContext spelContext = SpelUtils.initSpelContext(joinPoint);
            spelContext.setVariable(VARIABLE_NAME_RESULT, ret);
            for (CacheEvict cacheEvict : cacheMultiEvict.value()) {
                Boolean condition = SpelUtils.parseSpelExpression(spelContext, cacheEvict.condition(), Boolean.class);
                if (condition != null && !condition) {
                    continue;
                }
                doCacheEvict(cacheEvict, spelContext, cacheEvict.value());
            }
        } catch (Exception e) {
            log.error("clear cache error", e);
        }
    }

    private void clearCache(ProceedingJoinPoint joinPoint, CacheEvict cacheEvict, Object ret) {
        if (!cacheEnable) {
            return;
        }
        Class<? extends CacheClient> cls = cacheEvict.cacheClient();
        CacheClient cacheClient = SpringUtil.getBean(cls);
        if (cacheClient == null) {
            return;
        }
        try {
            StandardEvaluationContext spelContext = SpelUtils.initSpelContext(joinPoint);
            spelContext.setVariable(VARIABLE_NAME_RESULT, ret);
            Boolean condition = SpelUtils.parseSpelExpression(spelContext, cacheEvict.condition(), Boolean.class);
            if (condition != null && !condition) {
                return;
            }
            String preKey = cacheEvict.value();
            doCacheEvict(cacheEvict, spelContext, preKey);
        } catch (Exception e) {
            log.error("clear cache error", e);
        }
    }

    private void doCacheEvict(CacheEvict cacheEvict, StandardEvaluationContext spelContext, String preKey) {
        String fieldKey, cacheKey;
        Class<? extends CacheClient> cls = cacheEvict.cacheClient();
        CacheClient cacheClient = SpringUtil.getBean(cls);
        if (cacheClient == null) {
            return;
        }
        if (cacheEvict.collection()) {
            Collection fieldList = SpelUtils.parseSpelExpression(spelContext, cacheEvict.fieldKey(), Collection.class);
            if (fieldList == null) {
                return;
            }
            StandardEvaluationContext subContext;
            for (Object object : fieldList) {
                if (!StringUtils.hasText(cacheEvict.subFieldKey())) {
                    fieldKey = String.valueOf(object);
                } else {
                    subContext = SpelUtils.initSpelContext(object);
                    fieldKey = SpelUtils.parseSpelExpression(subContext, cacheEvict.subFieldKey(), String.class);
                }
                cacheKey = toKey(preKey, fieldKey);
                deleteCache(cacheEvict, cacheKey, cacheClient);
            }
        } else {
            fieldKey = SpelUtils.parseSpelExpression(spelContext, cacheEvict.fieldKey(), String.class);
            cacheKey = toKey(preKey, fieldKey);
            deleteCache(cacheEvict, cacheKey, cacheClient);
        }
    }

    private void deleteCache(CacheEvict cacheEvict, String cacheKey, CacheClient cacheClient) {
        if (cacheEvict.group()) {
            Set<Object> groupKeys = cacheClient.setMembers(cacheKey);
            if (groupKeys != null) {
                for (Object groupKey : groupKeys) {
                    deleteCache((String) groupKey, cacheClient);
                }
            }
        } else {
            deleteCache(cacheKey, cacheClient);
        }
    }

    private void deleteCache(String cacheKey, CacheClient cacheClient) {
        try {
            cacheClient.del(cacheKey);
            log.debug("Delete cache key={}", cacheKey);
        } catch (Exception ex) {
            log.error("Delete cache key={} error:{}", cacheKey, ex.getMessage());
        }
    }
}

