package cn.genn.cache.redis.component;

import cn.genn.cache.common.AbstractMultiCacheManager;
import org.redisson.api.RedissonClient;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.cache.CacheManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基于Redisson多缓存管理器
 * <AUTHOR>
 */
public class RedissonMultiCacheManager extends AbstractMultiCacheManager<CacheConfig> {

    private final RedissonClient redissonClient;

    public RedissonMultiCacheManager(RedissonClient redissonClient, CacheConfig defaultCacheConfig, Map<String, CacheConfig> cacheConfigs) {
        super(defaultCacheConfig);
        this.redissonClient = redissonClient;
        initialCacheManagerMap(cacheConfigs);
    }

    @Override
    protected void mergerCacheConfig(CacheConfig cacheConfig, CacheConfig defaultCacheConfig) {
        if (cacheConfig.getTTL() < 0) {
            cacheConfig.setTTL(defaultCacheConfig.getTTL());
        }
        if (cacheConfig.getMaxIdleTime() < 0) {
            cacheConfig.setMaxIdleTime(defaultCacheConfig.getMaxIdleTime());
        }
        if (cacheConfig.getMaxSize() < 0) {
            cacheConfig.setMaxSize(defaultCacheConfig.getMaxSize());
        }
    }

    @Override
    protected boolean isValid(CacheConfig cacheConfig) {
        return cacheConfig.getTTL() >= 0;
    }

    @Override
    protected CacheManager createCacheManager(List<String> names, CacheConfig cacheConfig) {
        Map<String, CacheConfig> finalConfigMap  = new HashMap<>();
        for(String name :names) {
            finalConfigMap.put(name, cacheConfig);
        }
        return new RedissonSpringCacheManager(redissonClient, finalConfigMap);
    }
}
