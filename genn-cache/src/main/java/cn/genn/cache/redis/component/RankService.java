package cn.genn.cache.redis.component;

import cn.genn.cache.redis.model.RankScore;
import org.springframework.data.redis.core.ZSetOperations;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface RankService {

    /**
     * 加入数据
     *
     * @param key
     * @param value
     * @param score
     */
    void put(String key, String value, double score);

    /**
     * 批量加入数据
     *
     * @param key
     * @param tuples
     */
    void put(String key, Set<ZSetOperations.TypedTuple<String>> tuples);

    /**
     * 删除数据
     *
     * @param key
     * @param values
     */
    void remove(String key, String... values);

    /**
     * 获取排行榜
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    List<RankScore> rankList(String key, long start, long end);

    /**
     * 查询排行
     *
     * @param key
     * @param value
     * @return
     */
    RankScore getRank(String key, String value);

    /**
     * 截断排行榜，只保留前几名
     *
     * @param key
     * @param maxRank
     */
    void removeRankAfter(String key, long maxRank);

    /**
     * 计算总数
     *
     * @param key
     * @return
     */
    long size(String key);

    /**
     * 重命名排行榜key
     *
     * @param oldKey
     * @param newKey
     */
    void renameKey(String oldKey, String newKey);
}
