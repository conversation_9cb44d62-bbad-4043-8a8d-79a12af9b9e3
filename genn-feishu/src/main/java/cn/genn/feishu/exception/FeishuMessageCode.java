package cn.genn.feishu.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * <AUTHOR>
 */
public enum FeishuMessageCode implements MessageCodeWrap {

    FEISHU_CLIENT_NOT_FOUND("201", "飞书客户端未找到"),
    FEISHU_APP_TOKEN_NOT_EMPTY("202", "飞书应用Token未设置"),
    FEISHU_URL_EMPTY("203", "飞书URL不能为空"),
    FEISHU_APP_TOKEN_EXTRACT_FAILED("204", "无法从URL提取appToken"),
    FEISHU_NODE_TOKEN_EXTRACT_FAILED("205", "无法从URL提取nodeToken"),
    FEISHU_API_CALL_FAILED("206", "调用飞书API获取appToken失败"),
    FEISHU_TABLE_ID_EXTRACT_FAILED("207", "无法从URL提取表格ID"),
    FEISHU_BITABLE_API_CALL_FAILED("208", "调用飞书多维表格API失败"),
    FEISHU_URL_PARSE_FAILED("209", "URL解析失败"),

    GET_OPEN_ID_FAIL("301","获取用户openId失败"),
    DEPARTMENT_INFO_FAIL("302", "获取部门信息失败"),
    USER_INFO_FAIL("303", "获取用户信息失败"),
    CHILDREN_DEPARTMENT_INFO_FAIL("304", "获取子部门信息失败"),

    SYSTEM_ERROR("500", "系统异常，请稍后再试"),
    ;

    private final String code;
    private final String description;

    FeishuMessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getBizCode() {
        return "01";
    }

    @Override
    public String getCode() {
        return code;
    }



    @Override
    public String getDescription() {
        return description;
    }
}
