package cn.genn.feishu.exception;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.MessageCodeWrap;

import java.io.Serial;

/**
 * 飞书相关异常
 * <AUTHOR>
 */
public class FeishuException extends BaseException {

    @Serial
    private static final long serialVersionUID = -4098680058281509449L;

    public FeishuException() {
        super(FeishuMessageCode.SYSTEM_ERROR);
    }

    public FeishuException(String message) {
        super(FeishuMessageCode.SYSTEM_ERROR, message);
    }

    public FeishuException(String code, String message) {
        super(code, message);
    }

    public FeishuException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public FeishuException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
