package cn.genn.feishu.constants;

import java.util.regex.Pattern;

/**
 * 飞书表格工具常量
 * <AUTHOR>
 */
public final class FeishuTableConstants {

    private FeishuTableConstants() {
        // 工具类，禁止实例化
    }

    /**
     * URL类型标识
     */
    public static final String BASE_URL_PATTERN = "feishu.cn/base";
    public static final String WIKI_URL_PATTERN = "feishu.cn/wiki";
    public static final String SHEETS_URL_PATTERN = "feishu.cn/sheets";

    /**
     * 预编译的正则表达式
     */
    public static final Pattern BASE_APP_TOKEN_PATTERN = Pattern.compile("base/([^/?]+)");
    public static final Pattern WIKI_NODE_TOKEN_PATTERN = Pattern.compile("/wiki/([\\w-]+)(?:\\?|$)");
    public static final Pattern SHEETS_NODE_TOKEN_PATTERN = Pattern.compile("/sheets/([\\w-]+)(?:\\?|$)");
    public static final Pattern TABLE_ID_PATH_PATTERN = Pattern.compile("/table/([^/]+)");

    /**
     * API相关常量
     */
    public static final String WIKI_OBJ_TYPE = "wiki";
    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * URL参数名称
     */
    public static final String TABLE_PARAM_NAME = "table";

    /**
     * 日志标识
     */
    public static final String LOG_PREFIX = "[FeishuTableUtils]";
}