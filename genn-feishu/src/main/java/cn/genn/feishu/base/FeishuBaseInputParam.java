package cn.genn.feishu.base;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 飞书基础输入参数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FeishuBaseInputParam {

    /**
     * 飞书应用ID
     */
    private String appId;

    /**
     * 飞书应用密钥
     */
    private String appSecret;

    /**
     * 用户ID类型，可选值为 open_id、union_id、user_id，默认值为 open_id
     */
    private String userIdType = "open_id";
}