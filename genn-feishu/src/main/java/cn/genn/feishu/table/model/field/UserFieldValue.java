package cn.genn.feishu.table.model.field;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人员字段值
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserFieldValue extends FieldValue {
    
    /**
     * 人员信息列表
     */
    private List<UserInfo> users = new ArrayList<>();
    
    public UserFieldValue() {
        setFieldType(FieldType.USER);
    }
    
    @SuppressWarnings("unchecked")
    public UserFieldValue(Object value) {
        this();
        setRawValue(value);
        if (value instanceof List) {
            List<Object> list = (List<Object>) value;
            for (Object item : list) {
                if (item instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) item;
                    UserInfo userInfo = new UserInfo()
                        .setId((String) map.get("id"))
                        .setName((String) map.get("name"))
                        .setEnName((String) map.get("en_name"))
                        .setEmail((String) map.get("email"));
                    this.users.add(userInfo);
                }
            }
        }
    }
    
    @Override
    public Object getParsedValue() {
        return users;
    }
    
    @Override
    public String getStringValue() {
        return users.stream()
            .map(UserInfo::getName)
            .filter(name -> name != null && !name.isEmpty())
            .collect(Collectors.joining(", "));
    }
    
    /**
     * 获取第一个用户信息
     * @return 第一个用户信息，如果没有则返回null
     */
    public UserInfo getFirstUser() {
        return users.isEmpty() ? null : users.getFirst();
    }
    
    /**
     * 获取用户数量
     * @return 用户数量
     */
    public int getUserCount() {
        return users.size();
    }
    
    /**
     * 用户信息
     */
    @Data
    @Accessors(chain = true)
    public static class UserInfo {
        /**
         * 用户ID
         */
        private String id;
        
        /**
         * 用户名称
         */
        private String name;
        
        /**
         * 英文名称
         */
        private String enName;
        
        /**
         * 邮箱
         */
        private String email;
    }
}