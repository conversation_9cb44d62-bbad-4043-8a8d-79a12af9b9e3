package cn.genn.feishu.table.model.field;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 单选字段值
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SingleSelectFieldValue extends FieldValue {
    
    /**
     * 选中的选项
     */
    private SelectOption selectedOption;
    
    public SingleSelectFieldValue() {
        setFieldType(FieldType.SINGLE_SELECT);
    }
    
    @SuppressWarnings("unchecked")
    public SingleSelectFieldValue(Object value) {
        this();
        if (value instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) value;
            this.selectedOption = new SelectOption()
                .setId((String) map.get("id"))
                .setText((String) map.get("text"))
                .setColor((Integer) map.get("color"));
        } else if (value instanceof String) {
            // 将字符串作为文本处理
            this.selectedOption = new SelectOption(null, (String) value);
        }
        setRawValue(value);
    }
    
    @Override
    public Object getParsedValue() {
        return selectedOption;
    }
    
    @Override
    public String getStringValue() {
        return selectedOption != null && selectedOption.getText() != null ? selectedOption.getText() : "";
    }
    
    /**
     * 获取选项ID
     * @return 选项ID
     */
    public String getOptionId() {
        return selectedOption != null ? selectedOption.getId() : null;
    }
    
    /**
     * 获取选项文本
     * @return 选项文本
     */
    public String getOptionText() {
        return selectedOption != null ? selectedOption.getText() : null;
    }
    
    /**
     * 获取选项颜色
     * @return 选项颜色
     */
    public Integer getOptionColor() {
        return selectedOption != null ? selectedOption.getColor() : null;
    }
}