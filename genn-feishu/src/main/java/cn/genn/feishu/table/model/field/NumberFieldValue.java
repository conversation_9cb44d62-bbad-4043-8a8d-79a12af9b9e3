package cn.genn.feishu.table.model.field;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 数字字段值
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class NumberFieldValue extends FieldValue {
    
    /**
     * 数字值
     */
    private BigDecimal number;
    
    public NumberFieldValue() {
        setFieldType(FieldType.NUMBER);
    }
    
    public NumberFieldValue(Object value) {
        this();
        if (value != null) {
            if (value instanceof Number) {
                this.number = new BigDecimal(value.toString());
            } else if (value instanceof String) {
                try {
                    this.number = new BigDecimal((String) value);
                } catch (NumberFormatException e) {
                    this.number = BigDecimal.ZERO;
                }
            }
        }
        setRawValue(value);
    }
    
    @Override
    public Object getParsedValue() {
        return number;
    }
    
    @Override
    public String getStringValue() {
        return number != null ? number.toString() : "0";
    }
    
    /**
     * 获取整数值
     * @return 整数值
     */
    public Integer getIntValue() {
        return number != null ? number.intValue() : 0;
    }
    
    /**
     * 获取长整数值
     * @return 长整数值
     */
    public Long getLongValue() {
        return number != null ? number.longValue() : 0L;
    }
    
    /**
     * 获取双精度浮点数值
     * @return 双精度浮点数值
     */
    public Double getDoubleValue() {
        return number != null ? number.doubleValue() : 0.0;
    }
}