package cn.genn.feishu.table.model.field;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 选择选项
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SelectOption {
    
    /**
     * 选项ID
     */
    private String id;
    
    /**
     * 选项文本
     */
    private String text;
    
    /**
     * 选项颜色
     */
    private Integer color;
    
    public SelectOption() {}
    
    public SelectOption(String id, String text) {
        this.id = id;
        this.text = text;
    }
    
    public SelectOption(String id, String text, Integer color) {
        this.id = id;
        this.text = text;
        this.color = color;
    }
}