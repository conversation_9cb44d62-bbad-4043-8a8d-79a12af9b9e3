package cn.genn.feishu.table;

import cn.genn.feishu.table.model.field.FieldValue;
import cn.genn.feishu.table.model.field.MultiLineTextFieldValue;
import cn.genn.feishu.table.model.field.NumberFieldValue;
import cn.genn.feishu.table.model.field.SingleSelectFieldValue;
import cn.genn.feishu.table.model.field.TextFieldValue;
import cn.genn.feishu.table.model.field.TypedFieldValue;
import cn.genn.feishu.table.model.field.UserFieldValue;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 飞书多维表格记录解析工具
 * <AUTHOR>
 */
@Slf4j
public final class BitableRecordParser {

    private BitableRecordParser() {
        // 工具类，禁止实例化
    }

    public static List<Map<String, FieldValue>> parseRecords(List<AppTableRecord> records) {
        if (records == null || records.isEmpty()) {
            return List.of();
        }

        return records.stream()
            .map(BitableRecordParser::parseRecordFields)
            .toList();
    }

    public static List<Map<String, FieldValue>> parseRecords(List<AppTableRecord> records, String... includeFields) {
        if (records == null || records.isEmpty()) {
            return List.of();
        }

        return records.stream()
                .map(record -> parseRecordFields(record, includeFields))
                .toList();
    }

    /**
     * 解析记录字段
     * @param record 记录对象
     * @return 解析后的字段映射
     */
    public static Map<String, FieldValue> parseRecordFields(AppTableRecord record) {
        return parseRecordFieldsInternal(record, null);
    }

    /**
     * 解析记录字段（只解析指定的字段，提升性能）
     * @param record 记录对象
     * @param includeFields 需要解析的字段名集合，为null时解析所有字段
     * @return 解析后的字段映射
     */
    public static Map<String, FieldValue> parseRecordFields(AppTableRecord record, Set<String> includeFields) {
        return parseRecordFieldsInternal(record, includeFields);
    }

    /**
     * 解析记录字段（只解析指定的字段，提升性能）
     * @param record 记录对象
     * @param includeFields 需要解析的字段名数组
     * @return 解析后的字段映射
     */
    public static Map<String, FieldValue> parseRecordFields(AppTableRecord record, String... includeFields) {
        if (includeFields == null || includeFields.length == 0) {
            return parseRecordFieldsInternal(record, null);
        }
        return parseRecordFieldsInternal(record, Set.of(includeFields));
    }

    /**
     * 内部解析方法
     * @param record 记录对象
     * @param includeFields 需要解析的字段名集合，为null时解析所有字段
     * @return 解析后的字段映射
     */
    private static Map<String, FieldValue> parseRecordFieldsInternal(AppTableRecord record, Set<String> includeFields) {
        Map<String, FieldValue> parsedFields = new HashMap<>();
        
        if (record == null || record.getFields() == null) {
            return parsedFields;
        }

        Map<String, Object> fields = record.getFields();
        for (Map.Entry<String, Object> entry : fields.entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();
            
            // 如果指定了包含字段列表，且当前字段不在列表中，则跳过
            if (includeFields != null && !includeFields.contains(fieldName)) {
                continue;
            }
            
            try {
                FieldValue parsedValue = parseFieldValue(fieldValue, fieldName);
                parsedFields.put(fieldName, parsedValue);
            } catch (Exception e) {
                log.warn("解析字段失败，字段名: {}, 原始值: {}, 错误: {}", fieldName, fieldValue, e.getMessage());
                // 解析失败时，设置为null，不影响整个逻辑
                parsedFields.put(fieldName, null);
            }
        }
        
        return parsedFields;
    }

    /**
     * 解析单个字段值
     * @param value 原始字段值
     * @return 解析后的字段值
     */
    public static FieldValue parseFieldValue(Object value) {
        return parseFieldValue(value, null);
    }

    /**
     * 解析单个字段值
     * @param value 原始字段值
     * @param fieldName 字段名（用于辅助判断字段类型）
     * @return 解析后的字段值
     */
    public static FieldValue parseFieldValue(Object value, String fieldName) {
        switch (value) {
            case null -> {
                return new TextFieldValue("");
            }

            // 根据值的类型进行智能解析
            case String s -> {
                return new TextFieldValue(s);
            }
            case Number number -> {
                return new NumberFieldValue(value);
            }
            case Boolean b -> {
                return new TextFieldValue(value.toString());
            }
            case List<?> list -> {
                // 检查是否是多行文本格式：[{"text": "内容", "type": "text"}]
                if (isMultiLineTextFormat(value)) {
                    return new MultiLineTextFieldValue(value);
                }
                
                // 检查是否是人员字段格式：[{"id": "xxx", "name": "xxx", "email": "xxx"}]
                if (isUserFieldFormat(value)) {
                    return new UserFieldValue(value);
                }
                
                // 其他列表类型暂时作为文本处理
                return new TextFieldValue(value.toString());
            }
            case Map<?, ?> map1 -> {
                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) value;

                // 检查是否是标准飞书字段格式：{"type": 数字, "value": [值]}
                if (map.containsKey("type") && map.containsKey("value")) {
                    return new TypedFieldValue(value);
                }

                // 检查是否是空的关联字段：{}
                if (map.isEmpty()) {
                    return new TextFieldValue("");
                }

                // 检查是否是选择字段格式
                if (map.containsKey("text") || map.containsKey("id")) {
                    return new SingleSelectFieldValue(value);
                }

                // 其他复杂类型暂时作为文本处理
                return new TextFieldValue(value.toString());
            }
            default -> {
                // 其他类型统一转为文本
                return new TextFieldValue(value.toString());
            }
        }
    }

    /**
     * 获取字段的文本值
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 字段的文本值
     */
    public static String getFieldText(AppTableRecord record, String fieldName) {
        if (record == null || record.getFields() == null) {
            return "";
        }
        
        Object value = record.getFields().get(fieldName);
        if (value == null) {
            return "";
        }
        
        FieldValue fieldValue = parseFieldValue(value);
        return fieldValue.getStringValue();
    }

    /**
     * 获取字段的数字值
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 字段的数字值，如果不是数字则返回null
     */
    public static Number getFieldNumber(AppTableRecord record, String fieldName) {
        if (record == null || record.getFields() == null) {
            return null;
        }
        
        Object value = record.getFields().get(fieldName);
        if (value == null) {
            return null;
        }
        
        FieldValue fieldValue = parseFieldValue(value);
        if (fieldValue instanceof NumberFieldValue) {
            return ((NumberFieldValue) fieldValue).getNumber();
        }
        
        return null;
    }

    /**
     * 获取字段的布尔值
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 字段的布尔值
     */
    public static Boolean getFieldBoolean(AppTableRecord record, String fieldName) {
        if (record == null || record.getFields() == null) {
            return false;
        }
        
        Object value = record.getFields().get(fieldName);
        if (value == null) {
            return false;
        }
        
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return "true".equalsIgnoreCase((String) value) || "1".equals(value);
        } else if (value instanceof Number) {
            return ((Number) value).intValue() != 0;
        }
        
        return false;
    }

    /**
     * 检查字段是否存在且不为空
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 字段是否存在且不为空
     */
    public static boolean hasField(AppTableRecord record, String fieldName) {
        if (record == null || record.getFields() == null) {
            return false;
        }
        
        Object value = record.getFields().get(fieldName);
        return value != null && !value.toString().trim().isEmpty();
    }

    /**
     * 检查是否是多行文本格式：[{"text": "内容", "type": "text"}]
     * @param value 字段值
     * @return 是否是多行文本格式
     */
    @SuppressWarnings("unchecked")
    public static boolean isMultiLineTextFormat(Object value) {
        if (!(value instanceof List)) {
            return false;
        }
        
        List<Object> list = (List<Object>) value;
        if (list.isEmpty()) {
            return false;
        }
        
        // 检查第一个元素是否包含text和type字段（多行文本格式）
        Object firstItem = list.getFirst();
        if (firstItem instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) firstItem;
            // 多行文本格式必须同时包含text和type字段
            return map.containsKey("text") && map.containsKey("type");
        }
        
        return false;
    }

    /**
     * 检查是否是人员字段格式：[{"id": "xxx", "name": "xxx", "email": "xxx"}]
     * @param value 字段值
     * @return 是否是人员字段格式
     */
    @SuppressWarnings("unchecked")
    public static boolean isUserFieldFormat(Object value) {
        if (!(value instanceof List)) {
            return false;
        }
        
        List<Object> list = (List<Object>) value;
        if (list.isEmpty()) {
            return false;
        }
        
        // 检查第一个元素是否包含人员字段的特征字段
        Object firstItem = list.getFirst();
        if (firstItem instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) firstItem;
            // 人员字段通常包含id字段，且不包含text+type组合（区别于多行文本）
            return map.containsKey("id") && !map.containsKey("type");
        }
        
        return false;
    }


    /**
     * 获取多行文本字段值
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 多行文本字段值，如果不是多行文本则返回null
     */
    public static MultiLineTextFieldValue getMultiLineTextField(AppTableRecord record, String fieldName) {
        if (record == null || record.getFields() == null) {
            return null;
        }
        
        Object value = record.getFields().get(fieldName);
        if (value == null) {
            return null;
        }
        
        // 检查是否是多行文本格式
        if (isMultiLineTextFormat(value)) {
            return new MultiLineTextFieldValue(value);
        }
        
        return null;
    }

    /**
     * 获取多行文本的纯文本内容
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 纯文本内容
     */
    public static String getMultiLineTextContent(AppTableRecord record, String fieldName) {
        MultiLineTextFieldValue fieldValue = getMultiLineTextField(record, fieldName);
        return fieldValue != null ? fieldValue.getPlainText() : "";
    }

    /**
     * 获取多行文本的段落数量
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 段落数量
     */
    public static int getMultiLineTextSegmentCount(AppTableRecord record, String fieldName) {
        MultiLineTextFieldValue fieldValue = getMultiLineTextField(record, fieldName);
        return fieldValue != null ? fieldValue.getSegmentCount() : 0;
    }

    /**
     * 检查多行文本是否包含指定类型的段落
     * @param record 记录对象
     * @param fieldName 字段名
     * @param segmentType 段落类型
     * @return 是否包含指定类型的段落
     */
    public static boolean hasMultiLineTextSegmentType(AppTableRecord record, String fieldName, String segmentType) {
        MultiLineTextFieldValue fieldValue = getMultiLineTextField(record, fieldName);
        return fieldValue != null && fieldValue.hasSegmentType(segmentType);
    }

    /**
     * 获取人员字段值
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 人员字段值，如果不是人员字段则返回null
     */
    public static UserFieldValue getUserField(AppTableRecord record, String fieldName) {
        if (record == null || record.getFields() == null) {
            return null;
        }
        
        Object value = record.getFields().get(fieldName);
        if (value == null) {
            return null;
        }
        
        // 检查是否是人员字段格式
        if (isUserFieldFormat(value)) {
            return new UserFieldValue(value);
        }
        
        return null;
    }

    /**
     * 获取人员字段的用户名列表
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 用户名列表，用逗号分隔
     */
    public static String getUserFieldNames(AppTableRecord record, String fieldName) {
        UserFieldValue fieldValue = getUserField(record, fieldName);
        return fieldValue != null ? fieldValue.getStringValue() : "";
    }

    /**
     * 获取人员字段的第一个用户
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 第一个用户信息，如果没有则返回null
     */
    public static UserFieldValue.UserInfo getFirstUser(AppTableRecord record, String fieldName) {
        UserFieldValue fieldValue = getUserField(record, fieldName);
        return fieldValue != null ? fieldValue.getFirstUser() : null;
    }

    /**
     * 获取带type的字段值
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 带type的字段值，如果不是该格式则返回null
     */
    public static TypedFieldValue getTypedField(AppTableRecord record, String fieldName) {
        if (record == null || record.getFields() == null) {
            return null;
        }
        
        Object value = record.getFields().get(fieldName);
        if (value == null) {
            return null;
        }
        
        // 检查是否是带type的字段格式
        if (value instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) value;
            if (map.containsKey("type") && map.containsKey("value")) {
                return new TypedFieldValue(value);
            }
        }
        
        return null;
    }

    /**
     * 获取带type字段的文本值
     * @param record 记录对象
     * @param fieldName 字段名
     * @return 字段的文本值
     */
    public static String getTypedFieldText(AppTableRecord record, String fieldName) {
        TypedFieldValue fieldValue = getTypedField(record, fieldName);
        return fieldValue != null ? fieldValue.getStringValue() : "";
    }

    /**
     * 获取记录的所有字段名
     * @param record 记录对象
     * @return 字段名集合
     */
    public static java.util.Set<String> getFieldNames(AppTableRecord record) {
        if (record == null || record.getFields() == null) {
            return java.util.Collections.emptySet();
        }
        
        return record.getFields().keySet();
    }
}