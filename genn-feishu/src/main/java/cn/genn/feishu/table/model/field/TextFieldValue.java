package cn.genn.feishu.table.model.field;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 文本字段值
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class TextFieldValue extends FieldValue {
    
    /**
     * 文本内容
     */
    private String text;
    
    public TextFieldValue() {
        setFieldType(FieldType.TEXT);
    }
    
    public TextFieldValue(String text) {
        this();
        this.text = text;
        setRawValue(text);
    }
    
    @Override
    public Object getParsedValue() {
        return text;
    }
    
    @Override
    public String getStringValue() {
        return text != null ? text : "";
    }
}