package cn.genn.feishu.table.model.field;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 飞书多维表格字段值基础类
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public abstract class FieldValue {
    
    /**
     * 字段类型
     */
    private FieldType fieldType;
    
    /**
     * 原始值
     */
    private Object rawValue;
    
    /**
     * 获取解析后的值
     * @return 解析后的值
     */
    public abstract Object getParsedValue();
    
    /**
     * 获取字符串表示
     * @return 字符串值
     */
    public abstract String getStringValue();
}