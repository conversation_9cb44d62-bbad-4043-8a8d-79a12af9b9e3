package cn.genn.feishu.table;

import cn.genn.feishu.constants.FeishuTableConstants;
import cn.genn.feishu.exception.FeishuException;
import cn.genn.feishu.exception.FeishuMessageCode;
import cn.genn.feishu.table.model.FeishuBitableQueryParam;
import cn.genn.feishu.table.model.FeishuBitableQueryResult;
import cn.genn.feishu.utils.FeishuClientUtils;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.Client;
import com.lark.oapi.service.bitable.v1.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 飞书多维表格查询工具类
 * <AUTHOR>
 */
@Slf4j
public final class FeishuBitableQueryUtils {

    private FeishuBitableQueryUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 查询多维表格记录
     * @param queryParam 查询参数
     * @return 查询结果
     * @throws FeishuException 查询失败时抛出异常
     */
    public static FeishuBitableQueryResult queryRecords(FeishuBitableQueryParam queryParam) {
        validateQueryParam(queryParam);
        
        Client client = FeishuClientUtils.getClient(
            queryParam.getAppInfo().getAppId(), 
            queryParam.getAppInfo().getAppSecret()
        );
        
        String appToken = FeishuTableUtils.extractAppTokenFromUrl(queryParam.getTableUrl(), client);
        String tableId = FeishuTableUtils.fetchBitableFirstTableId(client, queryParam.getTableUrl(), appToken);
        
        if (StrUtil.isEmpty(tableId)) {
            throw new FeishuException(FeishuMessageCode.FEISHU_BITABLE_API_CALL_FAILED, "无法获取表格ID");
        }

        if (queryParam.isAutoPage()) {
            return queryAllRecords(client, appToken, tableId, queryParam);
        } else {
            return querySinglePage(client, appToken, tableId, queryParam);
        }
    }

    /**
     * 查询单页记录
     * @param client 飞书客户端
     * @param appToken 应用Token
     * @param tableId 表格ID
     * @param queryParam 查询参数
     * @return 查询结果
     */
    private static FeishuBitableQueryResult querySinglePage(Client client, String appToken, String tableId, FeishuBitableQueryParam queryParam) {
        try {
            SearchAppTableRecordReq req = buildSearchRequest(appToken, tableId, queryParam);
            SearchAppTableRecordResp resp = client.bitable().v1().appTableRecord().search(req);
            
            if (!resp.success()) {
                log.error("{} 查询多维表格记录失败, appToken: {}, tableId: {}, 错误码: {}, 错误信息: {}",
                    FeishuTableConstants.LOG_PREFIX, appToken, tableId, resp.getCode(), resp.getMsg());
                throw new FeishuException(FeishuMessageCode.FEISHU_BITABLE_API_CALL_FAILED, resp.getMsg());
            }

            return buildQueryResult(resp, false);
        } catch (FeishuException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} 查询多维表格记录异常, appToken: {}, tableId: {}", 
                FeishuTableConstants.LOG_PREFIX, appToken, tableId, e);
            throw new FeishuException(FeishuMessageCode.FEISHU_BITABLE_API_CALL_FAILED, e.getMessage());
        }
    }

    /**
     * 查询所有记录（自动分页）
     * @param client 飞书客户端
     * @param appToken 应用Token
     * @param tableId 表格ID
     * @param queryParam 查询参数
     * @return 查询结果
     */
    private static FeishuBitableQueryResult queryAllRecords(Client client, String appToken, String tableId, FeishuBitableQueryParam queryParam) {
        List<AppTableRecord> allRecords = new ArrayList<>();
        String pageToken = queryParam.getPageToken();
        int pageCount = 0;
        boolean hasMore = true;

        try {
            while (hasMore) {
                // 检查是否达到最大记录数限制
                if (queryParam.getMaxRecords() != null && allRecords.size() >= queryParam.getMaxRecords()) {
                    log.info("{} 已达到最大记录数限制: {}, 停止查询",
                        FeishuTableConstants.LOG_PREFIX, queryParam.getMaxRecords());
                    break;
                }
                
                // 创建当前页的查询参数
                FeishuBitableQueryParam currentPageParam = cloneQueryParam(queryParam);
                currentPageParam.setPageToken(pageToken);
                
                // 如果设置了最大记录数限制，调整当前页的页大小
                if (queryParam.getMaxRecords() != null) {
                    int remainingRecords = queryParam.getMaxRecords() - allRecords.size();
                    if (remainingRecords < currentPageParam.getPageSize()) {
                        currentPageParam.setPageSize(remainingRecords);
                    }
                }
                
                SearchAppTableRecordReq req = buildSearchRequest(appToken, tableId, currentPageParam);
                SearchAppTableRecordResp resp = client.bitable().v1().appTableRecord().search(req);
                
                if (!resp.success()) {
                    log.error("{} 查询多维表格记录失败, appToken: {}, tableId: {}, 页码: {}, 错误码: {}, 错误信息: {}",
                        FeishuTableConstants.LOG_PREFIX, appToken, tableId, pageCount + 1, resp.getCode(), resp.getMsg());
                    throw new FeishuException(FeishuMessageCode.FEISHU_BITABLE_API_CALL_FAILED, resp.getMsg());
                }

                // 收集当前页的记录
                if (resp.getData() != null && resp.getData().getItems() != null) {
                    List<AppTableRecord> currentPageRecords = Arrays.asList(resp.getData().getItems());
                    
                    // 如果设置了最大记录数限制，可能需要截取部分记录
                    if (queryParam.getMaxRecords() != null) {
                        int remainingSlots = queryParam.getMaxRecords() - allRecords.size();
                        if (currentPageRecords.size() > remainingSlots) {
                            currentPageRecords = currentPageRecords.subList(0, remainingSlots);
                        }
                    }
                    
                    allRecords.addAll(currentPageRecords);
                }

                // 检查是否还有更多页
                hasMore = resp.getData() != null && resp.getData().getHasMore() != null && resp.getData().getHasMore();
                pageToken = hasMore ? resp.getData().getPageToken() : null;
                pageCount++;

                log.debug("{} 已查询第{}页，当前页记录数: {}, 累计记录数: {}, 是否还有更多: {}, 最大记录数限制: {}",
                    FeishuTableConstants.LOG_PREFIX, pageCount,
                    resp.getData() != null && resp.getData().getItems() != null ? resp.getData().getItems().length : 0,
                    allRecords.size(), hasMore, queryParam.getMaxRecords());
                
                // 如果达到最大记录数限制，停止查询
                if (queryParam.getMaxRecords() != null && allRecords.size() >= queryParam.getMaxRecords()) {
                    log.info("{} 已达到最大记录数限制: {}, 停止查询",
                        FeishuTableConstants.LOG_PREFIX, queryParam.getMaxRecords());
                    break;
                }
            }

            return new FeishuBitableQueryResult()
                .setRecords(allRecords)
                .setHasMore(false)
                .setPageToken(null)
                .setTotalCount(allRecords.size())
                .setPageCount(pageCount);

        } catch (FeishuException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} 自动分页查询多维表格记录异常, appToken: {}, tableId: {}", 
                FeishuTableConstants.LOG_PREFIX, appToken, tableId, e);
            throw new FeishuException(FeishuMessageCode.FEISHU_BITABLE_API_CALL_FAILED, e.getMessage());
        }
    }

    /**
     * 构建搜索请求
     * @param appToken 应用Token
     * @param tableId 表格ID
     * @param queryParam 查询参数
     * @return 搜索请求
     */
    private static SearchAppTableRecordReq buildSearchRequest(String appToken, String tableId, FeishuBitableQueryParam queryParam) {
        SearchAppTableRecordReqBody.Builder bodyBuilder = SearchAppTableRecordReqBody.newBuilder()
            .automaticFields(Optional.ofNullable(queryParam.getAutomaticFields()).orElse(false));

        // 设置视图ID
        if (StrUtil.isNotEmpty(queryParam.getViewId())) {
            bodyBuilder.viewId(queryParam.getViewId());
        }

        // 设置字段名称
        if (queryParam.getFieldNames() != null && queryParam.getFieldNames().length > 0) {
            bodyBuilder.fieldNames(queryParam.getFieldNames());
        }

        // 设置排序条件
        if (queryParam.getSorts() != null && queryParam.getSorts().length > 0) {
            bodyBuilder.sort(queryParam.getSorts());
        }

        // 设置过滤条件
        if (queryParam.getFilter() != null) {
            bodyBuilder.filter(queryParam.getFilter());
        }

        return SearchAppTableRecordReq.newBuilder()
            .appToken(appToken)
            .tableId(tableId)
            .userIdType(Optional.ofNullable(queryParam.getAppInfo().getUserIdType()).orElse("open_id"))
            .pageToken(queryParam.getPageToken())
            .pageSize(Optional.ofNullable(queryParam.getPageSize()).orElse(20))
            .searchAppTableRecordReqBody(bodyBuilder.build())
            .build();
    }

    /**
     * 构建查询结果
     * @param resp API响应
     * @param isAutoPage 是否自动分页
     * @return 查询结果
     */
    private static FeishuBitableQueryResult buildQueryResult(SearchAppTableRecordResp resp, boolean isAutoPage) {
        FeishuBitableQueryResult result = new FeishuBitableQueryResult();
        
        if (resp.getData() != null) {
            if (resp.getData().getItems() != null) {
                result.setRecords(Arrays.asList(resp.getData().getItems()));
                if (!isAutoPage) {
                    result.setTotalCount(resp.getData().getItems().length);
                }
            } else {
                result.setRecords(new ArrayList<>());
                result.setTotalCount(0);
            }
            
            result.setHasMore(resp.getData().getHasMore());
            result.setPageToken(resp.getData().getPageToken());
        } else {
            result.setRecords(new ArrayList<>());
            result.setHasMore(false);
            result.setTotalCount(0);
        }
        
        if (!isAutoPage) {
            result.setPageCount(1);
        }
        
        return result;
    }

    /**
     * 克隆查询参数
     * @param original 原始参数
     * @return 克隆的参数
     */
    private static FeishuBitableQueryParam cloneQueryParam(FeishuBitableQueryParam original) {
        return new FeishuBitableQueryParam()
            .setAppInfo(original.getAppInfo())
            .setTableUrl(original.getTableUrl())
            .setAutomaticFields(original.getAutomaticFields())
            .setFieldNames(original.getFieldNames())
            .setSorts(original.getSorts())
            .setFilter(original.getFilter())
            .setPageSize(original.getPageSize())
            .setAutoPage(original.isAutoPage())
            .setMaxRecords(original.getMaxRecords())
            .setViewId(original.getViewId());
    }

    /**
     * 验证查询参数
     * @param queryParam 查询参数
     * @throws FeishuException 参数验证失败时抛出异常
     */
    private static void validateQueryParam(FeishuBitableQueryParam queryParam) {
        if (queryParam == null) {
            throw new FeishuException(FeishuMessageCode.SYSTEM_ERROR);
        }
        
        if (queryParam.getAppInfo() == null) {
            throw new FeishuException(FeishuMessageCode.FEISHU_CLIENT_NOT_FOUND);
        }
        
        if (StrUtil.isEmpty(queryParam.getAppInfo().getAppId()) || StrUtil.isEmpty(queryParam.getAppInfo().getAppSecret())) {
            throw new FeishuException(FeishuMessageCode.FEISHU_CLIENT_NOT_FOUND);
        }
        
        if (StrUtil.isEmpty(queryParam.getTableUrl())) {
            throw new FeishuException(FeishuMessageCode.FEISHU_URL_EMPTY);
        }
        
        if (queryParam.getPageSize() != null && (queryParam.getPageSize() <= 0 || queryParam.getPageSize() > 500)) {
            throw new FeishuException(FeishuMessageCode.SYSTEM_ERROR);
        }
    }
}