package cn.genn.feishu.table;

import cn.genn.feishu.constants.FeishuTableConstants;
import cn.genn.feishu.exception.FeishuException;
import cn.genn.feishu.exception.FeishuMessageCode;
import com.lark.oapi.Client;
import com.lark.oapi.service.bitable.v1.model.ListAppTableReq;
import com.lark.oapi.service.bitable.v1.model.ListAppTableResp;
import com.lark.oapi.service.sheets.v3.model.GetSpreadsheetReq;
import com.lark.oapi.service.sheets.v3.model.GetSpreadsheetResp;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceReq;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceResp;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;

/**
 * 飞书表格工具类
 * <AUTHOR>
 */
@Slf4j
public final class FeishuTableUtils {

    private FeishuTableUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 从飞书URL中提取appToken
     * @param url 飞书URL
     * @param client 飞书客户端
     * @return appToken
     * @throws FeishuException 提取失败时抛出异常
     */
    public static String extractAppTokenFromUrl(String url, Client client) {
        if (url == null || url.isEmpty()) {
            throw new FeishuException(FeishuMessageCode.FEISHU_URL_EMPTY);
        }
        
        // 判断URL类型并提取appToken
        if (url.contains(FeishuTableConstants.BASE_URL_PATTERN)) {
            return extractBaseAppToken(url);
        } else if (url.contains(FeishuTableConstants.WIKI_URL_PATTERN)) {
            return fetchAppTokenFromWiki(url, client);
        } else if (url.contains(FeishuTableConstants.SHEETS_URL_PATTERN)) {
            return fetchAppTokenFromSheet(url, client);
        } else {
            // 如果不是标准URL格式，直接返回，可能已经是appToken
            return url;
        }
    }


    /**
     * 获取多维表格的第一个表格ID
     * @param client 飞书客户端
     * @param url 飞书URL
     * @param appToken 应用Token
     * @return 表格ID，获取失败返回null
     */
    public static String fetchBitableFirstTableId(Client client, String url, String appToken) {
        if (client == null) {
            throw new FeishuException(FeishuMessageCode.FEISHU_CLIENT_NOT_FOUND);
        }
        if (appToken == null || appToken.isEmpty()) {
            throw new FeishuException(FeishuMessageCode.FEISHU_APP_TOKEN_NOT_EMPTY);
        }

        try {
            // 首先尝试从URL中提取表格ID
            String tableId = extractTableIdFromUrl(url);
            if (tableId != null) {
                return tableId;
            }

            // 如果URL中没有表格ID，则调用API获取第一个表格
            return fetchFirstTableFromApi(client, appToken);
        } catch (FeishuException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} 获取表格ID异常: {}", FeishuTableConstants.LOG_PREFIX, e.getMessage(), e);
            throw new FeishuException(FeishuMessageCode.FEISHU_BITABLE_API_CALL_FAILED, e.getMessage());
        }
    }

    /**
     * 从URL中提取表格ID
     * @param url 飞书URL
     * @return 表格ID，未找到返回null
     * @throws FeishuException URL解析失败时抛出异常
     */
    public static String extractTableIdFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        try {
            // 优先从查询参数中提取
            if (url.contains("?")) {
                String tableId = extractQueryParam(url, FeishuTableConstants.TABLE_PARAM_NAME);
                if (tableId != null) {
                    return tableId;
                }
            }

            // 从路径中提取
            Matcher matcher = FeishuTableConstants.TABLE_ID_PATH_PATTERN.matcher(url);
            if (matcher.find()) {
                return matcher.group(1);
            }

            return null;
        } catch (Exception e) {
            log.error("{} 从URL提取表格ID失败: {}", FeishuTableConstants.LOG_PREFIX, e.getMessage(), e);
            throw new FeishuException(FeishuMessageCode.FEISHU_TABLE_ID_EXTRACT_FAILED, e.getMessage());
        }
    }

    /**
     * 从API获取第一个表格ID
     * @param client 飞书客户端
     * @param appToken 应用Token
     * @return 表格ID
     * @throws FeishuException API调用失败时抛出异常
     */
    private static String fetchFirstTableFromApi(Client client, String appToken) {
        ListAppTableReq req = ListAppTableReq.newBuilder()
                .appToken(appToken)
                .pageSize(FeishuTableConstants.DEFAULT_PAGE_SIZE)
                .build();

        try {
            ListAppTableResp resp = client.bitable().v1().appTable().list(req);
            if (!resp.success()) {
                log.error("{} 调用多维表格API失败, appToken: {}, 错误码: {}, 错误信息: {}",
                    FeishuTableConstants.LOG_PREFIX, appToken, resp.getCode(), resp.getMsg());
                throw new FeishuException(FeishuMessageCode.FEISHU_BITABLE_API_CALL_FAILED, resp.getMsg());
            }

            if (resp.getData() != null && resp.getData().getItems() != null && resp.getData().getItems().length > 0) {
                return resp.getData().getItems()[0].getTableId();
            }

            log.warn("{} 未找到任何表格, appToken: {}", FeishuTableConstants.LOG_PREFIX, appToken);
            return null;
        } catch (FeishuException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} 调用多维表格API异常, appToken: {}", FeishuTableConstants.LOG_PREFIX, appToken, e);
            throw new FeishuException(FeishuMessageCode.FEISHU_BITABLE_API_CALL_FAILED, e.getMessage());
        }
    }

    /**
     * 从URL查询参数中提取指定参数值
     * @param url URL
     * @param paramName 参数名
     * @return 参数值，未找到返回null
     * @throws FeishuException URL解析失败时抛出异常
     */
    private static String extractQueryParam(String url, String paramName) {
        try {
            URI parsedUrl = new URI(url);
            String query = parsedUrl.getQuery();
            if (query != null) {
                String[] pairs = query.split("&");
                for (String pair : pairs) {
                    String[] keyValue = pair.split("=", 2);
                    if (keyValue.length == 2 && keyValue[0].equals(paramName)) {
                        return URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8);
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("{} URL解析失败: {}", FeishuTableConstants.LOG_PREFIX, e.getMessage(), e);
            throw new FeishuException(FeishuMessageCode.FEISHU_URL_PARSE_FAILED, e.getMessage());
        }
    }

    /**
     * 提取base类型URL的appToken
     * @param url base类型URL
     * @return appToken
     * @throws FeishuException 提取失败时抛出异常
     */
    private static String extractBaseAppToken(String url) {
        Matcher matcher = FeishuTableConstants.BASE_APP_TOKEN_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new FeishuException(FeishuMessageCode.FEISHU_APP_TOKEN_EXTRACT_FAILED);
    }

    /**
     * 从sheets类型URL获取appToken
     * @param url sheets类型URL
     * @param client 飞书客户端
     * @return appToken
     * @throws FeishuException 获取失败时抛出异常
     */
    private static String fetchAppTokenFromSheet(String url, Client client) {
        String nodeToken = extractNodeToken(url, FeishuTableConstants.SHEETS_NODE_TOKEN_PATTERN);
        return callSheetApiToGetAppToken(nodeToken, client);
    }

    /**
     * 从wiki类型URL获取appToken
     * @param url wiki类型URL
     * @param client 飞书客户端
     * @return appToken
     * @throws FeishuException 获取失败时抛出异常
     */
    private static String fetchAppTokenFromWiki(String url, Client client) {
        String nodeToken = extractNodeToken(url, FeishuTableConstants.WIKI_NODE_TOKEN_PATTERN);
        return callWikiApiToGetAppToken(nodeToken, client);
    }

    /**
     * 从URL中提取nodeToken
     * @param url URL
     * @param pattern 正则表达式模式
     * @return nodeToken
     * @throws FeishuException 提取失败时抛出异常
     */
    private static String extractNodeToken(String url, java.util.regex.Pattern pattern) {
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new FeishuException(FeishuMessageCode.FEISHU_NODE_TOKEN_EXTRACT_FAILED);
    }

    /**
     * 调用wiki API获取appToken
     * @param nodeToken 节点Token
     * @param client 飞书客户端
     * @return appToken
     * @throws FeishuException API调用失败时抛出异常
     */
    private static String callWikiApiToGetAppToken(String nodeToken, Client client) {
        GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder()
                .token(nodeToken)
                .objType(FeishuTableConstants.WIKI_OBJ_TYPE)
                .build();

        try {
            GetNodeSpaceResp resp = client.wiki().v2().space().getNode(req);
            if (!resp.success()) {
                log.error("{} 调用wiki API获取appToken失败, nodeToken: {}, 错误码: {}, 错误信息: {}",
                    FeishuTableConstants.LOG_PREFIX, nodeToken, resp.getCode(), resp.getMsg());
                throw new FeishuException(FeishuMessageCode.FEISHU_API_CALL_FAILED, resp.getMsg());
            }
            return resp.getData().getNode().getObjToken();
        } catch (FeishuException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} 调用wiki API获取appToken异常, nodeToken: {}",
                FeishuTableConstants.LOG_PREFIX, nodeToken, e);
            throw new FeishuException(FeishuMessageCode.FEISHU_API_CALL_FAILED, e.getMessage());
        }
    }

    /**
     * 调用sheets API获取appToken
     * @param nodeToken 节点Token
     * @param client 飞书客户端
     * @return appToken
     * @throws FeishuException API调用失败时抛出异常
     */
    private static String callSheetApiToGetAppToken(String nodeToken, Client client) {
        GetSpreadsheetReq req = GetSpreadsheetReq.newBuilder()
                .spreadsheetToken(nodeToken)
                .build();

        try {
            GetSpreadsheetResp resp = client.sheets().spreadsheet().get(req);
            if (!resp.success()) {
                log.error("{} 调用sheets API获取appToken失败, nodeToken: {}, 错误码: {}, 错误信息: {}",
                    FeishuTableConstants.LOG_PREFIX, nodeToken, resp.getCode(), resp.getMsg());
                throw new FeishuException(FeishuMessageCode.FEISHU_API_CALL_FAILED, resp.getMsg());
            }
            return resp.getData().getSpreadsheet().getToken();
        } catch (FeishuException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} 调用sheets API获取appToken异常, nodeToken: {}",
                FeishuTableConstants.LOG_PREFIX, nodeToken, e);
            throw new FeishuException(FeishuMessageCode.FEISHU_API_CALL_FAILED, e.getMessage());
        }
    }
}
