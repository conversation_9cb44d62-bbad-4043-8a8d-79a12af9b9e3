package cn.genn.feishu.table.model.field;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 带type的字段值（飞书标准格式：{"type": 数字, "value": [值]}）
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class TypedFieldValue extends FieldValue {
    
    /**
     * 字段类型代码
     */
    private Integer typeCode;
    
    /**
     * 原始字段值列表
     * -- GETTER --
     *  获取原始值列表
     *
     * @return 原始值列表

     */
    private List<Object> rawValues;
    
    /**
     * 解析后的字段值列表
     * -- GETTER --
     *  获取解析后的值列表
     *
     * @return 解析后的值列表

     */
    private List<Object> parsedValues;
    
    public TypedFieldValue() {
        setFieldType(FieldType.TEXT);
    }
    
    @SuppressWarnings("unchecked")
    public TypedFieldValue(Object value) {
        this();
        setRawValue(value);
        if (value instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) value;
            
            // 处理type字段，可能是Integer或Double
            Object typeObj = map.get("type");
            if (typeObj instanceof Number) {
                this.typeCode = ((Number) typeObj).intValue();
            }
            
            this.rawValues = (List<Object>) map.get("value");
            
            // 根据type设置字段类型
            if (typeCode != null) {
                setFieldType(FieldType.fromCode(typeCode));
            }
            
            // 根据类型解析values
            this.parsedValues = parseValuesByType();
        }
    }
    
    /**
     * 根据字段类型解析values
     * @return 解析后的值列表
     */
    @SuppressWarnings("unchecked")
    private List<Object> parseValuesByType() {
        if (rawValues == null || rawValues.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Object> parsed = new ArrayList<>();
        
        switch (typeCode) {
            case 11: // 人员字段
                for (Object item : rawValues) {
                    if (item instanceof Map) {
                        Map<String, Object> userMap = (Map<String, Object>) item;
                        UserFieldValue.UserInfo userInfo = new UserFieldValue.UserInfo()
                            .setId((String) userMap.get("id"))
                            .setName((String) userMap.get("name"))
                            .setEnName((String) userMap.get("en_name"))
                            .setEmail((String) userMap.get("email"));
                        parsed.add(userInfo);
                    } else {
                        parsed.add(item);
                    }
                }
                break;
                
            case 1: // 文本字段（可能包含富文本）
                for (Object item : rawValues) {
                    if (item instanceof Map) {
                        Map<String, Object> textMap = (Map<String, Object>) item;
                        if (textMap.containsKey("text") && textMap.containsKey("type")) {
                            MultiLineTextFieldValue.TextSegment segment = new MultiLineTextFieldValue.TextSegment()
                                .setText((String) textMap.get("text"))
                                .setType((String) textMap.get("type"));
                            parsed.add(segment);
                        } else {
                            parsed.add(item);
                        }
                    } else {
                        parsed.add(item);
                    }
                }
                break;
                
            default:
                // 其他类型直接返回原始值
                parsed.addAll(rawValues);
                break;
        }
        
        return parsed;
    }
    
    @Override
    public Object getParsedValue() {
        return parsedValues;
    }

    @Override
    public String getStringValue() {
        if (parsedValues == null || parsedValues.isEmpty()) {
            return "";
        }
        
        // 根据字段类型返回合适的字符串表示
        return switch (typeCode) {
            case 11 -> // 人员字段
                    parsedValues.stream()
                            .filter(UserFieldValue.UserInfo.class::isInstance)
                            .map(UserFieldValue.UserInfo.class::cast)
                            .map(UserFieldValue.UserInfo::getName)
                            .filter(name -> name != null && !name.isEmpty())
                            .reduce((a, b) -> a + ", " + b)
                            .orElse("");
            case 1 -> // 文本字段
                    parsedValues.stream()
                            .map(item -> {
                                if (item instanceof MultiLineTextFieldValue.TextSegment) {
                                    return ((MultiLineTextFieldValue.TextSegment) item).getText();
                                }
                                return item.toString();
                            })
                            .filter(text -> text != null && !text.isEmpty())
                            .reduce((a, b) -> a + "\n" + b)
                            .orElse("");
            default -> {
                // 其他类型直接转换为字符串
                Object firstValue = getFirstParsedValue();
                yield firstValue != null ? firstValue.toString() : "";
            }
        };
    }
    
    /**
     * 获取第一个解析后的值
     * @return 第一个解析后的值，如果没有则返回null
     */
    public Object getFirstParsedValue() {
        return parsedValues != null && !parsedValues.isEmpty() ? parsedValues.getFirst() : null;
    }
    
    /**
     * 获取第一个原始值
     * @return 第一个原始值，如果没有则返回null
     */
    public Object getFirstRawValue() {
        return rawValues != null && !rawValues.isEmpty() ? rawValues.getFirst() : null;
    }
    
    /**
     * 获取值的数量
     * @return 值的数量
     */
    public int getValueCount() {
        return parsedValues != null ? parsedValues.size() : 0;
    }
    
    /**
     * 获取指定类型的解析后值列表
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 指定类型的值列表
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getParsedValuesAs(Class<T> clazz) {
        if (parsedValues == null) {
            return new ArrayList<>();
        }
        
        return parsedValues.stream()
            .filter(clazz::isInstance)
            .map(item -> (T) item)
            .toList();
    }
}