package cn.genn.feishu.table.model.field;

import lombok.Getter;

/**
 * 飞书多维表格字段类型枚举
 * 参考文档：https://open.feishu.cn/document/docs/bitable-v1/app-table-record/bitable-record-data-structure-overview
 * <AUTHOR>
 */
@Getter
public enum FieldType {
    
    /**
     * 文本、邮箱或条码（使用 ui_type 区分）
     */
    TEXT(1, "文本"),
    
    /**
     * 数字、进度、货币或评分（使用 ui_type 区分）
     */
    NUMBER(2, "数字"),
    
    /**
     * 单选
     */
    SINGLE_SELECT(3, "单选"),
    
    /**
     * 多选
     */
    MULTI_SELECT(4, "多选"),
    
    /**
     * 日期
     */
    DATE(5, "日期"),
    
    /**
     * 复选框
     */
    CHECKBOX(7, "复选框"),
    
    /**
     * 人员
     */
    USER(11, "人员"),
    
    /**
     * 电话号码
     */
    PHONE(13, "电话号码"),
    
    /**
     * 超链接
     */
    URL(15, "超链接"),
    
    /**
     * 附件
     */
    ATTACHMENT(17, "附件"),
    
    /**
     * 单向关联
     */
    SINGLE_LINK(18, "单向关联"),
    
    /**
     * 查找引用
     */
    LOOKUP(19, "查找引用"),
    
    /**
     * 公式
     */
    FORMULA(20, "公式"),
    
    /**
     * 双向关联
     */
    DUPLEX_LINK(21, "双向关联"),
    
    /**
     * 地理位置
     */
    LOCATION(22, "地理位置"),
    
    /**
     * 群组
     */
    GROUP_CHAT(23, "群组"),
    
    /**
     * 创建时间
     */
    CREATED_TIME(1001, "创建时间"),
    
    /**
     * 最后更新时间
     */
    MODIFIED_TIME(1002, "最后更新时间"),
    
    /**
     * 创建人
     */
    CREATED_USER(1003, "创建人"),
    
    /**
     * 修改人
     */
    MODIFIED_USER(1004, "修改人"),
    
    /**
     * 自动编号
     */
    AUTO_NUMBER(1005, "自动编号");
    
    private final int code;
    private final String description;
    
    FieldType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取字段类型
     * @param code 字段类型代码
     * @return 字段类型枚举
     */
    public static FieldType fromCode(int code) {
        for (FieldType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return TEXT; // 默认返回文本类型
    }
}