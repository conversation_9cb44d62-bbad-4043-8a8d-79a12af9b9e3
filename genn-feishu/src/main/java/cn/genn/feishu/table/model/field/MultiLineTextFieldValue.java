package cn.genn.feishu.table.model.field;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 多行文本字段值
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class MultiLineTextFieldValue extends FieldValue {
    
    /**
     * 文本段落列表
     */
    private List<TextSegment> segments = new ArrayList<>();
    
    public MultiLineTextFieldValue() {
        setFieldType(FieldType.TEXT);
    }
    
    @SuppressWarnings("unchecked")
    public MultiLineTextFieldValue(Object value) {
        this();
        setRawValue(value);
        if (value instanceof List) {
            List<Object> list = (List<Object>) value;
            for (Object item : list) {
                if (item instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) item;
                    TextSegment segment = new TextSegment()
                        .setText((String) map.get("text"))
                        .setType((String) map.get("type"));
                    this.segments.add(segment);
                } else if (item instanceof String) {
                    // 简单文本
                    this.segments.add(new TextSegment((String) item, "text"));
                }
            }
        } else if (value instanceof String) {
            // 单行文本
            this.segments.add(new TextSegment((String) value, "text"));
        }
    }
    
    /**
     * 获取解析后的值
     * @return 解析后的值
     */
    public Object getParsedValue() {
        return segments;
    }
    
    /**
     * 获取字符串表示
     * @return 字符串值
     */
    public String getStringValue() {
        return segments.stream()
            .map(TextSegment::getText)
            .filter(text -> text != null && !text.isEmpty())
            .collect(Collectors.joining("\n"));
    }
    
    /**
     * 获取纯文本内容（去除格式）
     * @return 纯文本内容
     */
    public String getPlainText() {
        return getStringValue();
    }
    
    /**
     * 获取指定类型的文本段落
     * @param type 段落类型
     * @return 指定类型的文本段落列表
     */
    public List<TextSegment> getSegmentsByType(String type) {
        return segments.stream()
            .filter(segment -> type.equals(segment.getType()))
            .collect(Collectors.toList());
    }
    
    /**
     * 检查是否包含指定类型的段落
     * @param type 段落类型
     * @return 是否包含指定类型的段落
     */
    public boolean hasSegmentType(String type) {
        return segments.stream()
            .anyMatch(segment -> type.equals(segment.getType()));
    }
    
    /**
     * 获取段落数量
     * @return 段落数量
     */
    public int getSegmentCount() {
        return segments.size();
    }
    
    /**
     * 文本段落
     */
    @Data
    @Accessors(chain = true)
    public static class TextSegment {
        /**
         * 文本内容
         */
        private String text;
        
        /**
         * 文本类型，如：text, link, mention等
         */
        private String type;
        
        public TextSegment() {}
        
        public TextSegment(String text, String type) {
            this.text = text;
            this.type = type;
        }
    }
}