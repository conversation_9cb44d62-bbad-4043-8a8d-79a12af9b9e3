package cn.genn.feishu.table.model;

import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 飞书多维表格查询结果
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FeishuBitableQueryResult {

    /**
     * 查询到的记录列表，使用SDK的AppTableRecord类
     */
    private List<AppTableRecord> records;

    /**
     * 是否还有更多数据
     */
    private Boolean hasMore;

    /**
     * 下一页的分页标记
     */
    private String pageToken;

    /**
     * 总记录数（仅在自动分页时有效）
     */
    private Integer totalCount;

    /**
     * 实际查询的页数（仅在自动分页时有效）
     */
    private Integer pageCount;
}