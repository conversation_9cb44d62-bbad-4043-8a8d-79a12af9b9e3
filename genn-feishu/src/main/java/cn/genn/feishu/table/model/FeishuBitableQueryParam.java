package cn.genn.feishu.table.model;

import cn.genn.feishu.base.FeishuBaseInputParam;
import com.lark.oapi.service.bitable.v1.model.FilterInfo;
import com.lark.oapi.service.bitable.v1.model.Sort;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 飞书多维表格查询参数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FeishuBitableQueryParam {

    /**
     * 飞书应用信息
     */
    private FeishuBaseInputParam appInfo;

    /**
     * 多维表格URL
     */
    private String tableUrl;

    /**
     * 控制是否返回自动计算的字段, true 表示返回，false 表示不返回，默认值为 false
     */
    private Boolean automaticFields = false;

    /**
     * 字段名称，用于指定本次查询返回记录中包含的字段
     */
    private String[] fieldNames;

    /**
     * 排序条件列表，使用SDK的Sort类
     */
    private Sort[] sorts;

    /**
     * 过滤条件，使用SDK的FilterInfo类
     */
    private FilterInfo filter;

    /**
     * 分页大小，默认值为 20，最大值为 500
     */
    private Integer pageSize = 20;

    /**
     * 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
     */
    private String pageToken;

    /**
     * 是否自动分页，默认值为 true
     */
    private boolean autoPage = true;

    /**
     * 自动分页时的最大记录数限制，null表示无限制，默认为无限制
     */
    private Integer maxRecords;

    /**
     * 视图ID，可选参数
     */
    private String viewId;
}