package cn.genn.feishu.contact;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.feishu.constants.FeishuCacheConstants;
import cn.genn.feishu.contact.model.*;
import cn.genn.feishu.exception.FeishuMessageCode;
import cn.genn.feishu.utils.FeishuClientUtils;
import cn.genn.feishu.utils.FeishuInvokeUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.Lists;
import com.lark.oapi.Client;
import com.lark.oapi.service.contact.v3.model.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Component
public class FeishuContactQueryUtils {

    private static StringRedisTemplate stringRedisTemplate;

    @Autowired
    public void setUpmSeverProperties(StringRedisTemplate stringRedisTemplate) {
        FSUserInfoUtil.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 手机号获取用户openId
     *
     * @param param
     * @return
     */
    public static List<UserContactInfo> getUserByTelephone(FeishuContactTelephoneParam param) {
        if (CollUtil.isEmpty(param.getTelephones())) {
            return new ArrayList<>();
        }
        Client client = FeishuClientUtils.getClient(
                param.getAppInfo().getAppId(),
                param.getAppInfo().getAppSecret()
        );
        //集团特殊处理手机号
        List<String> telephones = param.getTelephones();
        List<String> toAdd = new ArrayList<>();
        for (String phone : telephones) {
            if (!phone.startsWith("+86")) {
                toAdd.add("+86" + phone);
            }
        }
        telephones.addAll(toAdd);
        List<List<String>> partition = Lists.partition(telephones, 50);
        return partition.stream().map(list -> getUserByTelephone(list, client)).flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 批量获取部门信息
     */
    public static List<Department> getDepartmentBatch(FeishuContactDepartmentParam param) {
        if (CollUtil.isEmpty(param.getDepartmentIds())) {
            return new ArrayList<>();
        }
        Client client = FeishuClientUtils.getClient(
                param.getAppInfo().getAppId(),
                param.getAppInfo().getAppSecret()
        );
        // 飞书单次查询最大50个部门
        List<List<String>> partition = Lists.partition(param.getDepartmentIds(), 50);
        return partition.stream().map(list -> getDepartmentBatch(list, client)).flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 获取部门下用户信息
     */
    public static List<User> getUserListByDepartment(FeishuContactUserByDepartmentParam param) {
        Client client = FeishuClientUtils.getClient(
                param.getAppInfo().getAppId(),
                param.getAppInfo().getAppSecret()
        );
        return getUserListByDepartment(param.getDepartmentId(), client);
    }

    /**
     * 获取子部门列表
     */
    public static List<Department> getDepartmentChildren(FeishuContactDepartmentChildrenParam param) {
        Client client = FeishuClientUtils.getClient(
                param.getAppInfo().getAppId(),
                param.getAppInfo().getAppSecret()
        );
        return getDepartmentChildren(param.getDepartmentId(), client);
    }

    /**
     * openId批量查询用户
     * 支持查询离职员工
     */
    public List<User> userBatch(FeishuOpenIdQueryParam param) {
        List<User> userInfos = new ArrayList<>();
        List<String> notCachedUserIds = new ArrayList<>();

        // 从Redis中获取用户信息
        for (String openId : param.getOpenIds()) {
            String cacheKey = FeishuCacheConstants.getCacheFeishuUserInfo(openId);
            String cachedUserInfo = stringRedisTemplate.opsForValue().get(cacheKey);
            if (cachedUserInfo != null) {
                User userInfo = JsonUtils.parse(cachedUserInfo, User.class);
                userInfos.add(userInfo);
            } else {
                notCachedUserIds.add(openId);
            }
        }
        Client client = FeishuClientUtils.getClient(
                param.getAppInfo().getAppId(),
                param.getAppInfo().getAppSecret()
        );
        // 从飞书中获取未缓存的用户信息
        if (!notCachedUserIds.isEmpty()) {
            // 飞书单次查询最大50人
            List<List<String>> partition = Lists.partition(notCachedUserIds, 40);
            List<User> fetchedUserInfos = partition.stream().flatMap(openIdList -> this.getUserBatch(openIdList,client).stream()).distinct().collect(Collectors.toList());
            userInfos.addAll(fetchedUserInfos);
            // 缓存从飞书中获取的用户信息到Redis
            for (User userInfo : fetchedUserInfos) {
                String cacheKey = FeishuCacheConstants.getCacheFeishuUserInfo(userInfo.getOpenId());
                String userInfoJson = JsonUtils.toJson(userInfo);
                stringRedisTemplate.opsForValue().set(cacheKey, userInfoJson, 1, TimeUnit.DAYS);
            }
        }
        return userInfos;
    }


    /**
     * =================飞书接口简单封装=================
     */

    /**
     * @param telephones 最大数量50
     * @param client
     * @return
     */
    protected static List<UserContactInfo> getUserByTelephone(List<String> telephones, Client client) {
        BatchGetIdUserReq req = BatchGetIdUserReq.newBuilder()
                .userIdType("open_id")
                .batchGetIdUserReqBody(BatchGetIdUserReqBody.newBuilder()
                        .mobiles(telephones.toArray(new String[telephones.size()]))
                        .includeResigned(true)
                        .build())
                .build();
        BatchGetIdUserRespBody body = FeishuInvokeUtil.executeRequest(req, client.contact().user()::batchGetId, FeishuMessageCode.GET_OPEN_ID_FAIL);
        return Arrays.asList(body.getUserList());
    }

    /**
     * 批量获取部门信息
     *
     * @param departmentIds 最大数量50
     * @param client
     * @return
     */
    protected static List<Department> getDepartmentBatch(List<String> departmentIds, Client client) {
        BatchDepartmentReq req = BatchDepartmentReq.newBuilder()
                .departmentIds(departmentIds.toArray(new String[0]))
                .build();
        BatchDepartmentRespBody body = FeishuInvokeUtil.executeRequest(req, client.contact().v3().department()::batch, FeishuMessageCode.DEPARTMENT_INFO_FAIL);
        return Arrays.asList(body.getItems());
    }

    /**
     * 获取部门下用户信息
     *
     * @param departmentId 部门ID
     * @param client
     * @return
     */
    protected static List<User> getUserListByDepartment(String departmentId, Client client) {
        List<User> resultList = new ArrayList<>();
        FindByDepartmentUserReq req = FindByDepartmentUserReq.newBuilder()
                .departmentId(departmentId)
                .pageSize(50)
                .build();
        FindByDepartmentUserRespBody body = FeishuInvokeUtil.executeRequest(req, client.contact().v3().user()::findByDepartment, FeishuMessageCode.USER_INFO_FAIL);
        if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
            resultList.addAll(Arrays.asList(body.getItems()));
            while (ObjUtil.isNotNull(body.getPageToken()) && !body.getPageToken().isEmpty()) {
                req = FindByDepartmentUserReq.newBuilder().departmentId(departmentId).pageSize(50).pageToken(body.getPageToken()).build();
                body = FeishuInvokeUtil.executeRequest(req, client.contact().v3().user()::findByDepartment, FeishuMessageCode.USER_INFO_FAIL);
                if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
                    resultList.addAll(Arrays.asList(body.getItems()));
                }
            }
        }
        return resultList;
    }

    /**
     * 获取子部门列表
     *
     * @param departmentId 部门ID
     * @param client
     * @return
     */
    protected static List<Department> getDepartmentChildren(String departmentId, Client client) {
        List<Department> resultList = new ArrayList<>();
        ChildrenDepartmentReq req = ChildrenDepartmentReq.newBuilder()
                .departmentId(departmentId)
                .pageSize(50)
                .build();
        ChildrenDepartmentRespBody body = FeishuInvokeUtil.executeRequest(req, client.contact().v3().department()::children, FeishuMessageCode.CHILDREN_DEPARTMENT_INFO_FAIL);
        if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
            resultList.addAll(Arrays.asList(body.getItems()));
            while (ObjUtil.isNotNull(body.getPageToken()) && !body.getPageToken().isEmpty()) {
                req = ChildrenDepartmentReq.newBuilder().departmentId(departmentId).pageSize(50).pageToken(body.getPageToken()).build();
                body = FeishuInvokeUtil.executeRequest(req, client.contact().v3().department()::children, FeishuMessageCode.CHILDREN_DEPARTMENT_INFO_FAIL);
                if (ObjUtil.isNotNull(body) && ObjUtil.isNotNull(body.getItems()) && body.getItems().length > 0) {
                    resultList.addAll(Arrays.asList(body.getItems()));
                }
            }
        }
        return resultList;
    }

    /**
     * openIds批量查询接口
     * @param openIds
     * @return
     */
    protected List<User> getUserBatch(List<String> openIds, Client client) {
        BatchUserReq req = BatchUserReq.newBuilder()
                .userIdType("open_id")
                .userIds(openIds.toArray(new String[0]))
                .build();
        BatchUserRespBody body = FeishuInvokeUtil.executeRequest(req, client.contact().v3().user()::batch, FeishuMessageCode.USER_INFO_FAIL);
        return Arrays.asList(body.getItems());
    }

}
