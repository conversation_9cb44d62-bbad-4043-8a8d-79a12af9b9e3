package cn.genn.feishu.utils;

import cn.genn.feishu.exception.FeishuException;
import cn.genn.feishu.exception.FeishuMessageCode;
import com.lark.oapi.Client;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class FeishuClientUtils {

    private static final ConcurrentHashMap<String, Client> clientCache = new ConcurrentHashMap<>();

    /**
     * 获取飞书客户端
     * @param appId 飞书应用id
     * @param appSecret 飞书应用密钥
     * @return Client
     */
    public static Client getClient(String appId, String appSecret) {
        if (appId == null || appSecret == null) {
            log.error("[FeishuClientService] App ID or App Secret is null: appId={}, appSecret={}", appId, appSecret);
            throw new FeishuException(FeishuMessageCode.FEISHU_CLIENT_NOT_FOUND);
        }
        return clientCache.computeIfAbsent(appId, id -> new Client.Builder(appId, appSecret).build());
    }

    public static Client getClient(String appId) {
        Client client = clientCache.get(appId);
        if (client == null) {
            log.error("[FeishuClientService] Client not found for appId: {}", appId);
            throw new FeishuException(FeishuMessageCode.FEISHU_CLIENT_NOT_FOUND);
        }
        return client;
    }

    public static void clear() {
        clientCache.clear();
    }

    public static void clear(String appId) {
        clientCache.remove(appId);
    }
}
