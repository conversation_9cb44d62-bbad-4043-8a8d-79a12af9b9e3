package cn.genn.feishu.utils;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.feishu.base.FeishuRequestExecutor;
import cn.genn.feishu.exception.FeishuException;
import cn.genn.feishu.exception.FeishuMessageCode;
import com.lark.oapi.core.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 飞书调用工具
 * @date 2024-12-25
 */
@Slf4j
public class FeishuInvokeUtil {

    private static <R> R handleResponse(BaseResponse<R> response, FeishuMessageCode errorCode, long startTime) {
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        if (!response.success()) {
            log.error("Feishu Request failed, code: {}, msg: {}", response.getCode(), response.getMsg());
            throw new FeishuException(errorCode.buildCode(), response.getMsg());
        }
        log.info("Feishu Response data: {}, took {} ms.", JsonUtils.toJson(response.getData()), executionTime);
        return response.getData();
    }

    public static <T, R> R executeRequest(T request, FeishuRequestExecutor<T, R> executor, FeishuMessageCode errorCode) {
        log.info("Feishu Request: {}", JsonUtils.toJson(request));
        try {
            long startTime = System.currentTimeMillis();
            BaseResponse<R> response = executor.execute(request);
            return handleResponse(response, errorCode, startTime);
        } catch (Exception e) {
            log.error("Feishu Request error: {}", e.getMessage(), e);
            throw new FeishuException(errorCode);
        }
    }

}
