package cn.genn.graph.core;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.action.AsyncEdgeAction;
import com.alibaba.cloud.ai.graph.action.AsyncNodeAction;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;

import java.util.List;
import java.util.Map;

import static com.alibaba.cloud.ai.graph.StateGraph.END;
import static com.alibaba.cloud.ai.graph.StateGraph.START;

/**
 * ModularStateGraph 边合并专项测试
 * 
 * 专门测试各种边的合并情况：
 * 1. 普通边的覆盖
 * 2. 条件边的覆盖
 * 3. 普通边与条件边的冲突
 * 4. 多源边的处理
 * 5. 边的链式覆盖
 * 6. START和END边的特殊处理
 * 
 * <AUTHOR>
 */
public class ModularStateGraphEdgeMergeTests {
    
    /**
     * 测试用例1：普通边的简单覆盖
     */
    public static void testSimpleEdgeOverride() {
        System.out.println("=== 测试用例1：普通边的简单覆盖 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("SimpleEdgeOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("source", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行源节点");
                        return Map.of("from", "source");
                    }));
                    this.addNode("target1", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行目标节点1（内置路径）");
                        return Map.of("target", "target1");
                    }));
                    this.addNode("target2", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行目标节点2（自定义路径）");
                        return Map.of("target", "target2");
                    }));
                    
                    // 内置边：source -> target1
                    this.addEdge(START, "source");
                    this.addEdge("source", "target1");
                    this.addEdge("target1", END);
                }
            };
            
            // 自定义边覆盖：source -> target2
            graph.addEdge("source", "target2"); // 应该覆盖 source -> target1
            graph.addEdge("target2", END);
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 简单边覆盖测试通过（应该执行source->target2路径）");
        } catch (Exception e) {
            System.err.println("✗ 简单边覆盖测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例2：条件边覆盖普通边
     */
    public static void testConditionalOverrideNormal() {
        System.out.println("\n=== 测试用例2：条件边覆盖普通边 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("ConditionalOverrideNormalTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("decision", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行决策节点");
                        return Map.of("choice", "path_a");
                    }));
                    this.addNode("default_target", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行默认目标（不应该被执行）");
                        return Map.of("path", "default");
                    }));
                    this.addNode("path_a", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行路径A");
                        return Map.of("path", "a");
                    }));
                    this.addNode("path_b", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行路径B");
                        return Map.of("path", "b");
                    }));
                    
                    // 内置普通边
                    this.addEdge(START, "decision");
                    this.addEdge("decision", "default_target"); // 这条边应该被条件边覆盖
                    this.addEdge("default_target", END);
                }
            };
            
            // 自定义条件边覆盖普通边
            AsyncEdgeAction condition = AsyncEdgeAction.edge_async(state -> {
                String choice = state.value("choice", String.class).orElse("path_b");
                System.out.println("条件判断结果: " + choice);
                return choice;
            });
            
            graph.addConditionalEdges("decision", condition, Map.of(
                "path_a", "path_a",
                "path_b", "path_b"
            ));
            graph.addEdge("path_a", END);
            graph.addEdge("path_b", END);
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 条件边覆盖普通边测试通过");
        } catch (Exception e) {
            System.err.println("✗ 条件边覆盖普通边测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例3：普通边覆盖条件边
     */
    public static void testNormalOverrideConditional() {
        System.out.println("\n=== 测试用例3：普通边覆盖条件边 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("NormalOverrideConditionalTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("router", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行路由节点");
                        return Map.of("route", "complex_path");
                    }));
                    this.addNode("complex_path", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行复杂路径（不应该被执行）");
                        return Map.of("path", "complex");
                    }));
                    this.addNode("simple_path", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行简单路径");
                        return Map.of("path", "simple");
                    }));
                    this.addNode("alternative_path", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行替代路径");
                        return Map.of("path", "alternative");
                    }));
                    
                    // 内置条件边
                    AsyncEdgeAction builtinCondition = AsyncEdgeAction.edge_async(state -> {
                        System.out.println("执行内置条件判断（不应该被执行）");
                        return "complex_path";
                    });
                    
                    this.addEdge(START, "router");
                    this.addConditionalEdges("router", builtinCondition, Map.of(
                        "complex_path", "complex_path",
                        "alternative_path", "alternative_path"
                    ));
                    this.addEdge("complex_path", END);
                    this.addEdge("alternative_path", END);
                }
            };
            
            // 自定义普通边覆盖条件边
            graph.addEdge("router", "simple_path"); // 应该覆盖条件边
            graph.addEdge("simple_path", END);
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 普通边覆盖条件边测试通过");
        } catch (Exception e) {
            System.err.println("✗ 普通边覆盖条件边测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例4：START边的覆盖
     */
    public static void testStartEdgeOverride() {
        System.out.println("\n=== 测试用例4：START边的覆盖 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("StartEdgeOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("builtin_entry", AsyncNodeAction.node_async(state -> {
                        System.out.println("内置入口节点（不应该被执行）");
                        return Map.of("entry", "builtin");
                    }));
                    this.addNode("common_node", AsyncNodeAction.node_async(state -> {
                        System.out.println("公共节点");
                        String entry = state.value("entry", String.class).orElse("unknown");
                        System.out.println("入口类型: " + entry);
                        return Map.of("processed", true);
                    }));
                    
                    // 内置START边
                    this.addEdge(START, "builtin_entry");
                    this.addEdge("builtin_entry", "common_node");
                    this.addEdge("common_node", END);
                }
            };
            
            // 添加自定义入口节点
            graph.addNode("custom_entry", AsyncNodeAction.node_async(state -> {
                System.out.println("自定义入口节点");
                return Map.of("entry", "custom");
            }));
            
            // 自定义START边覆盖内置START边
            graph.addEdge(START, "custom_entry"); // 应该覆盖 START -> builtin_entry
            graph.addEdge("custom_entry", "common_node");
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ START边覆盖测试通过");
        } catch (Exception e) {
            System.err.println("✗ START边覆盖测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例5：END边的覆盖
     */
    public static void testEndEdgeOverride() {
        System.out.println("\n=== 测试用例5：END边的覆盖 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("EndEdgeOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("processor", AsyncNodeAction.node_async(state -> {
                        System.out.println("处理器节点");
                        return Map.of("processed", true);
                    }));
                    this.addNode("builtin_finalizer", AsyncNodeAction.node_async(state -> {
                        System.out.println("内置终结器（不应该被执行）");
                        return Map.of("finalized_by", "builtin");
                    }));
                    
                    // 内置边
                    this.addEdge(START, "processor");
                    this.addEdge("processor", "builtin_finalizer");
                    this.addEdge("builtin_finalizer", END);
                }
            };
            
            // 添加自定义终结器
            graph.addNode("custom_finalizer", AsyncNodeAction.node_async(state -> {
                System.out.println("自定义终结器");
                return Map.of("finalized_by", "custom");
            }));
            
            // 覆盖到END的边
            graph.addEdge("processor", "custom_finalizer"); // 覆盖 processor -> builtin_finalizer
            graph.addEdge("custom_finalizer", END);
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ END边覆盖测试通过");
        } catch (Exception e) {
            System.err.println("✗ END边覆盖测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例6：链式边覆盖
     */
    public static void testChainedEdgeOverride() {
        System.out.println("\n=== 测试用例6：链式边覆盖 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("ChainedEdgeOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点链：A -> B -> C -> D
                    this.addNode("node_a", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行节点A");
                        return Map.of("current", "a");
                    }));
                    this.addNode("node_b", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行节点B（可能被跳过）");
                        return Map.of("current", "b");
                    }));
                    this.addNode("node_c", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行节点C（可能被跳过）");
                        return Map.of("current", "c");
                    }));
                    this.addNode("node_d", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行节点D");
                        String current = state.value("current", String.class).orElse("unknown");
                        System.out.println("从节点 " + current + " 到达D");
                        return Map.of("current", "d");
                    }));
                    
                    // 内置边链
                    this.addEdge(START, "node_a");
                    this.addEdge("node_a", "node_b");
                    this.addEdge("node_b", "node_c");
                    this.addEdge("node_c", "node_d");
                    this.addEdge("node_d", END);
                }
            };
            
            // 自定义边：A -> D（跳过B和C）
            graph.addEdge("node_a", "node_d"); // 覆盖 node_a -> node_b
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 链式边覆盖测试通过（应该直接从A跳到D）");
        } catch (Exception e) {
            System.err.println("✗ 链式边覆盖测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例7：复杂边覆盖场景
     */
    public static void testComplexEdgeOverrideScenario() {
        System.out.println("\n=== 测试用例7：复杂边覆盖场景 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("ComplexEdgeOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置复杂流程
                    this.addNode("init", AsyncNodeAction.node_async(state -> {
                        System.out.println("初始化");
                        return Map.of("initialized", true);
                    }));
                    this.addNode("validate", AsyncNodeAction.node_async(state -> {
                        System.out.println("验证");
                        return Map.of("valid", true);
                    }));
                    this.addNode("process", AsyncNodeAction.node_async(state -> {
                        System.out.println("处理");
                        return Map.of("processed", true);
                    }));
                    this.addNode("cleanup", AsyncNodeAction.node_async(state -> {
                        System.out.println("清理");
                        return Map.of("cleaned", true);
                    }));
                    
                    // 内置边和条件边
                    this.addEdge(START, "init");
                    this.addEdge("init", "validate");
                    
                    // 内置条件边
                    AsyncEdgeAction validationCondition = AsyncEdgeAction.edge_async(state -> {
                        System.out.println("内置验证条件（不应该执行）");
                        return "process";
                    });
                    this.addConditionalEdges("validate", validationCondition, Map.of(
                        "process", "process",
                        "cleanup", "cleanup"
                    ));
                    
                    this.addEdge("process", "cleanup");
                    this.addEdge("cleanup", END);
                }
            };
            
            // 添加自定义节点
            graph.addNode("custom_validator", AsyncNodeAction.node_async(state -> {
                System.out.println("自定义验证器");
                return Map.of("custom_validated", true);
            }));
            
            graph.addNode("custom_processor", AsyncNodeAction.node_async(state -> {
                System.out.println("自定义处理器");
                return Map.of("custom_processed", true);
            }));
            
            // 复杂的边覆盖：
            // 1. 插入自定义验证器
            graph.addEdge("init", "custom_validator");        // 覆盖 init -> validate
            graph.addEdge("custom_validator", "validate");     // 新边
            
            // 2. 用普通边覆盖条件边
            graph.addEdge("validate", "custom_processor");     // 覆盖条件边
            graph.addEdge("custom_processor", "process");      // 新边
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 复杂边覆盖场景测试通过");
        } catch (Exception e) {
            System.err.println("✗ 复杂边覆盖场景测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 运行所有边合并测试
     */
    public static void runAllEdgeMergeTests() {
        System.out.println("开始运行 ModularStateGraph 边合并专项测试...\n");
        
        testSimpleEdgeOverride();
        testConditionalOverrideNormal();
        testNormalOverrideConditional();
        testStartEdgeOverride();
        testEndEdgeOverride();
        testChainedEdgeOverride();
        testComplexEdgeOverrideScenario();
        
        System.out.println("\n=== ModularStateGraph 边合并测试套件执行完成 ===");
    }
    
    public static void main(String[] args) {
        runAllEdgeMergeTests();
    }
}
