package cn.genn.graph.core;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.action.AsyncNodeAction;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.alibaba.cloud.ai.graph.StateGraph.END;
import static com.alibaba.cloud.ai.graph.StateGraph.START;

/**
 * ModularStateGraph 所有合并测试的综合运行器
 * 
 * 包含：
 * 1. 节点和边合并测试
 * 2. 边专项合并测试
 * 3. 异常情况测试
 * 4. 性能测试
 * 5. 详细的测试报告
 * 
 * <AUTHOR>
 */
public class ModularStateGraphAllMergeTests {
    
    private static final List<TestResult> testResults = new ArrayList<>();
    
    /**
     * 测试结果记录
     */
    private static class TestResult {
        private final String testName;
        private final boolean passed;
        private final long executionTime;
        private final String errorMessage;
        
        public TestResult(String testName, boolean passed, long executionTime, String errorMessage) {
            this.testName = testName;
            this.passed = passed;
            this.executionTime = executionTime;
            this.errorMessage = errorMessage;
        }
        
        public String getTestName() { return testName; }
        public boolean isPassed() { return passed; }
        public long getExecutionTime() { return executionTime; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * 运行测试并记录结果
     */
    private static void runTestSuite(String suiteName, Runnable testSuite) {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("开始运行测试套件: " + suiteName);
        System.out.println("=".repeat(60));
        
        long startTime = System.currentTimeMillis();
        boolean passed = true;
        String errorMessage = null;
        
        try {
            testSuite.run();
        } catch (Exception e) {
            passed = false;
            errorMessage = e.getMessage();
            System.err.println("测试套件 " + suiteName + " 执行失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        long executionTime = System.currentTimeMillis() - startTime;
        testResults.add(new TestResult(suiteName, passed, executionTime, errorMessage));
        
        System.out.println("\n测试套件 " + suiteName + " 执行完成");
        System.out.println("状态: " + (passed ? "✓ 通过" : "✗ 失败"));
        System.out.println("耗时: " + executionTime + "ms");
        if (!passed && errorMessage != null) {
            System.out.println("错误: " + errorMessage);
        }
    }
    
    /**
     * 异常情况测试
     */
    public static void testExceptionScenarios() {
        System.out.println("=== 异常情况测试 ===");
        
        // 测试1：空的buildWorkflow
        try {
            ModularStateGraph emptyGraph = new ModularStateGraph("EmptyWorkflowTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 空的内置工作流
                }
            };
            
            // 只添加自定义节点和边
            emptyGraph.addNode("only_custom", AsyncNodeAction.node_async(state -> {
                System.out.println("只有自定义节点");
                return Map.of("result", "custom_only");
            }));
            emptyGraph.addEdge(START, "only_custom");
            emptyGraph.addEdge("only_custom", END);
            
            CompiledGraph compiled = emptyGraph.compile();
            compiled.invoke(Map.of());
            System.out.println("✓ 空内置工作流测试通过");
            
        } catch (Exception e) {
            System.err.println("✗ 空内置工作流测试失败: " + e.getMessage());
        }
        
        // 测试2：只有内置工作流，无自定义
        try {
            ModularStateGraph builtinOnlyGraph = new ModularStateGraph("BuiltinOnlyTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    this.addNode("builtin_only", AsyncNodeAction.node_async(state -> {
                        System.out.println("只有内置节点");
                        return Map.of("result", "builtin_only");
                    }));
                    this.addEdge(START, "builtin_only");
                    this.addEdge("builtin_only", END);
                }
            };
            
            // 不添加任何自定义节点和边
            CompiledGraph compiled = builtinOnlyGraph.compile();
            compiled.invoke(Map.of());
            System.out.println("✓ 只有内置工作流测试通过");
            
        } catch (Exception e) {
            System.err.println("✗ 只有内置工作流测试失败: " + e.getMessage());
        }
        
        // 测试3：重复编译
        try {
            ModularStateGraph repeatCompileGraph = new ModularStateGraph("RepeatCompileTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    this.addNode("test_node", AsyncNodeAction.node_async(state -> {
                        System.out.println("测试重复编译");
                        return Map.of("compiled", true);
                    }));
                    this.addEdge(START, "test_node");
                    this.addEdge("test_node", END);
                }
            };
            
            // 多次编译同一个图
            CompiledGraph compiled1 = repeatCompileGraph.compile();
            CompiledGraph compiled2 = repeatCompileGraph.compile();
            CompiledGraph compiled3 = repeatCompileGraph.compile();
            
            // 应该返回同一个实例
            if (compiled1 == compiled2 && compiled2 == compiled3) {
                System.out.println("✓ 重复编译测试通过（返回同一实例）");
            } else {
                System.err.println("✗ 重复编译测试失败（返回不同实例）");
            }
            
            compiled1.invoke(Map.of());
            
        } catch (Exception e) {
            System.err.println("✗ 重复编译测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 性能测试：大量节点和边的合并
     */
    public static void testLargeScaleMerge() {
        System.out.println("\n=== 大规模合并性能测试 ===");
        
        try {
            long startTime = System.currentTimeMillis();
            
            ModularStateGraph largeGraph = new ModularStateGraph("LargeScaleTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 创建100个内置节点
                    for (int i = 0; i < 100; i++) {
                        final int nodeIndex = i;
                        this.addNode("builtin_" + i, AsyncNodeAction.node_async(state -> {
                            return Map.of("builtin_node", nodeIndex);
                        }));
                    }
                    
                    // 创建内置边链
                    this.addEdge(START, "builtin_0");
                    for (int i = 0; i < 99; i++) {
                        this.addEdge("builtin_" + i, "builtin_" + (i + 1));
                    }
                    this.addEdge("builtin_99", END);
                }
            };
            
            // 添加100个自定义节点
            for (int i = 0; i < 100; i++) {
                final int nodeIndex = i;
                largeGraph.addNode("custom_" + i, AsyncNodeAction.node_async(state -> {
                    return Map.of("custom_node", nodeIndex);
                }));
            }
            
            // 覆盖一些内置边，插入自定义节点
            for (int i = 0; i < 50; i++) {
                if (i % 10 == 0) { // 每10个节点插入一个自定义节点
                    largeGraph.addEdge("builtin_" + i, "custom_" + i);
                    largeGraph.addEdge("custom_" + i, "builtin_" + (i + 1));
                }
            }
            
            long buildTime = System.currentTimeMillis() - startTime;
            System.out.println("图构建耗时: " + buildTime + "ms");
            
            // 编译
            long compileStart = System.currentTimeMillis();
            CompiledGraph compiled = largeGraph.compile();
            long compileTime = System.currentTimeMillis() - compileStart;
            System.out.println("图编译耗时: " + compileTime + "ms");
            
            // 执行（只执行一小部分以节省时间）
            long executeStart = System.currentTimeMillis();
            compiled.invoke(Map.of());
            long executeTime = System.currentTimeMillis() - executeStart;
            System.out.println("图执行耗时: " + executeTime + "ms");
            
            long totalTime = System.currentTimeMillis() - startTime;
            System.out.println("总耗时: " + totalTime + "ms");
            System.out.println("✓ 大规模合并性能测试通过");
            
        } catch (Exception e) {
            System.err.println("✗ 大规模合并性能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 并发合并测试
     */
    public static void testConcurrentMerge() {
        System.out.println("\n=== 并发合并测试 ===");
        
        try {
            ModularStateGraph concurrentGraph = new ModularStateGraph("ConcurrentTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    this.addNode("shared_builtin", AsyncNodeAction.node_async(state -> {
                        System.out.println("内置共享节点");
                        return Map.of("builtin", true);
                    }));
                    this.addEdge(START, "shared_builtin");
                    this.addEdge("shared_builtin", END);
                }
            };
            
            // 多线程同时添加自定义节点
            List<Thread> threads = new ArrayList<>();
            for (int i = 0; i < 5; i++) {
                final int threadId = i;
                Thread thread = new Thread(() -> {
                    try {
                        // 每个线程添加自己的节点
                        concurrentGraph.addNode("thread_" + threadId, AsyncNodeAction.node_async(state -> {
                            System.out.println("线程 " + threadId + " 的节点");
                            return Map.of("thread_id", threadId);
                        }));
                        
                        // 尝试覆盖共享节点（只有一个会生效）
                        concurrentGraph.addNode("shared_builtin", AsyncNodeAction.node_async(state -> {
                            System.out.println("线程 " + threadId + " 覆盖的共享节点");
                            return Map.of("overridden_by_thread", threadId);
                        }));
                        
                        Thread.sleep(10); // 模拟一些处理时间
                        
                    } catch (Exception e) {
                        System.err.println("线程 " + threadId + " 执行失败: " + e.getMessage());
                    }
                });
                threads.add(thread);
                thread.start();
            }
            
            // 等待所有线程完成
            for (Thread thread : threads) {
                thread.join(1000);
            }
            
            // 编译和执行
            CompiledGraph compiled = concurrentGraph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 并发合并测试通过");
            
        } catch (Exception e) {
            System.err.println("✗ 并发合并测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 打印测试总结报告
     */
    private static void printTestSummary() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("ModularStateGraph 合并测试总结报告");
        System.out.println("=".repeat(80));
        
        int totalTests = testResults.size();
        long passedTests = testResults.stream().mapToLong(r -> r.isPassed() ? 1 : 0).sum();
        long totalTime = testResults.stream().mapToLong(TestResult::getExecutionTime).sum();
        
        System.out.println("总体统计:");
        System.out.println("- 测试套件总数: " + totalTests);
        System.out.println("- 通过套件数: " + passedTests);
        System.out.println("- 失败套件数: " + (totalTests - passedTests));
        System.out.println("- 总执行时间: " + totalTime + "ms");
        System.out.println("- 成功率: " + String.format("%.1f", (passedTests * 100.0 / totalTests)) + "%");
        
        System.out.println("\n详细结果:");
        for (TestResult result : testResults) {
            String status = result.isPassed() ? "✓ 通过" : "✗ 失败";
            System.out.printf("- %-40s %s (%dms)%n", 
                result.getTestName(), status, result.getExecutionTime());
            if (!result.isPassed() && result.getErrorMessage() != null) {
                System.out.println("  错误: " + result.getErrorMessage());
            }
        }
        
        if (passedTests == totalTests) {
            System.out.println("\n🎉 所有ModularStateGraph合并测试都通过了！");
        } else {
            System.out.println("\n⚠️  有 " + (totalTests - passedTests) + " 个测试套件失败，请检查上述错误信息。");
        }
        
        System.out.println("=".repeat(80));
    }
    
    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        System.out.println("ModularStateGraph 内置节点和自定义节点合并测试套件");
        System.out.println("开始时间: " + new java.util.Date());
        
        long overallStartTime = System.currentTimeMillis();
        
        // 运行节点和边合并测试
        runTestSuite("节点和边合并测试", ModularStateGraphMergeTests::runAllMergeTests);
        
        // 运行边专项合并测试
        runTestSuite("边专项合并测试", ModularStateGraphEdgeMergeTests::runAllEdgeMergeTests);
        
        // 运行异常情况测试
        runTestSuite("异常情况测试", ModularStateGraphAllMergeTests::testExceptionScenarios);
        
        // 运行性能测试
        runTestSuite("大规模合并性能测试", ModularStateGraphAllMergeTests::testLargeScaleMerge);
        
        // 运行并发测试
        runTestSuite("并发合并测试", ModularStateGraphAllMergeTests::testConcurrentMerge);
        
        long overallTime = System.currentTimeMillis() - overallStartTime;
        
        // 打印总结报告
        printTestSummary();
        
        System.out.println("\n测试套件总执行时间: " + overallTime + "ms");
        System.out.println("结束时间: " + new java.util.Date());
    }
}
