package cn.genn.graph.core;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.action.AsyncNodeAction;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class TestStateGraph extends ModularStateGraph {
    
    public TestStateGraph(String name, List<ToolCallback> tools) {
        super(name, tools);
    }

    @Override
    protected void buildWorkflow() throws GraphStateException {
        this.addNode("step1", AsyncNodeAction.node_async(state -> {
            System.out.println("Executing step 1");
            return Map.of();
        }));
        this.addNode("step2", AsyncNodeAction.node_async(state -> {
            System.out.println("Executing step 2");
            return Map.of();
        }));
        this.addNode("step3", AsyncNodeAction.node_async(state -> {
            System.out.println("Executing step 3");
            return Map.of();
        }));
        this.addEdge(START, "step1");
        this.addEdge("step1", "step2");
        this.addEdge("step2", "step3");
        this.addEdge("step3", END);
    }

    public static void main(String[] args) {
        try {
            TestStateGraph graph = new TestStateGraph("TestGraph", List.of());
            graph.addNode("custom1", AsyncNodeAction.node_async(state -> {
                System.out.println("Executing custom step 1");
                return Map.of();
            }));
            graph.addNode("custom2", AsyncNodeAction.node_async(state -> {
                System.out.println("Executing custom step 2");
                return Map.of();
            }));
            graph.addEdge(START, "custom1");
            graph.addEdge("custom1", "custom2");
            graph.addEdge("custom2", "step1");
            CompiledGraph compile = graph.compile();
            compile.invoke(Map.of());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
