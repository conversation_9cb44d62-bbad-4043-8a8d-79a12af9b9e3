package cn.genn.graph.core;

import cn.genn.ai.cerebro.service.CerebroService;
import cn.genn.ai.tools.CerebroToolCallback;
import cn.genn.ai.tools.model.Tool;
import cn.genn.ai.tools.model.ToolInputSchemaBuilder;
import cn.genn.core.model.KVStruct;
import cn.genn.graph.adapter.StreamingToolResultGenerator;
import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.tools.StateGraphToolCallback;
import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.NodeOutput;
import com.alibaba.cloud.ai.graph.RunnableConfig;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.async.AsyncGenerator;
import com.alibaba.cloud.ai.graph.streaming.StreamingOutput;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.alibaba.cloud.ai.graph.action.AsyncNodeAction.node_async;

/**
 * <AUTHOR>
 */
public class ToTStateGraphTest {

    public static void testToTFlow() throws Exception {

        String totOriginInfo = """
                {
                    "tot_id": "TOT-MOTOR-TEMP-DIAGNOSIS-001",
                    "name": "电机定子温度异常诊断思维树",
                    "version": "1.0",
                    "nodes": [
                        {
                            "id": "n1",
                            "name": "数据特征分析",
                            "type": "START",
                            "prompt": "分析温度变化特征",
                            "tools": [
                                {
                                    "name": "定子温度数据特征分析专家",
                                    "params": {
                                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1",
                                        "告警时间": "2025-07-28 10:42:35",
                                        "报警值": "95℃"
                                    }
                                }
                            ],
                            "next": [
                                {
                                    "condition": "result == '趋势性升高'",
                                    "target": "n2"
                                },
                                {
                                    "condition": "result == '突变式升高'",
                                    "target": "n4"
                                }
                            ]
                        },
                        {
                            "id": "n2",
                            "name": "趋势性升高分析",
                            "type": "THOUGHT",
                            "prompt": "检测到温度持续缓慢上升趋势",
                            "tools": [
                                {
                                    "name": "定子温度趋势性升高诊断专家",
                                    "params": {
                                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1"
                                    }
                                }
                            ],
                            "next": [
                                {
                                    "condition": "result == '三相温度同步升高'",
                                    "target": "n3"
                                },
                                {
                                    "condition": "result == '单相温度异常突出'",
                                    "target": "n6"
                                }
                            ]
                        },
                        {
                            "id": "n3",
                            "name": "系统性问题诊断",
                            "type": "AGENT_EXECUTION",
                            "prompt": "三相温度同步升高，可能存在系统性问题",
                            "tools": [
                                {
                                    "name": "三相温度同步升高分析诊断专家",
                                    "params": {
                                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1",
                                        "需要综合判断的定子组名称": ["TE-81518A","TE-81518B","TE-81518C"]
                                    }
                                }
                            ]
                        },
                        {
                            "id": "n6",
                            "name": "局部问题诊断",
                            "type": "AGENT_EXECUTION",
                            "prompt": "单相温度异常突出，可能存在局部故障",
                            "tools": [
                                {
                                    "name": "单相突出升高分析诊断专家",
                                    "params": {
                                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1",
                                        "需要综合判断的定子组名称": ["TE-81518A","TE-81518B","TE-81518C"]
                                    }
                                }
                            ]
                        },
                        {
                            "id": "n4",
                            "name": "突变式升高诊断",
                            "type": "AGENT_EXECUTION",
                            "prompt": "检测到温度突变式升高，需紧急分析",
                            "tools": [
                                {
                                    "name": "定子温度短时间跃升诊断专家",
                                    "params": {
                                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1"
                                    }
                                }
                            ]
                        }
                    ]
                }
                """;

        StateGraph agent1StateGraph = new StateGraph();

        agent1StateGraph.addNode("node1", node_async((state) -> {
            System.out.println("定子温度数据特征分析专家执行了");
            return Map.of(StateKey.TOOL_SUB_GRAPH_RESULT.getKey(), "定子过热,当前温度为100度,属于趋势性升高");
        }));

        agent1StateGraph.addEdge(StateGraph.START, "node1");
        agent1StateGraph.addEdge("node1", StateGraph.END);

        StateGraphToolCallback agent1 = new StateGraphToolCallback(agent1StateGraph,
                new Tool("定子温度数据特征分析专家", "定子温度数据特征分析专家",
                        ToolInputSchemaBuilder
                                .object()
                                .string("报警的定子名称").description("报警的定子名称").required().end()
                                .string("告警时间").description("告警时间").required().end()
                                .string("报警值").description("报警值").required().end()
                                .build()));


        StateGraph agent2StateGraph = new StateGraph();
        agent2StateGraph.addNode("node1", node_async((state) -> {
            System.out.println("定子温度趋势性升高诊断专家执行了");
            return Map.of(StateKey.TOOL_SUB_GRAPH_RESULT.getKey(), "三相温度同步升高");
        }));

        agent2StateGraph.addEdge(StateGraph.START, "node1");
        agent2StateGraph.addEdge("node1", StateGraph.END);

        StateGraphToolCallback agent2 = new StateGraphToolCallback(agent2StateGraph,
                new Tool("定子温度趋势性升高诊断专家", "定子温度趋势性升高诊断专家",
                        ToolInputSchemaBuilder
                                .object()
                                .string("报警的定子名称").description("报警的定子名称").required().end()
                                .build()));

        StateGraph agent3StateGraph = new StateGraph();
        agent3StateGraph.addNode("node1", node_async((state) -> {
            System.out.println("三相温度同步升高分析诊断专家执行了");
            return Map.of(StateKey.TOOL_SUB_GRAPH_RESULT.getKey(), "存在系统性问题");
        }));

        agent3StateGraph.addEdge(StateGraph.START, "node1");
        agent3StateGraph.addEdge("node1", StateGraph.END);


        StateGraphToolCallback agent3 = new StateGraphToolCallback(agent3StateGraph,
                new Tool("三相温度同步升高分析诊断专家", "三相温度同步升高分析诊断专家",
                        ToolInputSchemaBuilder
                                .object()
                                .string("报警的定子名称").description("报警的定子名称").required().end()
                                .array("需要综合判断的定子组名称").description("需要综合判断的定子组名称").stringItems().end()
                                .build()));

        ToTStateGraph graph = new ToTStateGraph("你是一个设备故障诊断流程的控制中枢，你需要遵循行业的故障诊断模式，结合思维树，对用户提取的问题进行逐步排查，你可以根据需求调用工具完成某个节点的故障检查，你需要经过多步的排查，得到最终的故障原因。\n",
                totOriginInfo, List.of(agent1, agent2, agent3));
        CompiledGraph compiledGraph = graph.compile();
        String threadId = "thread1";
        AsyncGenerator<NodeOutput> asyncGenerator = compiledGraph.stream(
                Map.of(StateKey.CONTEXT_USER_INPUT.getKey(), "分析一下1期1号鼓风机有什么异常"),
                RunnableConfig.builder().threadId(threadId).build());
        asyncGenerator.forEachAsync(output -> {
            if (output instanceof StreamingOutput streamingOutput) {
                System.out.print(streamingOutput.chunk());
            }
        });

        //测试多轮会话 - 使用相同的threadId
        System.out.println("\n\n=== 第二轮对话开始 ===");
        System.out.println("用户输入：用100字概括一下");
        AsyncGenerator<NodeOutput> asyncGenerator2 = compiledGraph.stream(
                Map.of(StateKey.CONTEXT_USER_INPUT.getKey(), "用100字概括一下"),
                RunnableConfig.builder().threadId(threadId).build());
        asyncGenerator2.forEachAsync(output -> {
            if (output instanceof StreamingOutput streamingOutput) {
                System.out.print(streamingOutput.chunk());
            }
        });

        // 第三轮对话测试
        System.out.println("\n\n=== 第三轮对话开始 ===");
        System.out.println("用户输入：还有其他需要注意的地方吗？");
        AsyncGenerator<NodeOutput> asyncGenerator3 = compiledGraph.stream(
                Map.of(StateKey.CONTEXT_USER_INPUT.getKey(), "还有其他需要注意的地方吗？"),
                RunnableConfig.builder().threadId(threadId).build());
        asyncGenerator3.forEachAsync(output -> {
            if (output instanceof StreamingOutput streamingOutput) {
                System.out.print(streamingOutput.chunk());
            }
        });

        System.out.println("多轮对话测试完成");

        graph.getExecutorService().shutdown();

    }

    public static void testToTHumanFill() throws Exception {

        String totOriginInfo = """
                {
                    "tot_id": "TOT-MOTOR-TEMP-DIAGNOSIS-001",
                    "name": "电机定子温度异常诊断思维树",
                    "version": "1.0",
                    "nodes": [
                        {
                            "id": "n1",
                            "name": "数据特征分析",
                            "type": "START",
                            "prompt": "分析温度变化特征",
                            "tools": [
                                {
                                    "name": "定子温度数据特征分析专家",
                                    "params": {
                                        "报警的定子名称": "",
                                        "告警时间": "",
                                        "报警值": ""
                                    }
                                }
                            ],
                            "next": [
                                {
                                    "condition": "result == '趋势性升高'",
                                    "target": "n2"
                                },
                                {
                                    "condition": "result == '突变式升高'",
                                    "target": "n4"
                                }
                            ]
                        },
                        {
                            "id": "n2",
                            "name": "趋势性升高分析",
                            "type": "THOUGHT",
                            "prompt": "检测到温度持续缓慢上升趋势",
                            "tools": [
                                {
                                    "name": "定子温度趋势性升高诊断专家",
                                    "params": {
                                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1"
                                    }
                                }
                            ],
                            "next": [
                                {
                                    "condition": "result == '三相温度同步升高'",
                                    "target": "n3"
                                },
                                {
                                    "condition": "result == '单相温度异常突出'",
                                    "target": "n6"
                                }
                            ]
                        }
                }
                """;

        StateGraph agent1StateGraph = new StateGraph();

        agent1StateGraph.addNode("node1", node_async((state) -> {
            System.out.println("定子温度数据特征分析专家执行了");
            return Map.of(StateKey.TOOL_SUB_GRAPH_RESULT.getKey(), "定子过热,当前温度为100度,属于趋势性升高");
        }));

        agent1StateGraph.addEdge(StateGraph.START, "node1");
        agent1StateGraph.addEdge("node1", StateGraph.END);

        StateGraphToolCallback agent1 = new StateGraphToolCallback(agent1StateGraph,
                new Tool("定子温度数据特征分析专家", "定子温度数据特征分析专家",
                        ToolInputSchemaBuilder
                                .object()
                                .string("报警的定子名称").description("报警的定子名称").required().end()
                                .string("告警时间").description("告警时间").required().end()
                                .string("报警值").description("报警值").required().end()
                                .build()));
        String threadId = "thread1";

        ToTStateGraph graph = new ToTStateGraph("你是一个设备故障诊断流程的控制中枢，你需要遵循行业的故障诊断模式，结合思维树，对用户提取的问题进行逐步排查，你可以根据需求调用工具完成某个节点的故障检查，你需要经过多步的排查，得到最终的故障原因。\n",
                totOriginInfo, List.of(agent1));
        CompiledGraph compiledGraph = graph.compile();

        AsyncGenerator<NodeOutput> asyncGenerator = compiledGraph.stream(
                Map.of(StateKey.CONTEXT_USER_INPUT.getKey(), "分析一下1期1号鼓风机有什么异常"),
                RunnableConfig.builder().threadId(threadId).build());
        AtomicBoolean human = new AtomicBoolean(false);
        asyncGenerator.forEachAsync(output -> {
            if (output instanceof StreamingOutput streamingOutput) {
                System.out.print(streamingOutput.chunk());
            }
            if (StateGraph.END.equals(output.node())) {
                Optional<AgentContextState> agentContextStateOptional = output.state().value(StateKey.CONTEXT_AGENT.getKey(), AgentContextState.class);
                agentContextStateOptional.ifPresent(context -> {
                    System.out.println(context.getInstant().getFinalResult());
                    human.set(true);
                });
            }
        });
        if (human.get()) {
            AsyncGenerator<NodeOutput> asyncGenerator2 = compiledGraph.stream(
                    Map.of(StateKey.CONTEXT_USER_FILL_INFO.getKey(), FocusLayer.UserFillInfo.builder()
                            .infoList(List.of(
                                    KVStruct.of("报警的定子名称", "一期1号煤气鼓风机的鼓风机电机定子温度1"),
                                    KVStruct.of("告警时间", "2025-01-01 11:11:11"),
                                    KVStruct.of("报警值", "96度")))
                            .build()),
                    RunnableConfig.builder().threadId(threadId).build());
            asyncGenerator2.forEachAsync(output -> {
                if (output instanceof StreamingOutput streamingOutput) {
                    System.out.print(streamingOutput.chunk());
                }
            });
        }
    }

    public static void testStreamToolExecute() throws Exception {
        String totOriginInfo = """
                {
                    "tot_id": "TOT-MOTOR-TEMP-DIAGNOSIS-001",
                    "name": "电机定子温度异常诊断思维树",
                    "version": "1.0",
                    "nodes": [
                        {
                            "id": "n1",
                            "name": "数据特征分析",
                            "type": "START",
                            "prompt": "分析温度变化特征",
                            "tools": [
                                {
                                    "name": "定子温度数据特征分析专家",
                                    "params": {
                                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1",
                                        "告警时间": "2025-07-28 10:42:35",
                                        "报警值": "95℃"
                                    }
                                }
                            ],
                            "next": [
                                {
                                    "condition": "result == '趋势性升高'",
                                    "target": "n2"
                                }
                            ]
                        },
                        {
                            "id": "n2",
                            "name": "趋势性升高分析",
                            "type": "THOUGHT",
                            "prompt": "检测到温度持续缓慢上升趋势",
                            "tools": [
                                {
                                    "name": "定子温度趋势性升高诊断专家",
                                    "params": {
                                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1"
                                    }
                                }
                            ]
                        }
                    ]
                }
                """;
        
        CerebroService cerebroService = new CerebroService(WebClient.builder().build(), "https://cerebro-sit.genn.cn/");

        CerebroToolCallback agent1 = new CerebroToolCallback("68b92f14ba8b979a67c3aef1", "gennai-zxiNclqaSks2ywa42aI77MrlyqdJRbjkBK7g3ptoOzeWbIek0Du7SHUponvp",
                new Tool("定子温度数据特征分析专家", "定子温度数据特征分析专家",
                        ToolInputSchemaBuilder
                                .object()
                                .string("报警的定子名称").description("报警的定子名称").required().end()
                                .string("告警时间").description("告警时间").required().end()
                                .string("报警值").description("报警值").required().end()
                                .build()), cerebroService);


        StateGraph agent2StateGraph = new StateGraph();
        agent2StateGraph.addNode("node1", node_async((state) -> {
            System.out.println("定子温度趋势性升高诊断专家执行了");
            return Map.of(StateKey.TOOL_SUB_GRAPH_RESULT.getKey(), "定子温度异常,需要人工介入");
        }));
        agent2StateGraph.addEdge(StateGraph.START, "node1");
        agent2StateGraph.addEdge("node1", StateGraph.END);

        StateGraphToolCallback agent2 = new StateGraphToolCallback(agent2StateGraph,
                new Tool("定子温度趋势性升高诊断专家", "定子温度趋势性升高诊断专家",
                        ToolInputSchemaBuilder
                                .object()
                                .string("报警的定子名称").description("报警的定子名称").required().end()
                                .build()));

        ToTStateGraph graph = new ToTStateGraph("你是一个设备故障诊断流程的控制中枢，你需要遵循行业的故障诊断模式，结合思维树，对用户提取的问题进行逐步排查，你可以根据需求调用工具完成某个节点的故障检查，你需要经过多步的排查，得到最终的故障原因。\n",
                totOriginInfo, List.of(agent1, agent2));
        CompiledGraph compiledGraph = graph.compile();
        String threadId = "thread1";
        AsyncGenerator<NodeOutput> asyncGenerator = compiledGraph.stream(
                Map.of(StateKey.CONTEXT_USER_INPUT.getKey(), "分析一下1期1号鼓风机有什么异常"),
                RunnableConfig.builder().threadId(threadId).build());
        asyncGenerator.forEachAsync(output -> {
            if (output instanceof StreamingToolResultGenerator.StreamingStringOutput) {
                System.out.print(((StreamingToolResultGenerator.StreamingStringOutput) output).getChunk());
            }
            if (output instanceof StreamingOutput streamingOutput) {
                System.out.print(streamingOutput.chunk());
            }
        });
        
        graph.getExecutorService().shutdown();

    }

    public static void main(String[] args) throws Exception {
//        testToTHumanFill();
        testStreamToolExecute();
    }
}
