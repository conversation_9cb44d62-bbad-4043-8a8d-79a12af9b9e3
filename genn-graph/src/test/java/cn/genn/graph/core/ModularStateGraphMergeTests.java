package cn.genn.graph.core;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.action.AsyncEdgeAction;
import com.alibaba.cloud.ai.graph.action.AsyncNodeAction;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.alibaba.cloud.ai.graph.StateGraph.END;

/**
 * ModularStateGraph 内置节点和自定义节点合并测试
 * 
 * 测试各种合并场景：
 * 1. 内置节点 + 自定义节点（无冲突）
 * 2. 自定义节点覆盖内置节点
 * 3. 内置边 + 自定义边（无冲突）
 * 4. 自定义边覆盖内置边
 * 5. 条件边的覆盖
 * 6. 复杂合并场景
 * 
 * <AUTHOR>
 */
public class ModularStateGraphMergeTests {
    
    private static final AtomicInteger testCounter = new AtomicInteger(0);
    
    /**
     * 测试用例1：内置节点和自定义节点无冲突合并
     */
    public static void testNoConflictMerge() {
        System.out.println("=== 测试用例1：内置节点和自定义节点无冲突合并 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("NoConflictTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("builtin_step1", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行内置节点: builtin_step1");
                        return Map.of("builtin_step1", "completed");
                    }));
                    this.addNode("builtin_step2", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行内置节点: builtin_step2");
                        return Map.of("builtin_step2", "completed");
                    }));
                    
                    // 内置边
                    this.addEdge(START, "builtin_step1");
                    this.addEdge("builtin_step1", "builtin_step2");
                    this.addEdge("builtin_step2", END);
                }
            };
            
            // 添加自定义节点（不与内置节点冲突）
            graph.addNode("custom_step1", AsyncNodeAction.node_async(state -> {
                System.out.println("执行自定义节点: custom_step1");
                return Map.of("custom_step1", "completed");
            }));
            graph.addNode("custom_step2", AsyncNodeAction.node_async(state -> {
                System.out.println("执行自定义节点: custom_step2");
                return Map.of("custom_step2", "completed");
            }));
            
            // 添加自定义边（插入到内置流程中）
            graph.addEdge("builtin_step1", "custom_step1"); // 覆盖内置边 builtin_step1 -> builtin_step2
            graph.addEdge("custom_step1", "custom_step2");
            graph.addEdge("custom_step2", "builtin_step2");
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 无冲突合并测试通过");
        } catch (Exception e) {
            System.err.println("✗ 无冲突合并测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例2：自定义节点覆盖内置节点
     */
    public static void testNodeOverride() {
        System.out.println("\n=== 测试用例2：自定义节点覆盖内置节点 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("NodeOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("shared_node", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行内置版本的 shared_node");
                        return Map.of("version", "builtin", "data", "builtin_data");
                    }));
                    this.addNode("builtin_only", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行内置专有节点: builtin_only");
                        return Map.of("builtin_only", "completed");
                    }));
                    
                    // 内置边
                    this.addEdge(START, "shared_node");
                    this.addEdge("shared_node", "builtin_only");
                    this.addEdge("builtin_only", END);
                }
            };
            
            // 自定义节点覆盖内置节点
            graph.addNode("shared_node", AsyncNodeAction.node_async(state -> {
                System.out.println("执行自定义版本的 shared_node（应该覆盖内置版本）");
                return Map.of("version", "custom", "data", "custom_data", "override", true);
            }));
            
            // 添加自定义专有节点
            graph.addNode("custom_only", AsyncNodeAction.node_async(state -> {
                System.out.println("执行自定义专有节点: custom_only");
                String version = state.value("version", String.class).orElse("unknown");
                System.out.println("从shared_node获取的版本: " + version);
                return Map.of("custom_only", "completed", "received_version", version);
            }));
            
            // 修改边，插入自定义节点
            graph.addEdge("shared_node", "custom_only");
            graph.addEdge("custom_only", "builtin_only");
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 节点覆盖测试通过");
        } catch (Exception e) {
            System.err.println("✗ 节点覆盖测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例3：边覆盖测试
     */
    public static void testEdgeOverride() {
        System.out.println("\n=== 测试用例3：边覆盖测试 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("EdgeOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("node_a", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行节点A");
                        return Map.of("from", "node_a");
                    }));
                    this.addNode("node_b", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行节点B");
                        return Map.of("from", "node_b");
                    }));
                    this.addNode("node_c", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行节点C");
                        return Map.of("from", "node_c");
                    }));
                    
                    // 内置边：A -> B -> END
                    this.addEdge(START, "node_a");
                    this.addEdge("node_a", "node_b");
                    this.addEdge("node_b", END);
                }
            };
            
            // 自定义边覆盖内置边：A -> C -> END（跳过B）
            graph.addEdge("node_a", "node_c"); // 覆盖 node_a -> node_b
            graph.addEdge("node_c", END);      // 覆盖 node_b -> END
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 边覆盖测试通过（应该执行A->C->END，跳过B）");
        } catch (Exception e) {
            System.err.println("✗ 边覆盖测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例4：条件边覆盖测试
     */
    public static void testConditionalEdgeOverride() {
        System.out.println("\n=== 测试用例4：条件边覆盖测试 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("ConditionalEdgeOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("decision_node", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行决策节点");
                        return Map.of("decision", "builtin_path");
                    }));
                    this.addNode("builtin_path", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行内置路径");
                        return Map.of("path", "builtin");
                    }));
                    this.addNode("alternative_path", AsyncNodeAction.node_async(state -> {
                        System.out.println("执行替代路径");
                        return Map.of("path", "alternative");
                    }));
                    
                    // 内置条件边
                    AsyncEdgeAction builtinCondition = AsyncEdgeAction.edge_async(state -> {
                        System.out.println("执行内置条件判断");
                        return "builtin_path";
                    });
                    
                    this.addEdge(START, "decision_node");
                    this.addConditionalEdges("decision_node", builtinCondition, Map.of(
                        "builtin_path", "builtin_path",
                        "alternative_path", "alternative_path"
                    ));
                    this.addEdge("builtin_path", END);
                    this.addEdge("alternative_path", END);
                }
            };
            
            // 添加自定义节点
            graph.addNode("custom_path", AsyncNodeAction.node_async(state -> {
                System.out.println("执行自定义路径");
                return Map.of("path", "custom");
            }));
            
            // 自定义条件边覆盖内置条件边
            AsyncEdgeAction customCondition = AsyncEdgeAction.edge_async(state -> {
                System.out.println("执行自定义条件判断（应该覆盖内置条件）");
                return "custom_path";
            });
            
            graph.addConditionalEdges("decision_node", customCondition, Map.of(
                "custom_path", "custom_path",
                "builtin_path", "builtin_path"
            ));
            graph.addEdge("custom_path", END);
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 条件边覆盖测试通过");
        } catch (Exception e) {
            System.err.println("✗ 条件边覆盖测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例5：复杂合并场景
     */
    public static void testComplexMergeScenario() {
        System.out.println("\n=== 测试用例5：复杂合并场景 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("ComplexMergeTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置工作流：预处理 -> 处理 -> 后处理
                    this.addNode("preprocess", AsyncNodeAction.node_async(state -> {
                        System.out.println("内置预处理");
                        return Map.of("preprocessed", true, "stage", "preprocess");
                    }));
                    this.addNode("process", AsyncNodeAction.node_async(state -> {
                        System.out.println("内置处理");
                        return Map.of("processed", true, "stage", "process");
                    }));
                    this.addNode("postprocess", AsyncNodeAction.node_async(state -> {
                        System.out.println("内置后处理");
                        return Map.of("postprocessed", true, "stage", "postprocess");
                    }));
                    
                    // 内置边
                    this.addEdge(START, "preprocess");
                    this.addEdge("preprocess", "process");
                    this.addEdge("process", "postprocess");
                    this.addEdge("postprocess", END);
                }
            };
            
            // 1. 覆盖中间的处理节点
            graph.addNode("process", AsyncNodeAction.node_async(state -> {
                System.out.println("自定义处理（覆盖内置处理）");
                boolean preprocessed = state.value("preprocessed", Boolean.class).orElse(false);
                return Map.of("processed", true, "stage", "custom_process", "enhanced", true, "preprocessed_received", preprocessed);
            }));
            
            // 2. 添加新的验证节点
            graph.addNode("validate", AsyncNodeAction.node_async(state -> {
                System.out.println("自定义验证节点");
                boolean processed = state.value("processed", Boolean.class).orElse(false);
                boolean enhanced = state.value("enhanced", Boolean.class).orElse(false);
                return Map.of("validated", processed && enhanced, "stage", "validate");
            }));
            
            // 3. 修改边，插入验证节点
            graph.addEdge("process", "validate");      // process -> validate
            graph.addEdge("validate", "postprocess");  // validate -> postprocess
            
            // 4. 添加条件边到验证节点
            AsyncEdgeAction validationCondition = AsyncEdgeAction.edge_async(state -> {
                boolean validated = state.value("validated", Boolean.class).orElse(false);
                System.out.println("验证结果: " + validated);
                return validated ? "continue" : "error";
            });
            
            // 添加错误处理节点
            graph.addNode("error_handler", AsyncNodeAction.node_async(state -> {
                System.out.println("处理验证错误");
                return Map.of("error_handled", true, "stage", "error_handler");
            }));
            
            // 用条件边替换 validate -> postprocess
            graph.addConditionalEdges("validate", validationCondition, Map.of(
                "continue", "postprocess",
                "error", "error_handler"
            ));
            graph.addEdge("error_handler", END);
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 复杂合并场景测试通过");
        } catch (Exception e) {
            System.err.println("✗ 复杂合并场景测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用例6：多次覆盖测试
     */
    public static void testMultipleOverrides() {
        System.out.println("\n=== 测试用例6：多次覆盖测试 ===");
        try {
            ModularStateGraph graph = new ModularStateGraph("MultipleOverrideTest", List.of()) {
                @Override
                protected void buildWorkflow() throws GraphStateException {
                    // 内置节点
                    this.addNode("target_node", AsyncNodeAction.node_async(state -> {
                        System.out.println("内置版本的target_node");
                        return Map.of("version", "builtin", "counter", 1);
                    }));
                    
                    this.addEdge(START, "target_node");
                    this.addEdge("target_node", END);
                }
            };
            
            // 第一次覆盖
            graph.addNode("target_node", AsyncNodeAction.node_async(state -> {
                System.out.println("第一次自定义覆盖的target_node");
                return Map.of("version", "custom_v1", "counter", 2);
            }));
            
            // 第二次覆盖（应该覆盖第一次的）
            graph.addNode("target_node", AsyncNodeAction.node_async(state -> {
                System.out.println("第二次自定义覆盖的target_node（最终版本）");
                return Map.of("version", "custom_v2", "counter", 3, "final", true);
            }));
            
            CompiledGraph compiled = graph.compile();
            compiled.invoke(Map.of());
            
            System.out.println("✓ 多次覆盖测试通过（应该使用最后一次覆盖的版本）");
        } catch (Exception e) {
            System.err.println("✗ 多次覆盖测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 运行所有合并测试
     */
    public static void runAllMergeTests() {
        System.out.println("开始运行 ModularStateGraph 内置节点和自定义节点合并测试...\n");
        
        testNoConflictMerge();
        testNodeOverride();
        testEdgeOverride();
        testConditionalEdgeOverride();
        testComplexMergeScenario();
        testMultipleOverrides();
        
        System.out.println("\n=== ModularStateGraph 合并测试套件执行完成 ===");
    }
    
    public static void main(String[] args) {
        runAllMergeTests();
//        testComplexMergeScenario();

    }
}
