package cn.genn.graph.tools;

import cn.genn.ai.tools.model.Tool;
import cn.genn.graph.tot.ToTStructure;
import org.springframework.ai.tool.ToolCallback;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ToolUtils {
    
    public static Tool convertFromToTTool(ToTStructure.ToTTool toTTool) {
        return null;
    }

    public static List<ToolCallback> filterTool(List<ToTStructure.ToTTool> toTTools, Collection<ToolCallback> toolCallbacks) {
        //只保留在totTools中出现的工具
        return toolCallbacks.stream()
                .filter(tool -> toTTools.stream().anyMatch(toTTool -> toTTool.getName().equals(tool.getToolDefinition().name())))
                .toList();
    }

    public static boolean isBaseTool(String name, List<ToolCallback> baseTools) {
        return baseTools.stream().anyMatch(tool -> tool.getToolDefinition().name().equals(name));
    }
}
