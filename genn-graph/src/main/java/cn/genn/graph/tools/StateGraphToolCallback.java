package cn.genn.graph.tools;

import cn.genn.ai.tools.model.Tool;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.cloud.ai.graph.CompileConfig;
import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.KeyStrategyFactory;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 状态图工具回调
 * 每次都会编译状态图，适用于需要动态编译的场景。
 * 
 * <AUTHOR>
 */
@Slf4j
@Getter
@Setter
public class StateGraphToolCallback extends AbstractGraphToolCallback {

    /**
     * 状态图实例
     */
    @JsonIgnore
    private final StateGraph stateGraph;

    /**
     * 编译配置
     */
    @Setter
    @JsonIgnore
    private CompileConfig compileConfig = CompileConfig.builder().build();

    /**
     * 构造函数
     * @param stateGraph 状态图实例
     * @param tool 工具定义
     */
    public StateGraphToolCallback(StateGraph stateGraph, Tool tool) {
        super(tool);
        this.stateGraph = stateGraph;
        injectBuiltInKeyStrategies();
    }

    @Override
    protected CompiledGraph getCompiledGraph() throws Exception {
        return stateGraph.compile(compileConfig);
    }

    /**
     * 通过反射修改stateGraph的keyStrategyFactory，注入内置的策略
     */
    private void injectBuiltInKeyStrategies() {
        // 获取StateGraph类的keyStrategyFactory字段
        Class<?> stateGraphClass = stateGraph.getClass();
        Object currentFactory = ReflectUtil.getField(stateGraphClass, "keyStrategyFactory");
        // 创建增强的KeyStrategyFactory，包含内置策略
        KeyStrategyFactory enhancedFactory = createEnhancedKeyStrategyFactory(currentFactory);
        ReflectUtil.setFieldValue(stateGraph, "keyStrategyFactory", enhancedFactory);
        log.debug("Successfully injected built-in key strategies into StateGraph");
    }

    
}
