package cn.genn.graph.tools;

import cn.genn.ai.tools.StreamToolCallback;
import cn.genn.ai.tools.ToolUtils;
import cn.genn.ai.tools.model.Tool;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.graph.core.StateKey;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cloud.ai.graph.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.definition.DefaultToolDefinition;
import org.springframework.ai.tool.definition.ToolDefinition;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * 图工具回调抽象基类
 * 提取CompileGraphToolCallback和StateGraphToolCallback的公共逻辑
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Slf4j
public abstract class AbstractGraphToolCallback implements StreamToolCallback {

    /**
     * 结果键
     * 用于标识执行结果的存储位置
     */
    @Setter
    protected String resultKey = StateKey.TOOL_SUB_GRAPH_RESULT.getKey();
    
    /**
     * 上游agent传递的指令
     */
    @Setter
    protected String inputKey = StateKey.TOOL_UPSTREAM_INPUT_KEY.getKey();

    /**
     * 工具定义
     */
    protected Tool tool;

    /**
     * 构造函数
     * @param tool 工具定义
     */
    protected AbstractGraphToolCallback(Tool tool) {
        this.tool = tool;
    }

    @Override
    public boolean enabledStreaming() {
        return false;
    }

    @Override
    public ToolDefinition getToolDefinition() {
        return DefaultToolDefinition.builder()
                .name(this.tool.name())
                .description(this.tool.description())
                .inputSchema(JsonUtils.toJson(this.tool.inputSchema()))
                .build();
    }

    @Override
    public String call(String toolInput) {
        try {
            // 解析工具输入
            Map<String, Object> inputMap = ToolUtils.parseToolInput(toolInput, this.tool);
            
            // 将上游输入放入输入映射
            inputMap.put(inputKey, toolInput);

            // 获取编译后的图
            CompiledGraph compiledGraph = getCompiledGraph();

            // 创建运行配置
            RunnableConfig config = RunnableConfig.builder()
                    .threadId(generateThreadId())
                    .build();

            // 执行图并获取结果
            Optional<OverAllState> result = compiledGraph.invoke(inputMap, config);

            if (result.isPresent()) {
                // 处理执行结果
                return processResult(result.get());
            } else {
                log.warn("Graph execution returned empty result");
                return "执行完成，但未返回结果";
            }
        } catch (Exception e) {
            log.error("Graph execution failed", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public String call(String toolInput, ToolContext tooContext) {
        //todo 对于toolContext的处理
        return call(toolInput);
    }

    /**
     * 获取编译后的图，由子类实现具体获取逻辑
     * @return 编译后的图实例
     * @throws Exception 获取异常
     */
    protected abstract CompiledGraph getCompiledGraph() throws Exception;

    /**
     * 处理执行结果
     */
    protected String processResult(OverAllState overAllState) {
        Optional<Object> valueOptional = overAllState.value(resultKey);
        if (valueOptional.isPresent()) {
            return JsonUtils.toJson(valueOptional.get());
        }
        return CharSequenceUtil.EMPTY;
    }

    /**
     * 生成线程ID
     */
    protected String generateThreadId() {
        return "tool-" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 注入内置的KeyStrategy
     */
    protected KeyStrategyFactory createBuiltInKeyStrategyFactory() {
        Map<String, KeyStrategy> strategies = new HashMap<>(2);
        strategies.put(StateKey.TOOL_SUB_GRAPH_RESULT.getKey(), StateKey.TOOL_SUB_GRAPH_RESULT.getStrategy());
        strategies.put(StateKey.TOOL_UPSTREAM_INPUT_KEY.getKey(), StateKey.TOOL_UPSTREAM_INPUT_KEY.getStrategy());
        return () -> strategies;
    }

    /**
     * 创建增强的KeyStrategyFactory，合并原有策略和内置策略
     */
    protected KeyStrategyFactory createEnhancedKeyStrategyFactory(Object originalFactory) {
        return () -> {
            Map<String, KeyStrategy> strategies = new HashMap<>();

            // 如果原有factory存在，先添加原有策略
            if (originalFactory instanceof KeyStrategyFactory original) {
                Map<String, KeyStrategy> originalStrategies = original.apply();
                if (originalStrategies != null) {
                    strategies.putAll(originalStrategies);
                }
            }

            // 使用KeyStrategyFactoryBuilder创建内置策略并合并
            KeyStrategyFactory builtInFactory = createBuiltInKeyStrategyFactory();
            Map<String, KeyStrategy> builtInStrategies = builtInFactory.apply();

            if (builtInStrategies != null) {
                // 内置策略优先级更高，会覆盖同名的原有策略
                strategies.putAll(builtInStrategies);
                log.debug("Injected {} built-in key strategies: {}",
                        builtInStrategies.size(), builtInStrategies.keySet());
            }

            return strategies;
        };
    }
}
