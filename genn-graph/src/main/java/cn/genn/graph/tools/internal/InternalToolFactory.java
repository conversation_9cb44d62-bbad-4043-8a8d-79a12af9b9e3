package cn.genn.graph.tools.internal;

import org.springframework.ai.tool.ToolCallback;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 内置工具工厂类，使用单例模式管理内置工具
 * <AUTHOR>
 */
public class InternalToolFactory {
    
    private static volatile InternalToolFactory instance;
    private final Map<String, ToolCallback> toolMap;
    
    // 私有构造函数，防止外部实例化
    private InternalToolFactory() {
        toolMap = new HashMap<>();
        initializeTools();
    }
    
    /**
     * 获取工厂实例（双重检查锁定）
     */
    public static InternalToolFactory getInstance() {
        if (instance == null) {
            synchronized (InternalToolFactory.class) {
                if (instance == null) {
                    instance = new InternalToolFactory();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化内置工具
     */
    private void initializeTools() {
        // 初始化人类交互工具
        HumanTool humanTool = new HumanTool();
        toolMap.put(HumanTool.NAME, humanTool);
        
        // 初始化终止工具
        TerminateTool terminateTool = new TerminateTool();
        toolMap.put(TerminateTool.NAME, terminateTool);
    }
    
    /**
     * 根据工具名称获取工具实例
     * @param toolName 工具名称
     * @return 工具实例，如果不存在返回null
     */
    public ToolCallback getTool(String toolName) {
        return toolMap.get(toolName);
    }
    
    /**
     * 获取人类交互工具
     */
    public HumanTool getHumanTool() {
        return (HumanTool) toolMap.get(HumanTool.NAME);
    }
    
    /**
     * 获取终止工具
     */
    public TerminateTool getTerminateTool() {
        return (TerminateTool) toolMap.get(TerminateTool.NAME);
    }
    
    /**
     * 获取所有内置工具
     */
    public Map<String, ToolCallback> getAllTools() {
        return new HashMap<>(toolMap);
    }
    
    /**
     * 获取所有内置工具列表
     */
    public List<ToolCallback> getAllToolList() {
        return new ArrayList<>(toolMap.values());
    }
    
    /**
     * 检查是否存在指定名称的工具
     */
    public boolean hasTool(String toolName) {
        return toolMap.containsKey(toolName);
    }
    
    /**
     * 获取内置工具数量
     */
    public int getToolCount() {
        return toolMap.size();
    }
}
