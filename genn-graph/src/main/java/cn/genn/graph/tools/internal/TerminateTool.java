package cn.genn.graph.tools.internal;

import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;

/**
 * 不需要定义
 * <AUTHOR>
 */
public class TerminateTool implements ToolCallback {
    
    public static final String NAME = "terminate_tool";

    @Override
    public ToolDefinition getToolDefinition() {
        return ToolDefinition.builder()
                .name(NAME)
                .description("当不需要调用任何工具时使用")
                .inputSchema("{}")
                .build();
    }

    @Override
    public String call(String toolInput) {
        return "";
    }

    @Override
    public String call(String toolInput, ToolContext tooContext) {
        return call(toolInput);
    }
}
