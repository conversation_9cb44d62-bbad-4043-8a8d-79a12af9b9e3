package cn.genn.graph.tools.internal;

import cn.genn.graph.core.ChatClientFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;

import java.util.HashMap;
import java.util.Map;

/**
 * 人类交互工具, 用于与人类进行交互，例如询问问题、获取信息等。
 * <AUTHOR>
 */
public class HumanTool implements ToolCallback {
    
    public static final String NAME = "human_tool";
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public ToolDefinition getToolDefinition() {
        String inputSchema = """
            {
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "向人类展示的提示信息或问题描述"
                    },
                    "fields": {
                        "type": "object",
                        "description": "需要人类填写的表单字段，key为字段名，value为字段描述",
                        "additionalProperties": {
                            "type": "string"
                        }
                    },
                    "required_fields": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "description": "必填字段列表"
                    }
                },
                "required": ["message", "fields"]
            }
            """;
        
        return ToolDefinition.builder()
                .name(NAME)
                .description("当所需信息不足以处理问题时,调用此工具,与人类进行交互，要求人为补充信息。")
                .inputSchema(inputSchema)
                .build();
    }

    @Override
    public String call(String toolInput) {
        try {
            JsonNode inputNode = objectMapper.readTree(toolInput);
            
            String message = inputNode.get("message").asText();
            JsonNode fieldsNode = inputNode.get("fields");
            JsonNode requiredFieldsNode = inputNode.get("required_fields");
            
            // 构建表单字段信息
            Map<String, FormField> formFields = new HashMap<>();
            fieldsNode.fields().forEachRemaining(field -> {
                String fieldName = field.getKey();
                String fieldDescription = field.getValue().asText();
                boolean isRequired = requiredFieldsNode != null && 
                    requiredFieldsNode.toString().contains("\"" + fieldName + "\"");
                
                FormField formField = new FormField();
                formField.setName(fieldName);
                formField.setDescription(fieldDescription);
                formField.setRequired(isRequired);
                formField.setValue(""); // 空值，等待填写
                
                formFields.put(fieldName, formField);
            });
            
            // 封装返回结果 - 返回需要填写的表单结构
            HumanToolResult result = new HumanToolResult();
            result.setSuccess(true);
            result.setMessage(message);
            result.setFormFields(formFields);
            result.setPlainText(buildFormPrompt(message, formFields));
            result.setNeedHumanInput(true);
            
            return objectMapper.writeValueAsString(result);
            
        } catch (JsonProcessingException e) {
            HumanToolResult errorResult = new HumanToolResult();
            errorResult.setSuccess(false);
            errorResult.setMessage("解析输入参数失败: " + e.getMessage());
            errorResult.setFormFields(new HashMap<>());
            errorResult.setPlainText("操作失败: " + e.getMessage());
            errorResult.setNeedHumanInput(false);
            
            try {
                return objectMapper.writeValueAsString(errorResult);
            } catch (JsonProcessingException ex) {
                return "{\"success\": false, \"message\": \"系统错误\", \"plainText\": \"系统错误\", \"needHumanInput\": false}";
            }
        }
    }

    @Override
    public String call(String toolInput, ToolContext tooContext) {
        return call(toolInput);
    }

    /**
     * 构建表单提示文本
     */
    private String buildFormPrompt(String message, Map<String, FormField> formFields) {
        StringBuilder prompt = new StringBuilder();
        prompt.append(message).append("\n\n");
        prompt.append("需要填写的字段：\n");
        
        formFields.values().forEach(field -> {
            prompt.append("- ").append(field.getName())
                  .append(" (").append(field.getDescription()).append(")")
                  .append(field.isRequired() ? " [必填]" : " [可选]")
                  .append("\n");
        });
        
        return prompt.toString().trim();
    }
    
    /**
     * 表单字段信息
     */
    @Data
    public static class FormField {
        private String name;
        private String description;
        private boolean required;
        private String value;
    }
    
    /**
     * 人类工具返回结果封装类
     */
    @Data
    public static class HumanToolResult {
        private boolean success;
        private String message;
        private Map<String, FormField> formFields;
        private String plainText;
        private boolean needHumanInput;
        
    }

    public static void main(String[] args) {
        HumanTool humanTool = new HumanTool();
        ChatClient client = ChatClientFactory.createChatModel();
        ChatResponse chatResponse = client.prompt("太原今天天气如何")
                .toolCallbacks(humanTool)
                .call()
                .chatResponse();
        if (chatResponse.hasToolCalls()) {
            chatResponse.getResult().getOutput().getToolCalls().forEach(toolCall -> {
                String arguments = toolCall.arguments();
                String result = humanTool.call(arguments);
                System.out.println("toolCall: " + toolCall.name() + ", result: " + result);
            });
        }
    }
}
