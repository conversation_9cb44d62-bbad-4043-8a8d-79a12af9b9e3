package cn.genn.graph.tools;

import cn.genn.ai.tools.model.Tool;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.KeyStrategyFactory;
import com.alibaba.cloud.ai.graph.StateGraph;

/**
 * 编译图工具回调
 * 
 * <AUTHOR>
 */
public class CompileGraphToolCallback extends AbstractGraphToolCallback {

    /**
     * 编译后的图实例
     */
    private final CompiledGraph compiledGraph;

    public CompileGraphToolCallback(CompiledGraph compiledGraph, Tool tool) {
        super(tool);
        this.compiledGraph = compiledGraph;
        injectBuiltInKeyStrategies();
    }


    @Override
    protected CompiledGraph getCompiledGraph() {
        return compiledGraph;
    }

    private void injectBuiltInKeyStrategies() {
        StateGraph stateGraph = this.compiledGraph.stateGraph;
        Class<?> stateGraphClass = stateGraph.getClass();
        Object currentFactory = ReflectUtil.getField(stateGraphClass, "keyStrategyFactory");
        KeyStrategyFactory enhancedFactory = createEnhancedKeyStrategyFactory(currentFactory);
        ReflectUtil.setFieldValue(stateGraph, "keyStrategyFactory", enhancedFactory);
        ReflectUtil.setFieldValue(this.compiledGraph, "stateGraph", stateGraph);
    }
}
