package cn.genn.graph.tools;

import com.alibaba.cloud.ai.graph.CompileConfig;
import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * StateGraph工具管理器，用于缓存编译后的图以避免重复编译。
 * <p>
 * 主要功能：
 * 1. 缓存已编译的CompiledGraph实例
 * 2. 基于StateGraph的结构特征生成缓存键
 * 3. 线程安全的缓存操作
 * <p>
 *
 * <AUTHOR>
 */
@Slf4j
public class StateGraphToolManager {

    /**
     * 默认缓存大小
     */
    private static final int DEFAULT_CACHE_SIZE = 100;

    /**
     * 缓存键
     */
    private record CacheKey(String graphHash, String configHash) {

        @Override
        public String toString() {
            return "CacheKey{" +
                    "graphHash='" + graphHash + '\'' +
                    ", configHash='" + configHash + '\'' +
                    '}';
        }
    }

    /**
     * 缓存存储
     */
    private final Map<CacheKey, CompiledGraph> cache = new ConcurrentHashMap<>();

    /**
     * 缓存配置
     */
    private final int maxCacheSize;

    /**
     * 默认构造函数
     */
    public StateGraphToolManager() {
        this(DEFAULT_CACHE_SIZE);
    }

    /**
     * 构造函数
     *
     * @param maxCacheSize 最大缓存大小
     */
    public StateGraphToolManager(int maxCacheSize) {
        this.maxCacheSize = maxCacheSize;
        log.info("StateGraphToolManager initialized with maxCacheSize={}", maxCacheSize);
    }

    /**
     * 获取或编译StateGraph
     *
     * @param stateGraph StateGraph实例
     * @return 编译后的CompiledGraph
     * @throws GraphStateException 编译异常
     */
    public CompiledGraph getOrCompile(StateGraph stateGraph) throws GraphStateException {
        return getOrCompile(stateGraph, CompileConfig.builder().build());
    }

    /**
     * 获取或编译StateGraph（带配置）
     *
     * @param stateGraph    StateGraph实例
     * @param compileConfig 编译配置
     * @return 编译后的CompiledGraph
     * @throws GraphStateException 编译异常
     */
    public CompiledGraph getOrCompile(StateGraph stateGraph, CompileConfig compileConfig) throws GraphStateException {
        if (stateGraph == null) {
            throw new IllegalArgumentException("StateGraph cannot be null");
        }
        if (compileConfig == null) {
            compileConfig = CompileConfig.builder().build();
        }

        // 生成缓存键
        CacheKey cacheKey = generateCacheKey(stateGraph, compileConfig);

        // 尝试从缓存获取
        CompiledGraph cachedGraph = cache.get(cacheKey);
        if (cachedGraph != null) {
            log.debug("Cache hit for StateGraph: {}", cacheKey);
            return cachedGraph;
        }

        // 缓存未命中，需要编译
        log.debug("Cache miss for StateGraph: {}, compiling...", cacheKey);
        CompiledGraph compiledGraph = stateGraph.compile(compileConfig);

        // 存入缓存
        putInCache(cacheKey, compiledGraph);

        return compiledGraph;
    }

    /**
     * 清空所有缓存
     */
    public void clearCache() {
        int size = cache.size();
        cache.clear();
        log.info("Cleared all cache entries, removed {} entries", size);
    }

    /**
     * 获取缓存大小
     */
    public int getCacheSize() {
        return cache.size();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 生成缓存键
     */
    private CacheKey generateCacheKey(StateGraph stateGraph, CompileConfig compileConfig) {
        String graphHash = generateGraphHash(stateGraph);
        String configHash = generateConfigHash(compileConfig);
        return new CacheKey(graphHash, configHash);
    }

    /**
     * 生成StateGraph的哈希值
     */
    private String generateGraphHash(StateGraph stateGraph) {
        // 基于StateGraph的关键属性生成哈希
        StringBuilder hashBuilder = new StringBuilder();

        // 添加图名称
        if (stateGraph.getName() != null) {
            hashBuilder.append("name:").append(stateGraph.getName()).append(";");
        }

        // 添加类名（用于区分不同类型的StateGraph）
        hashBuilder.append("class:").append(stateGraph.getClass().getName()).append(";");

        // 生成最终哈希
        return String.valueOf(hashBuilder.toString().hashCode());
    }

    /**
     * 生成CompileConfig的哈希值
     */
    private String generateConfigHash(CompileConfig compileConfig) {
        if (compileConfig == null) {
            return "default";
        }

        // 基于配置的关键属性生成哈希

        // 添加配置的字符串表示（如果有的话）

        return String.valueOf(("config:" + compileConfig).hashCode());
    }

    /**
     * 将编译结果存入缓存
     */
    private void putInCache(CacheKey cacheKey, CompiledGraph compiledGraph) {
        // 检查缓存大小，如果超过限制则清理
        if (cache.size() >= maxCacheSize) {
            evictOldest();
        }

        // 存入缓存
        cache.put(cacheKey, compiledGraph);
        log.debug("Cached compiled graph with key: {}", cacheKey);
    }

    /**
     * 简单的淘汰策略：移除第一个找到的项
     */
    private void evictOldest() {
        if (cache.isEmpty()) {
            return;
        }

        // 移除第一个找到的项（简单策略）
        CacheKey firstKey = cache.keySet().iterator().next();
        cache.remove(firstKey);
        log.debug("Evicted cache entry: {}", firstKey);
    }

}
