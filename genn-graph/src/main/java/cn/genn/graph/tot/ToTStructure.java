package cn.genn.graph.tot;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * Tree of Thoughts (ToT) 思维树结构定义
 * 
 * 表示一个完整的思维树结构，包含所有节点和它们之间的关系。
 * 这个类对应于ToT相关.md文档中定义的JSON结构。
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToTStructure {
    
    /**
     * ToT的唯一标识符
     */
    @JsonProperty("tot_id")
    private String totId;
    
    /**
     * ToT的名称描述
     */
    private String name;
    
    /**
     * ToT的版本号
     */
    private String version;
    
    /**
     * ToT中的所有节点
     */
    private List<ToTNode> nodes;
    
    /**
     * ToT节点定义
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ToTNode {
        
        /**
         * 节点唯一标识符
         */
        private String id;
        
        /**
         * 节点名称
         */
        private String name;
        
        /**
         * 节点类型
         */
        private NodeType type;
        
        /**
         * 节点提示词
         */
        private String prompt;
        
        /**
         * 节点关联的工具列表
         */
        private List<ToTTool> tools;
        
        /**
         * 下一步流转规则
         */
        private List<NextCondition> next;
        
        /**
         * 节点的额外元数据
         */
        private Map<String, Object> metadata;
    }
    
    /**
     * 节点类型枚举
     */
    public enum NodeType {
        /**
         * 起始节点
         */
        START,
        
        /**
         * 思考节点
         */
        THOUGHT,
        
        /**
         * Agent执行节点
         */
        AGENT_EXECUTION,
        
        /**
         * 决策节点
         */
        DECISION,
        
        /**
         * 结束节点
         */
        END
    }
    
    /**
     * ToT工具定义
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ToTTool {
        
        /**
         * 工具名称
         */
        private String name;
        
        /**
         * 工具参数
         */
        private Map<String, Object> params;
        
        /**
         * 工具描述
         */
        private String description;
        
        /**
         * 工具类型
         */
        private String type;
        
        /**
         * 是否必须执行
         */
        private boolean required;
    }
    
    /**
     * 下一步条件定义
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NextCondition {
        
        /**
         * 条件表达式
         */
        private String condition;
        
        /**
         * 目标节点ID
         */
        private String target;
        
        /**
         * 条件权重（用于多条件排序）
         */
        private int weight;
        
        /**
         * 条件描述
         */
        private String description;
    }
    
    /**
     * 获取起始节点
     */
    public ToTNode findStartNode() {
        return nodes.stream()
            .filter(node -> node.getType() == NodeType.START)
            .findFirst()
            .orElseThrow(() -> new IllegalStateException("No START node found in ToT structure"));
    }
    
    /**
     * 根据ID查找节点
     */
    public ToTNode findNodeById(String nodeId) {
        return nodes.stream()
            .filter(node -> node.getId().equals(nodeId))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Node not found: " + nodeId));
    }
    
    /**
     * 获取指定节点的所有可能下一步节点
     */
    public List<ToTNode> findNextNodes(String currentNodeId) {
        ToTNode currentNode = findNodeById(currentNodeId);
        if (currentNode.getNext() == null || currentNode.getNext().isEmpty()) {
            return List.of();
        }
        
        return currentNode.getNext().stream()
            .map(condition -> findNodeById(condition.getTarget()))
            .toList();
    }
    
    /**
     * 根据工具名称获取节点
     * 
     * @param toolName 工具名称
     * @return 节点
     */
    public ToTNode findNodeByToolName(String toolName) {
        return nodes.stream()
            .filter(node -> node.getTools() != null && node.getTools().stream()
                .anyMatch(tool -> tool.getName().equals(toolName)))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("No node found with tool: " + toolName));
    }
    
    /**
     * 初始化工具列表
     * 从START节点开始提取工具列表（包括当前节点和下一级节点的工具）
     * 
     * @return START节点及其下一级节点的所有工具列表
     */
    public List<ToTTool> initializeToolList() {
        ToTNode startNode = findStartNode();
        return extractToolList(startNode.getId());
    }
    
    /**
     * 提取工具列表
     * 包括当前节点的工具和下一级节点的工具列表
     * 
     * @param currentNodeId 当前节点ID
     * @return 当前节点及其下一级节点的所有工具列表（去重）
     */
    public List<ToTTool> extractToolList(String currentNodeId) {
        ToTNode currentNode = findNodeById(currentNodeId);
        Set<ToTTool> allTools = new HashSet<>();
        
        // 添加当前节点的工具
        if (currentNode.getTools() != null) {
            allTools.addAll(currentNode.getTools());
        }
        
        // 添加下一级节点的工具
        List<ToTNode> nextNodes = findNextNodes(currentNodeId);
        for (ToTNode nextNode : nextNodes) {
            if (nextNode.getTools() != null) {
                allTools.addAll(nextNode.getTools());
            }
        }
        
        return new ArrayList<>(allTools);
    }

    /**
     * 验证ToT结构的完整性
     */
    public void validate() {
        if (totId == null || totId.trim().isEmpty()) {
            throw new IllegalArgumentException("ToT ID cannot be null or empty");
        }
        
        if (nodes == null || nodes.isEmpty()) {
            throw new IllegalArgumentException("ToT must contain at least one node");
        }
        
        // 验证是否有起始节点
        long startNodeCount = nodes.stream()
            .filter(node -> node.getType() == NodeType.START)
            .count();
        
        if (startNodeCount == 0) {
            throw new IllegalArgumentException("ToT must have exactly one START node");
        }
        
        if (startNodeCount > 1) {
            throw new IllegalArgumentException("ToT cannot have multiple START nodes");
        }
        
        // 验证节点ID唯一性
        long uniqueIdCount = nodes.stream()
            .map(ToTNode::getId)
            .distinct()
            .count();
        
        if (uniqueIdCount != nodes.size()) {
            throw new IllegalArgumentException("All node IDs must be unique");
        }
        
        // 验证引用的节点ID都存在
        for (ToTNode node : nodes) {
            if (node.getNext() != null) {
                for (NextCondition condition : node.getNext()) {
                    if (nodes.stream().noneMatch(n -> n.getId().equals(condition.getTarget()))) {
                        throw new IllegalArgumentException(
                            "Node " + node.getId() + " references non-existent target: " + condition.getTarget());
                    }
                }
            }
        }
    }
}
