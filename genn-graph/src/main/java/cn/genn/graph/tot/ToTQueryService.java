package cn.genn.graph.tot;

import java.util.Map;

/**
 * ToT查询服务接口
 * 
 * 定义了从外部系统查询ToT结构的接口规范。
 * 业务系统需要实现此接口来提供具体的ToT查询逻辑。
 * 
 * 这是框架与业务系统集成的关键扩展点之一。
 * 
 * <AUTHOR> Framework Team
 */
public interface ToTQueryService {
    
    /**
     * 根据用户查询和业务上下文查询ToT结构
     * 
     * @param userQuery 用户查询内容
     * @param businessContext 业务上下文参数
     * @return ToT结构的JSON字符串，如果未找到则返回null
     * @throws ToTQueryException 查询过程中发生的异常
     */
    String queryToT(String userQuery, Map<String, Object> businessContext) throws ToTQueryException;
    
    /**
     * 根据ToT ID直接查询ToT结构
     * 
     * @param totId ToT的唯一标识符
     * @return ToT结构的JSON字符串，如果未找到则返回null
     * @throws ToTQueryException 查询过程中发生的异常
     */
    String queryToTById(String totId) throws ToTQueryException;
    
    /**
     * 检查ToT查询服务是否可用
     * 
     * @return true表示服务可用，false表示不可用
     */
    boolean isAvailable();
    
    /**
     * ToT查询异常
     */
    class ToTQueryException extends Exception {
        public ToTQueryException(String message) {
            super(message);
        }
        
        public ToTQueryException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
