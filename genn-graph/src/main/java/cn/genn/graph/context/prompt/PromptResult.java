package cn.genn.graph.context.prompt;

import cn.genn.graph.context.compression.CompressionLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * Prompt构建结果
 * 包含构建的Prompt内容和相关统计信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromptResult {
    
    /**
     * 系统提示词
     */
    private String systemPrompt;
    
    /**
     * 用户消息
     */
    private String userMessage;
    
    /**
     * 完整的Prompt内容（系统+用户）
     */
    private String fullPrompt;
    
    /**
     * 总Token数量
     */
    private long totalTokenCount;
    
    /**
     * 焦点层Token数量
     */
    private long focusTokenCount;
    
    /**
     * 全景层Token数量
     */
    private long panoramicTokenCount;
    
    /**
     * 战略层Token数量
     */
    private long strategicTokenCount;
    
    /**
     * 各层的压缩级别
     */
    @Builder.Default
    private Map<String, CompressionLevel> compressionLevels = new HashMap<>();
    
    /**
     * 各层的最终内容
     */
    @Builder.Default
    private Map<String, String> layerContents = new HashMap<>();
    
    /**
     * 是否成功构建
     */
    @Builder.Default
    private boolean success = true;
    
    /**
     * 错误信息（如果构建失败）
     */
    private String errorMessage;
    
    /**
     * 构建时间戳
     */
    @Builder.Default
    private Instant timestamp = Instant.now();
    
    /**
     * 构建耗时（毫秒）
     */
    private long buildTimeMillis;
    
    /**
     * 使用的模板名称
     */
    private String templateName;
    
    /**
     * 额外的元数据
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * 获取焦点层内容
     */
    public String getFocusContent() {
        return layerContents.get("focus");
    }
    
    /**
     * 获取全景层内容
     */
    public String getPanoramicContent() {
        return layerContents.get("panoramic");
    }
    
    /**
     * 获取战略层内容
     */
    public String getStrategicContent() {
        return layerContents.get("strategic");
    }
    
    /**
     * 设置层内容
     */
    public void setLayerContent(String layerName, String content) {
        layerContents.put(layerName, content);
    }
    
    /**
     * 设置压缩级别
     */
    public void setCompressionLevel(String layerName, CompressionLevel level) {
        compressionLevels.put(layerName, level);
    }
    
    /**
     * 获取压缩级别
     */
    public CompressionLevel getCompressionLevel(String layerName) {
        return compressionLevels.getOrDefault(layerName, CompressionLevel.NONE);
    }
    
    /**
     * 计算Token使用率
     */
    public double getTokenUsageRatio(long maxTokens) {
        if (maxTokens <= 0) {
            return 1.0;
        }
        return (double) totalTokenCount / maxTokens;
    }
    
    /**
     * 是否有压缩
     */
    public boolean hasCompression() {
        return compressionLevels.values().stream()
                .anyMatch(level -> level != CompressionLevel.NONE);
    }
    
    /**
     * 获取最严重的压缩级别
     */
    public CompressionLevel getMaxCompressionLevel() {
        return compressionLevels.values().stream()
                .max((a, b) -> Integer.compare(a.getOrdinal(), b.getOrdinal()))
                .orElse(CompressionLevel.NONE);
    }
    
    /**
     * 添加元数据
     */
    public void addMetadata(String key, Object value) {
        metadata.put(key, value);
    }
    
    /**
     * 构建完整的Prompt
     */
    public void buildFullPrompt() {
        if (systemPrompt != null && userMessage != null) {
            this.fullPrompt = systemPrompt + "\n\n" + userMessage;
        } else if (systemPrompt != null) {
            this.fullPrompt = systemPrompt;
        } else if (userMessage != null) {
            this.fullPrompt = userMessage;
        } else {
            this.fullPrompt = "";
        }
    }
    
    /**
     * 创建成功的结果
     */
    public static PromptResult success(String systemPrompt, String userMessage, long totalTokens) {
        PromptResult result = PromptResult.builder()
                .systemPrompt(systemPrompt)
                .userMessage(userMessage)
                .totalTokenCount(totalTokens)
                .success(true)
                .timestamp(Instant.now())
                .build();
        result.buildFullPrompt();
        return result;
    }
    
    /**
     * 创建失败的结果
     */
    public static PromptResult failure(String errorMessage) {
        return PromptResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .timestamp(Instant.now())
                .build();
    }
    
    /**
     * 获取摘要信息
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("Prompt Result: ");
        
        if (success) {
            sb.append("SUCCESS");
            sb.append(" | Tokens: ").append(totalTokenCount);
            sb.append(" (Focus: ").append(focusTokenCount);
            sb.append(", Panoramic: ").append(panoramicTokenCount);
            sb.append(", Strategic: ").append(strategicTokenCount).append(")");
            
            if (hasCompression()) {
                sb.append(" | Compression: ");
                compressionLevels.forEach((layer, level) -> {
                    if (level != CompressionLevel.NONE) {
                        sb.append(layer).append("=").append(level.name()).append(" ");
                    }
                });
            }
            
            if (templateName != null) {
                sb.append(" | Template: ").append(templateName);
            }
            
            if (buildTimeMillis > 0) {
                sb.append(" | Time: ").append(buildTimeMillis).append("ms");
            }
        } else {
            sb.append("FAILED");
            if (errorMessage != null) {
                sb.append(" | Error: ").append(errorMessage);
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 获取详细报告
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== Prompt Build Report ===\n");
        report.append("Status: ").append(success ? "SUCCESS" : "FAILED").append("\n");
        report.append("Timestamp: ").append(timestamp).append("\n");
        
        if (success) {
            report.append("\n--- Token Distribution ---\n");
            report.append("Total: ").append(totalTokenCount).append(" tokens\n");
            report.append("  Focus Layer: ").append(focusTokenCount)
                  .append(" (").append(String.format("%.1f%%", (double)focusTokenCount/totalTokenCount*100)).append(")\n");
            report.append("  Panoramic Layer: ").append(panoramicTokenCount)
                  .append(" (").append(String.format("%.1f%%", (double)panoramicTokenCount/totalTokenCount*100)).append(")\n");
            report.append("  Strategic Layer: ").append(strategicTokenCount)
                  .append(" (").append(String.format("%.1f%%", (double)strategicTokenCount/totalTokenCount*100)).append(")\n");
            
            if (hasCompression()) {
                report.append("\n--- Compression Applied ---\n");
                compressionLevels.forEach((layer, level) -> {
                    report.append("  ").append(layer).append(": ").append(level.getDescription()).append("\n");
                });
            }
            
            if (templateName != null) {
                report.append("\n--- Template ---\n");
                report.append("Name: ").append(templateName).append("\n");
            }
            
            if (buildTimeMillis > 0) {
                report.append("\n--- Performance ---\n");
                report.append("Build Time: ").append(buildTimeMillis).append(" ms\n");
            }
            
            if (!metadata.isEmpty()) {
                report.append("\n--- Metadata ---\n");
                metadata.forEach((key, value) -> 
                    report.append("  ").append(key).append(": ").append(value).append("\n"));
            }
            
            report.append("\n--- Content Preview ---\n");
            report.append("System Prompt Length: ").append(systemPrompt != null ? systemPrompt.length() : 0).append(" chars\n");
            report.append("User Message Length: ").append(userMessage != null ? userMessage.length() : 0).append(" chars\n");
            
        } else {
            report.append("\nError: ").append(errorMessage != null ? errorMessage : "Unknown error").append("\n");
        }
        
        report.append("===========================\n");
        return report.toString();
    }
}