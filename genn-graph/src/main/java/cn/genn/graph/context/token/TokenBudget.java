package cn.genn.graph.context.token;

import cn.genn.graph.context.compression.CompressionLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Token预算管理类
 * 管理和分配各层的Token预算
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenBudget {
    
    /**
     * 模型最大Token限制
     */
    private long maxTokens = 128000;
    
    /**
     * 为响应预留的Token数量
     */
    @Builder.Default
    private long reservedTokens = 4000;
    
    /**
     * 可用Token总量（maxTokens - reservedTokens）
     */
    private long availableTokens;
    
    /**
     * 焦点层分配的Token数
     */
    private long focusTokens;
    
    /**
     * 全景层分配的Token数
     */
    private long panoramicTokens;
    
    /**
     * 战略层分配的Token数
     */
    private long strategicTokens;
    
    /**
     * 当前已使用的Token数
     */
    private long usedTokens;
    
    /**
     * Token分配比例配置
     */
    @Builder.Default
    private TokenAllocationRatio allocationRatio = TokenAllocationRatio.DEFAULT;
    
    /**
     * 压缩级别建议
     */
    private Map<String, CompressionLevel> compressionSuggestions;
    
    /**
     * Token分配比例
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TokenAllocationRatio {
        /**
         * 焦点层比例（不可压缩，必须保证）
         */
        @Builder.Default
        private double focusRatio = 0.4;  // 40%
        
        /**
         * 全景层比例
         */
        @Builder.Default
        private double panoramicRatio = 0.35;  // 35%
        
        /**
         * 战略层比例
         */
        @Builder.Default
        private double strategicRatio = 0.25;  // 25%
        
        /**
         * 默认分配比例
         */
        public static final TokenAllocationRatio DEFAULT = TokenAllocationRatio.builder().build();
        
        /**
         * 紧凑模式分配比例（更多给焦点层）
         */
        public static final TokenAllocationRatio COMPACT = TokenAllocationRatio.builder()
                .focusRatio(0.5)
                .panoramicRatio(0.3)
                .strategicRatio(0.2)
                .build();
        
        /**
         * 平衡模式分配比例
         */
        public static final TokenAllocationRatio BALANCED = TokenAllocationRatio.builder()
                .focusRatio(0.35)
                .panoramicRatio(0.35)
                .strategicRatio(0.3)
                .build();
        
        /**
         * 历史优先模式（更多给战略层）
         */
        public static final TokenAllocationRatio HISTORY_FIRST = TokenAllocationRatio.builder()
                .focusRatio(0.3)
                .panoramicRatio(0.3)
                .strategicRatio(0.4)
                .build();
        
        /**
         * 验证比例是否有效
         */
        public boolean isValid() {
            double total = focusRatio + panoramicRatio + strategicRatio;
            return Math.abs(total - 1.0) < 0.001 && 
                   focusRatio > 0 && panoramicRatio > 0 && strategicRatio > 0;
        }
    }
    
    /**
     * 计算可用Token
     */
    public void calculateAvailableTokens() {
        this.availableTokens = Math.max(0, maxTokens - reservedTokens);
    }
    
    /**
     * 分配Token预算
     * 
     * @param focusNeeded 焦点层需要的Token数
     * @param panoramicNeeded 全景层需要的Token数
     * @param strategicNeeded 战略层需要的Token数
     */
    public void allocateTokens(long focusNeeded, long panoramicNeeded, long strategicNeeded) {
        calculateAvailableTokens();
        
        // 焦点层优先，必须满足
        this.focusTokens = Math.min(focusNeeded, availableTokens);
        long remaining = availableTokens - focusTokens;
        
        if (remaining <= 0) {
            // 只能保留焦点层
            this.panoramicTokens = 0;
            this.strategicTokens = 0;
            suggestCompression(CompressionLevel.EXTREME, CompressionLevel.EXTREME);
            return;
        }
        
        // 根据比例分配剩余Token
        if (panoramicNeeded + strategicNeeded <= remaining) {
            // 都能满足
            this.panoramicTokens = panoramicNeeded;
            this.strategicTokens = strategicNeeded;
            suggestCompression(CompressionLevel.NONE, CompressionLevel.NONE);
        } else {
            // 需要压缩，按比例分配
            double panoramicWeight = allocationRatio.getPanoramicRatio() / 
                (allocationRatio.getPanoramicRatio() + allocationRatio.getStrategicRatio());
            
            this.panoramicTokens = (long)(remaining * panoramicWeight);
            this.strategicTokens = remaining - panoramicTokens;
            
            // 计算压缩级别
            CompressionLevel panoramicLevel = calculateCompressionLevel(panoramicNeeded, panoramicTokens);
            CompressionLevel strategicLevel = calculateCompressionLevel(strategicNeeded, strategicTokens);
            suggestCompression(panoramicLevel, strategicLevel);
        }
        
        this.usedTokens = focusTokens + panoramicTokens + strategicTokens;
    }
    
    /**
     * 动态调整分配
     * 根据实际使用情况动态调整Token分配
     */
    public void dynamicAdjust(long actualFocus, long actualPanoramic, long actualStrategic) {
        long total = actualFocus + actualPanoramic + actualStrategic;
        
        if (total <= availableTokens) {
            // 不需要调整
            this.focusTokens = actualFocus;
            this.panoramicTokens = actualPanoramic;
            this.strategicTokens = actualStrategic;
            this.usedTokens = total;
            return;
        }
        
        // 需要压缩，重新分配
        allocateTokens(actualFocus, actualPanoramic, actualStrategic);
    }
    
    /**
     * 计算压缩级别
     */
    private CompressionLevel calculateCompressionLevel(long needed, long allocated) {
        if (allocated >= needed) {
            return CompressionLevel.NONE;
        }
        
        double ratio = (double) allocated / needed;
        if (ratio >= 0.8) {
            return CompressionLevel.LIGHT;
        } else if (ratio >= 0.6) {
            return CompressionLevel.MODERATE;
        } else if (ratio >= 0.4) {
            return CompressionLevel.HEAVY;
        } else {
            return CompressionLevel.EXTREME;
        }
    }
    
    /**
     * 设置压缩建议
     */
    private void suggestCompression(CompressionLevel panoramicLevel, CompressionLevel strategicLevel) {
        if (compressionSuggestions == null) {
            compressionSuggestions = new HashMap<>();
        }
        compressionSuggestions.put("panoramic", panoramicLevel);
        compressionSuggestions.put("strategic", strategicLevel);
    }
    
    /**
     * 获取使用率
     */
    public double getUsageRatio() {
        if (availableTokens == 0) {
            return 1.0;
        }
        return (double) usedTokens / availableTokens;
    }
    
    /**
     * 是否超出预算
     */
    public boolean isOverBudget() {
        return usedTokens > availableTokens;
    }
    
    /**
     * 获取剩余Token
     */
    public long getRemainingTokens() {
        return Math.max(0, availableTokens - usedTokens);
    }
    
    /**
     * 创建默认预算
     */
    public static TokenBudget createDefault(long maxTokens) {
        TokenBudget budget = TokenBudget.builder()
                .maxTokens(maxTokens)
                .reservedTokens(1000)
                .allocationRatio(TokenAllocationRatio.DEFAULT)
                .build();
        budget.calculateAvailableTokens();
        return budget;
    }
    
    /**
     * 创建紧凑预算
     */
    public static TokenBudget createCompact(long maxTokens) {
        TokenBudget budget = TokenBudget.builder()
                .maxTokens(maxTokens)
                .reservedTokens(500)  // 更少的预留
                .allocationRatio(TokenAllocationRatio.COMPACT)
                .build();
        budget.calculateAvailableTokens();
        return budget;
    }
    
    /**
     * 获取预算摘要
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("Token Budget: ");
        sb.append("Total=").append(maxTokens);
        sb.append(", Available=").append(availableTokens);
        sb.append(", Used=").append(usedTokens);
        sb.append(" (").append(String.format("%.1f%%", getUsageRatio() * 100)).append(")");
        sb.append(" | Allocation: Focus=").append(focusTokens);
        sb.append(", Panoramic=").append(panoramicTokens);
        sb.append(", Strategic=").append(strategicTokens);
        
        if (compressionSuggestions != null && !compressionSuggestions.isEmpty()) {
            sb.append(" | Compression: ");
            compressionSuggestions.forEach((layer, level) -> 
                sb.append(layer).append("=").append(level.name()).append(" "));
        }
        
        return sb.toString();
    }
}