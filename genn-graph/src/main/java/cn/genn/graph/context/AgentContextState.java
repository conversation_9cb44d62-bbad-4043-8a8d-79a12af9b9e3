package cn.genn.graph.context;

import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.context.layer.InstantContext;
import cn.genn.graph.context.layer.PanoramicLayer;
import cn.genn.graph.context.layer.StrategicLayer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Agent上下文状态 - 三层上下文模型
 * <p>
 * 这是所有层级共享的状态对象，封装了三层上下文模型：
 * 1. Focus Layer (焦点层) - 当前任务的核心信息和即时状态
 * 2. Panorama Layer (全景层) - 会话级别的完整上下文和历史信息
 * 3. Strategic Layer (战略层) - 长期目标、策略和跨会话的持久化信息
 * <p>
 * 这个类作为数据流的载体，在整个图执行过程中传递和更新状态信息。
 * 支持多轮对话的上下文状态缓存和管理。
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentContextState implements Cloneable {

    // ==================== 任务焦点层 (Task Focus Layer) ====================
    /**
     * 任务焦点层 - 为当前思维节点的单步推理提供必需的、即时的执行上下文
     * 这是代理"正在思考"的内容，完整性优先，是agent处理问题最关键的上下文
     */
    @Builder.Default
    private FocusLayer focus = new FocusLayer();

    // ==================== 任务全景层 (Task Panorama Layer) ====================
    /**
     * 任务全景层 - 提供完成当前整个任务（思维树）所需的背景信息和关联知识
     * 帮助LLM理解任务全貌和节点之间的依赖关系，半动态性、任务周期内持久、相关性优先
     */
    @Builder.Default
    private PanoramicLayer panorama = new PanoramicLayer();

    // ==================== 战略记忆层 (Strategic Memory Layer) ====================
    /**
     * 战略记忆层 - 提供跨任务、跨会话的长期记忆和通用背景知识
     * 为任务规划和高级推理提供战略性指导，低动态性、持久性、选择性加载
     */
    @Builder.Default
    private StrategicLayer strategic = new StrategicLayer();
    
    // ==================== 瞬时状态信息,用户节点间传递信息 ====================
    /**
     * 瞬时状态信息,此类无法序列化
     */
    @Builder.Default
    @JsonIgnore
    private InstantContext instant = new InstantContext();
    
    /**
     * 克隆上下文状态
     * 深度克隆所有三层上下文
     */
    @Override
    public AgentContextState clone() {
        try {
            AgentContextState cloned = (AgentContextState) super.clone();
            
            // 深度克隆三层上下文
            cloned.focus = this.focus != null ? this.focus.clone() : new FocusLayer();
            cloned.panorama = this.panorama != null ? this.panorama.clone() : new PanoramicLayer();
            cloned.strategic = this.strategic != null ? this.strategic.clone() : new StrategicLayer();
            
            return cloned;
        } catch (CloneNotSupportedException e) {
            log.error("Failed to clone AgentContextState", e);
            // 如果克隆失败，使用Builder重建
            return AgentContextState.builder()
                    .focus(this.focus != null ? this.focus.clone() : new FocusLayer())
                    .panorama(this.panorama != null ? this.panorama.clone() : new PanoramicLayer())
                    .strategic(this.strategic != null ? this.strategic.clone() : new StrategicLayer())
                    .build();
        }
    }
    
}
