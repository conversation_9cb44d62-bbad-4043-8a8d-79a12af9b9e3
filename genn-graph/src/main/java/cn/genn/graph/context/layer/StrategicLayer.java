package cn.genn.graph.context.layer;

import cn.genn.graph.context.compression.CompressionLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static cn.genn.graph.context.layer.XMLTagUtils.*;

/**
 * 战略层实现
 * 对应 AgentContextState.StrategicMemoryContext
 * 提供跨任务、跨会话的长期记忆和通用背景知识
 * 
 * <AUTHOR>
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StrategicLayer implements ContextLayer, Serializable, Cloneable {
    
    private static final long serialVersionUID = 1L;
    
    
    // ==================== 会话全局背景 (Global Conversation ContextInput) ====================
    
    /**
     * 下游agent的中间过程 - 记录下游agent的中间过程数据
     */
    @Builder.Default
    private Map<String, Object> downstreamProgress = new ConcurrentHashMap<>();
    
    /**
     * 完整会话历史
     */
    @Builder.Default
    private List<Message> allConversations = new ArrayList<>();
    
    /**
     * 领域知识库 - 特定领域的专业知识和经验
     * -- SETTER --
     *  设置领域知识

     */
    private String domainKnowledge;
    
    // ==================== 层管理属性 ====================
    
    /**
     * 压缩级别
     */
    @Builder.Default
    private CompressionLevel compressionLevel = CompressionLevel.NONE;
    
    /**
     * Token数量
     */
    private long tokenCount;
    
    /**
     * 原始Token数量（压缩前）
     */
    private long originalTokenCount;
    
    /**
     * 原始内容
     */
    private String rawContent;
    
    @Override
    public String getName() {
        return "strategic";
    }
    
    @Override
    public int getPriority() {
        return LayerPriority.STRATEGIC.getValue();
    }
    
    @Override
    public boolean isCompressible() {
        return true;
    }
    
    @Override
    public String getContent() {
        return formatContent();
    }
    
    @Override
    public void setContent(String content) {
        this.rawContent = content;
        parseContent(content);
    }
    
    @Override
    public StrategicLayer clone() {
        try {
            StrategicLayer cloned = (StrategicLayer) super.clone();
            // 深拷贝集合类型
            cloned.downstreamProgress = new ConcurrentHashMap<>(this.downstreamProgress);
            cloned.allConversations = new ArrayList<>(this.allConversations);
            return cloned;
        } catch (CloneNotSupportedException e) {
            log.error("Failed to clone StrategicLayer", e);
            return StrategicLayer.builder()
                    .downstreamProgress(new ConcurrentHashMap<>(this.downstreamProgress))
                    .allConversations(new ArrayList<>(this.allConversations))
                    .domainKnowledge(this.domainKnowledge)
                    .compressionLevel(this.compressionLevel)
                    .tokenCount(this.tokenCount)
                    .originalTokenCount(this.originalTokenCount)
                    .rawContent(this.rawContent)
                    .build();
        }
    }
    
    /**
     * 格式化内容为XML结构
     */
    private String formatContent() {
        StringBuilder sb = new StringBuilder();
        
        // 领域知识（短标签）
        if (domainKnowledge != null && !domainKnowledge.isEmpty() && 
            compressionLevel != CompressionLevel.EXTREME) {
            String knowledge = compressionLevel == CompressionLevel.HEAVY ? 
                summarize(domainKnowledge, 200) : domainKnowledge;
            sb.append(wrapContentWithNewLine("domain_knowledge", knowledge));
        }
        
        // 下游进展
        if (!downstreamProgress.isEmpty() && 
            compressionLevel.ordinal() <= CompressionLevel.MODERATE.ordinal()) {
            sb.append(openTag("downstreamProgress")).append("\n");
            int limit = calculateLimit(downstreamProgress.size());
            downstreamProgress.entrySet().stream()
                .limit(limit)
                .forEach(entry -> {
                    sb.append(wrapContentWithAttr("item", "agent", entry.getKey(),
                        formatValue(entry.getValue()))).append("\n");
                });
            
            if (downstreamProgress.size() > limit) {
                sb.append(wrapContentWithAttr("moreInfo", "count", 
                    String.valueOf(downstreamProgress.size() - limit),
                    "more progress items")).append("\n");
            }
            sb.append(closeTag("downstreamProgress")).append("\n");
        }
        
        // 会话历史
        if (!allConversations.isEmpty() && 
            compressionLevel != CompressionLevel.EXTREME) {
            //todo 压缩上下文
//            sb.append(openTag("conversationHistory")).append("\n");
//            int limit = calculateConversationLimit();
//
//            // 获取最近的N条消息
//            List<Message> recentMessages = allConversations.size() > limit ?
//                allConversations.subList(allConversations.size() - limit, allConversations.size()) :
//                allConversations;
//
//            for (Message message : recentMessages) {
//                sb.append(wrapContentWithAttr("item", "type", message.getMessageType().name(),
//                    message.getText())).append("\n");
//            }
//
//            if (allConversations.size() > limit) {
//                sb.append(wrapContentWithAttr("moreInfo", "count", 
//                    String.valueOf(allConversations.size() - limit),
//                    "earlier messages omitted")).append("\n");
//            }
//            sb.append(closeTag("conversationHistory")).append("\n");
        }
        
        // 如果有原始内容且上述格式化内容为空，则返回原始内容
        if (sb.isEmpty() && rawContent != null) {
            return wrapContent("rawContent", rawContent);
        }
        
        return sb.toString().trim();
    }
    
    /**
     * 截断内容
     */
    private String truncateContent(String content, int maxLength) {
        if (content == null || content.length() <= maxLength) {
            return content;
        }
        return content.substring(0, maxLength) + "...";
    }
    
    /**
     * 根据压缩级别计算显示限制
     */
    private int calculateLimit(int totalSize) {
        switch (compressionLevel) {
            case EXTREME:
                return Math.min(1, totalSize);
            case HEAVY:
                return Math.min(3, totalSize);
            case MODERATE:
                return Math.min(5, totalSize);
            case LIGHT:
                return Math.min(8, totalSize);
            default:
                return totalSize;
        }
    }
    
    /**
     * 计算会话历史显示限制
     */
    private int calculateConversationLimit() {
        switch (compressionLevel) {
            case EXTREME:
                return 0; // 极限压缩时不显示会话历史
            case HEAVY:
                return 3;
            case MODERATE:
                return 5;
            case LIGHT:
                return 8;
            default:
                return 10;
        }
    }
    
    /**
     * 获取消息最大长度
     */
    private int getMaxMessageLength() {
        switch (compressionLevel) {
            case HEAVY:
                return 100;
            case MODERATE:
                return 200;
            case LIGHT:
                return 300;
            default:
                return 500;
        }
    }
    
    /**
     * 摘要内容
     */
    private String summarize(String content, int maxLength) {
        if (content == null || content.length() <= maxLength) {
            return content;
        }
        return content.substring(0, maxLength - 3) + "...";
    }
    
    /**
     * 格式化值对象
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "null";
        }
        if (value instanceof Map || value instanceof List) {
            // 对于复杂对象，返回简要描述
            return value.getClass().getSimpleName() + "[" + 
                   (value instanceof Map ? ((Map<?,?>)value).size() : ((List<?>)value).size()) + 
                   " items]";
        }
        String str = value.toString();
        if (str.length() > 100) {
            return str.substring(0, 97) + "...";
        }
        return str;
    }
    
    /**
     * 解析内容
     */
    private void parseContent(String content) {
        if (content == null || content.isEmpty()) {
        }
        // 简化实现，实际可能需要更复杂的解析逻辑
        // 可以解析特定格式的内容并更新相应字段
    }
    
    /**
     * 添加下游进展
     */
    public void addDownstreamProgress(String agentName, Object progress) {
        if (agentName != null && progress != null) {
            this.downstreamProgress.put(agentName, progress);
        }
    }
    
    /**
     * 添加会话消息
     */
    public void addConversation(Message message) {
        if (message != null) {
            this.allConversations.add(message);
            // 保持会话历史在合理范围内（例如最多保留100条）
            if (allConversations.size() > 100) {
                // 可以考虑将旧消息进行摘要后存储
                allConversations.remove(0);
            }
        }
    }
    
    /**
     * 批量添加会话消息
     */
    public void addConversations(List<Message> messages) {
        if (messages != null && !messages.isEmpty()) {
            for (Message message : messages) {
                addConversation(message);
            }
        }
    }

    /**
     * 追加领域知识
     */
    public void appendDomainKnowledge(String knowledge) {
        if (knowledge != null && !knowledge.isEmpty()) {
            if (this.domainKnowledge == null) {
                this.domainKnowledge = knowledge;
            } else {
                this.domainKnowledge += "\n" + knowledge;
            }
        }
    }
    
    /**
     * 应用压缩
     */
    public void applyCompression(CompressionLevel level) {
        if (this.originalTokenCount == 0) {
            this.originalTokenCount = this.tokenCount;
        }
        this.compressionLevel = level;
        log.info("Applied {} to strategic layer", level.getDescription());
    }
    
}