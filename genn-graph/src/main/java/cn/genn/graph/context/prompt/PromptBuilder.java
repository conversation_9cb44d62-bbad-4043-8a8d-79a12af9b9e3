package cn.genn.graph.context.prompt;

import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.context.layer.PanoramicLayer;
import cn.genn.graph.context.layer.StrategicLayer;
import cn.genn.graph.context.token.TokenBudget;

/**
 * Prompt构造器接口
 * 负责将三层上下文构造成最终的Prompt
 * 
 * <AUTHOR>
 */
public interface PromptBuilder {
    
    /**
     * 构建Prompt
     * 
     * @param focusLayer 焦点层
     * @param panoramicLayer 全景层
     * @param strategicLayer 战略层
     * @param tokenBudget Token预算
     * @return Prompt结果
     */
    PromptResult build(
        String systemPrompt,
        FocusLayer focusLayer,
        PanoramicLayer panoramicLayer,
        StrategicLayer strategicLayer,
        TokenBudget tokenBudget
    );
    
    /**
     * 获取构造器名称
     * 
     * @return 构造器名称
     */
    String getName();
    
    /**
     * 获取构造器描述
     * 
     * @return 构造器描述
     */
    default String getDescription() {
        return "Prompt builder: " + getName();
    }
    
    /**
     * 验证Token预算是否足够
     * 
     * @param focusTokens 焦点层Token数
     * @param panoramicTokens 全景层Token数
     * @param strategicTokens 战略层Token数
     * @param budget Token预算
     * @return true表示预算足够
     */
    default boolean validateBudget(long focusTokens, long panoramicTokens, 
                                  long strategicTokens, TokenBudget budget) {
        long total = focusTokens + panoramicTokens + strategicTokens;
        return total <= budget.getAvailableTokens();
    }
}