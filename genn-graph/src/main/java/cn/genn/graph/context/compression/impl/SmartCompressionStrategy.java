package cn.genn.graph.context.compression.impl;

import cn.genn.graph.context.compression.CompressionLevel;
import cn.genn.graph.context.compression.CompressionResult;
import cn.genn.graph.context.compression.CompressionStrategy;
import cn.genn.graph.context.layer.ContextLayer;
import cn.genn.graph.context.token.TokenCalculator;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 智能组合压缩策略
 * 组合使用多种压缩策略，根据内容类型智能选择最适合的策略
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SmartCompressionStrategy implements CompressionStrategy {
    
    private static final String STRATEGY_NAME = "SmartCompression";
    private static final int COMPRESSION_PRIORITY = 5;  // 最高优先级
    
    /**
     * 内容类型检测模式
     */
    private static final Pattern CODE_PATTERN = Pattern.compile(
        "```[\\s\\S]*?```|" +
        "^\\s*(public|private|protected|class|interface|function|def|var|let|const)\\s+|" +
        "\\{[\\s\\S]*?\\}|" +
        "\\([\\s\\S]*?\\)|" +
        "\\[\\s\\S]*?\\]"
    );
    
    private static final Pattern DIALOGUE_PATTERN = Pattern.compile(
        "^(User|用户|Human|Assistant|助手|AI|System|系统)[:：]\\s*",
        Pattern.MULTILINE | Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern LOG_PATTERN = Pattern.compile(
        "\\[(\\d{4}-\\d{2}-\\d{2}[T\\s]\\d{2}:\\d{2}:\\d{2})\\]|" +
        "\\b(ERROR|WARN|INFO|DEBUG|TRACE)\\b|" +
        "\\b\\d+\\.\\d+\\.\\d+\\.\\d+\\b"  // IP地址
    );
    
    private static final Pattern STRUCTURED_DATA_PATTERN = Pattern.compile(
        "\\{[\"']\\w+[\"']\\s*:\\s*[\\s\\S]*?\\}|" +  // JSON
        "<\\w+[^>]*>[\\s\\S]*?</\\w+>|" +  // XML
        "^\\w+:\\s*.+$",  // YAML-like
        Pattern.MULTILINE
    );
    
    /**
     * Spring应用上下文
     */
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * Token计算器
     */
    @Autowired
    private TokenCalculator tokenCalculator;
    
    /**
     * 可用的压缩策略
     */
    private final Map<String, CompressionStrategy> availableStrategies = new ConcurrentHashMap<>();
    
    /**
     * 策略链配置
     */
    private final Map<ContentType, List<String>> strategyChains = new ConcurrentHashMap<>();
    
    /**
     * 压缩效果评估缓存
     */
    private final Map<String, StrategyPerformance> performanceCache = new ConcurrentHashMap<>();
    private static final int MAX_PERFORMANCE_CACHE_SIZE = 100;
    
    /**
     * 缓存
     */
    private final Map<String, CompressionResult> resultCache = new ConcurrentHashMap<>();
    private static final int MAX_RESULT_CACHE_SIZE = 50;
    
    /**
     * 初始化
     */
    @PostConstruct
    public void initialize() {
        // 加载所有可用的压缩策略
        loadAvailableStrategies();
        
        // 初始化策略链
        initializeStrategyChains();
        
        log.info("SmartCompressionStrategy initialized with {} available strategies", 
                availableStrategies.size());
    }
    
    /**
     * 加载可用的压缩策略
     */
    private void loadAvailableStrategies() {
        Map<String, CompressionStrategy> strategies = applicationContext.getBeansOfType(CompressionStrategy.class);
        
        for (Map.Entry<String, CompressionStrategy> entry : strategies.entrySet()) {
            CompressionStrategy strategy = entry.getValue();
            // 排除自己
            if (!(strategy instanceof SmartCompressionStrategy)) {
                availableStrategies.put(strategy.getName(), strategy);
                log.debug("Loaded compression strategy: {}", strategy.getName());
            }
        }
    }
    
    /**
     * 初始化策略链
     */
    private void initializeStrategyChains() {
        // 代码内容策略链
        strategyChains.put(ContentType.CODE, Arrays.asList(
            "PriorityCompression",
            "SlidingWindowCompression",
            "SummaryCompression"
        ));
        
        // 对话内容策略链
        strategyChains.put(ContentType.DIALOGUE, Arrays.asList(
            "SlidingWindowCompression",
            "SummaryCompression",
            "PriorityCompression"
        ));
        
        // 日志内容策略链
        strategyChains.put(ContentType.LOG, Arrays.asList(
            "SlidingWindowCompression",
            "PriorityCompression",
            "SummaryCompression"
        ));
        
        // 结构化数据策略链
        strategyChains.put(ContentType.STRUCTURED_DATA, Arrays.asList(
            "PriorityCompression",
            "SummaryCompression",
            "SlidingWindowCompression"
        ));
        
        // 普通文本策略链
        strategyChains.put(ContentType.PLAIN_TEXT, Arrays.asList(
            "SummaryCompression",
            "PriorityCompression",
            "SlidingWindowCompression"
        ));
    }
    
    @Override
    public CompressionResult compress(String content, long targetTokens) {
        Instant startTime = Instant.now();
        
        try {
            // 检查输入
            if (content == null || content.isEmpty()) {
                return createEmptyResult(startTime);
            }
            
            // 计算原始Token数
            long originalTokenCount = tokenCalculator.calculate(content);
            
            // 如果原始内容已经满足要求，直接返回
            if (originalTokenCount <= targetTokens) {
                return createUncompressedResult(content, originalTokenCount, startTime);
            }
            
            // 检查缓存
            String cacheKey = generateCacheKey(content, targetTokens);
            CompressionResult cachedResult = resultCache.get(cacheKey);
            if (cachedResult != null) {
                log.debug("Using cached compression result for key: {}", cacheKey);
                return cachedResult;
            }
            
            // 检测内容类型
            ContentType contentType = detectContentType(content);
            log.debug("Detected content type: {}", contentType);
            
            // 根据压缩比例确定压缩级别
            double compressionRatio = (double) targetTokens / originalTokenCount;
            CompressionLevel level = determineCompressionLevel(compressionRatio);
            
            // 执行智能压缩
            CompressionResult result = performSmartCompression(
                content, 
                targetTokens, 
                level, 
                contentType,
                originalTokenCount,
                startTime
            );
            
            // 缓存结果
            cacheResult(cacheKey, result);
            
            // 更新性能统计
            updatePerformanceStats(contentType, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("Smart compression failed", e);
            return CompressionResult.failure(
                "Smart compression failed: " + e.getMessage(),
                content,
                tokenCalculator.calculate(content)
            );
        }
    }
    
    /**
     * 检测内容类型
     */
    private ContentType detectContentType(String content) {
        // 计算各种模式的匹配度
        Map<ContentType, Double> scores = new HashMap<>();
        
        // 检测代码
        if (CODE_PATTERN.matcher(content).find()) {
            scores.put(ContentType.CODE, calculatePatternScore(content, CODE_PATTERN));
        }
        
        // 检测对话
        if (DIALOGUE_PATTERN.matcher(content).find()) {
            scores.put(ContentType.DIALOGUE, calculatePatternScore(content, DIALOGUE_PATTERN));
        }
        
        // 检测日志
        if (LOG_PATTERN.matcher(content).find()) {
            scores.put(ContentType.LOG, calculatePatternScore(content, LOG_PATTERN));
        }
        
        // 检测结构化数据
        if (STRUCTURED_DATA_PATTERN.matcher(content).find()) {
            scores.put(ContentType.STRUCTURED_DATA, 
                      calculatePatternScore(content, STRUCTURED_DATA_PATTERN));
        }
        
        // 选择得分最高的类型
        if (scores.isEmpty()) {
            return ContentType.PLAIN_TEXT;
        }
        
        return scores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(ContentType.PLAIN_TEXT);
    }
    
    /**
     * 计算模式匹配得分
     */
    private double calculatePatternScore(String content, Pattern pattern) {
        int matches = 0;
        int totalLength = content.length();
        
        var matcher = pattern.matcher(content);
        while (matcher.find()) {
            matches++;
        }
        
        // 计算匹配密度
        return (double) matches / Math.max(1, totalLength / 100);
    }
    
    /**
     * 执行智能压缩
     */
    private CompressionResult performSmartCompression(
            String content,
            long targetTokens,
            CompressionLevel level,
            ContentType contentType,
            long originalTokenCount,
            Instant startTime) {
        
        // 获取策略链
        List<String> strategyChain = getStrategyChain(contentType, level);
        
        // 尝试使用策略链中的策略
        CompressionResult bestResult = null;
        List<CompressionAttempt> attempts = new ArrayList<>();
        
        for (String strategyName : strategyChain) {
            CompressionStrategy strategy = availableStrategies.get(strategyName);
            if (strategy == null) {
                log.warn("Strategy not found: {}", strategyName);
                continue;
            }
            
            // 尝试压缩
            CompressionResult result = tryCompress(strategy, content, targetTokens, level);
            
            // 记录尝试
            attempts.add(new CompressionAttempt(strategyName, result));
            
            // 评估结果
            if (result.isSuccess() && result.meetsTarget(targetTokens)) {
                if (bestResult == null || isBetterResult(result, bestResult)) {
                    bestResult = result;
                }
                
                // 如果达到理想的压缩效果，提前结束
                if (isIdealResult(result, targetTokens, level)) {
                    break;
                }
            }
        }
        
        // 如果没有找到合适的结果，使用组合策略
        if (bestResult == null || !bestResult.meetsTarget(targetTokens)) {
            bestResult = performCombinedCompression(
                content, 
                targetTokens, 
                level, 
                contentType,
                attempts
            );
        }
        
        // 构建最终结果
        return buildFinalResult(
            bestResult, 
            originalTokenCount, 
            level, 
            contentType, 
            attempts, 
            startTime
        );
    }
    
    /**
     * 获取策略链
     */
    private List<String> getStrategyChain(ContentType contentType, CompressionLevel level) {
        List<String> baseChain = strategyChains.getOrDefault(
            contentType, 
            strategyChains.get(ContentType.PLAIN_TEXT)
        );
        
        // 根据压缩级别调整策略链
        if (level.isSevereCompression()) {
            // 严重压缩时，优先使用更激进的策略
            List<String> adjustedChain = new ArrayList<>();
            adjustedChain.add("SlidingWindowCompression");
            adjustedChain.addAll(baseChain);
            return adjustedChain.stream().distinct().collect(Collectors.toList());
        }
        
        return baseChain;
    }
    
    /**
     * 尝试压缩
     */
    private CompressionResult tryCompress(
            CompressionStrategy strategy,
            String content,
            long targetTokens,
            CompressionLevel level) {
        
        try {
            log.debug("Trying compression with strategy: {}", strategy.getName());
            CompressionResult result = strategy.compress(content, targetTokens);
            
            // 设置压缩级别（如果策略没有设置）
            if (result.getCompressionLevel() == null) {
                result.setCompressionLevel(level);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Strategy {} failed: {}", strategy.getName(), e.getMessage());
            return CompressionResult.failure(
                "Strategy failed: " + e.getMessage(),
                content,
                tokenCalculator.calculate(content)
            );
        }
    }
    
    /**
     * 执行组合压缩
     */
    private CompressionResult performCombinedCompression(
            String content,
            long targetTokens,
            CompressionLevel level,
            ContentType contentType,
            List<CompressionAttempt> previousAttempts) {
        
        log.info("Performing combined compression for content type: {}", contentType);
        
        // 分段压缩
        List<ContentSegment> segments = segmentContent(content, contentType);
        StringBuilder combinedResult = new StringBuilder();
        long currentTokens = 0;
        
        for (ContentSegment segment : segments) {
            // 为每个段选择最佳策略
            String bestStrategy = selectBestStrategyForSegment(segment, contentType);
            CompressionStrategy strategy = availableStrategies.get(bestStrategy);
            
            if (strategy != null) {
                long segmentTargetTokens = calculateSegmentTargetTokens(
                    segment, 
                    segments, 
                    targetTokens,
                    currentTokens
                );
                
                CompressionResult segmentResult = strategy.compress(
                    segment.content, 
                    segmentTargetTokens
                );
                
                if (segmentResult.isSuccess()) {
                    combinedResult.append(segmentResult.getContent());
                    combinedResult.append("\n\n");
                    currentTokens += segmentResult.getTokenCount();
                }
            }
            
            // 检查是否已达到目标
            if (currentTokens >= targetTokens) {
                break;
            }
        }
        
        String finalContent = combinedResult.toString().trim();
        long finalTokenCount = tokenCalculator.calculate(finalContent);
        
        return CompressionResult.builder()
            .content(finalContent)
            .tokenCount(finalTokenCount)
            .originalTokenCount(tokenCalculator.calculate(content))
            .compressionLevel(level)
            .strategyName(STRATEGY_NAME + " (Combined)")
            .success(true)
            .build();
    }
    
    /**
     * 分段内容
     */
    private List<ContentSegment> segmentContent(String content, ContentType type) {
        List<ContentSegment> segments = new ArrayList<>();
        
        switch (type) {
            case CODE:
                segments = segmentCodeContent(content);
                break;
            case DIALOGUE:
                segments = segmentDialogueContent(content);
                break;
            case LOG:
                segments = segmentLogContent(content);
                break;
            default:
                segments = segmentPlainContent(content);
                break;
        }
        
        return segments;
    }
    
    /**
     * 分段代码内容
     */
    private List<ContentSegment> segmentCodeContent(String content) {
        List<ContentSegment> segments = new ArrayList<>();
        
        // 按代码块分割
        String[] blocks = content.split("```");
        for (int i = 0; i < blocks.length; i++) {
            if (!blocks[i].trim().isEmpty()) {
                ContentType segmentType = (i % 2 == 1) ? ContentType.CODE : ContentType.PLAIN_TEXT;
                segments.add(new ContentSegment(blocks[i], segmentType));
            }
        }
        
        return segments.isEmpty() ? segmentPlainContent(content) : segments;
    }
    
    /**
     * 分段对话内容
     */
    private List<ContentSegment> segmentDialogueContent(String content) {
        List<ContentSegment> segments = new ArrayList<>();
        
        var matcher = DIALOGUE_PATTERN.matcher(content);
        int lastEnd = 0;
        
        while (matcher.find()) {
            if (lastEnd > 0) {
                String turn = content.substring(lastEnd, matcher.start()).trim();
                if (!turn.isEmpty()) {
                    segments.add(new ContentSegment(turn, ContentType.DIALOGUE));
                }
            }
            lastEnd = matcher.start();
        }
        
        if (lastEnd < content.length()) {
            String lastTurn = content.substring(lastEnd).trim();
            if (!lastTurn.isEmpty()) {
                segments.add(new ContentSegment(lastTurn, ContentType.DIALOGUE));
            }
        }
        
        return segments.isEmpty() ? segmentPlainContent(content) : segments;
    }
    
    /**
     * 分段日志内容
     */
    private List<ContentSegment> segmentLogContent(String content) {
        List<ContentSegment> segments = new ArrayList<>();
        String[] lines = content.split("\n");
        StringBuilder currentSegment = new StringBuilder();
        
        for (String line : lines) {
            if (LOG_PATTERN.matcher(line).find() && currentSegment.length() > 0) {
                segments.add(new ContentSegment(currentSegment.toString(), ContentType.LOG));
                currentSegment = new StringBuilder();
            }
            currentSegment.append(line).append("\n");
        }
        
        if (currentSegment.length() > 0) {
            segments.add(new ContentSegment(currentSegment.toString(), ContentType.LOG));
        }
        
        return segments.isEmpty() ? segmentPlainContent(content) : segments;
    }
    
    /**
     * 分段普通内容
     */
    private List<ContentSegment> segmentPlainContent(String content) {
        List<ContentSegment> segments = new ArrayList<>();
        
        // 按段落分割
        String[] paragraphs = content.split("\n\n+");
        for (String paragraph : paragraphs) {
            if (!paragraph.trim().isEmpty()) {
                segments.add(new ContentSegment(paragraph.trim(), ContentType.PLAIN_TEXT));
            }
        }
        
        return segments;
    }
    
    /**
     * 为段选择最佳策略
     */
    private String selectBestStrategyForSegment(ContentSegment segment, ContentType contentType) {
        // 基于历史性能数据选择
        String cacheKey = contentType.name() + "_segment";
        StrategyPerformance performance = performanceCache.get(cacheKey);
        
        if (performance != null && performance.getBestStrategy() != null) {
            return performance.getBestStrategy();
        }
        
        // 使用默认策略链的第一个
        List<String> chain = strategyChains.getOrDefault(
            contentType,
            strategyChains.get(ContentType.PLAIN_TEXT)
        );
        
        return chain.isEmpty() ? "SummaryCompression" : chain.get(0);
    }
    
    /**
     * 计算段目标Token数
     */
    private long calculateSegmentTargetTokens(
            ContentSegment segment,
            List<ContentSegment> allSegments,
            long totalTargetTokens,
            long currentUsedTokens) {
        
        // 计算段的权重
        long segmentTokens = tokenCalculator.calculate(segment.content);
        long totalTokens = allSegments.stream()
            .mapToLong(s -> tokenCalculator.calculate(s.content))
            .sum();
        
        double weight = (double) segmentTokens / totalTokens;
        long segmentTarget = Math.round(totalTargetTokens * weight);
        
        // 确保不超过剩余的Token预算
        long remainingBudget = totalTargetTokens - currentUsedTokens;
        return Math.min(segmentTarget, remainingBudget);
    }
    
    /**
     * 判断是否为更好的结果
     */
    private boolean isBetterResult(CompressionResult newResult, CompressionResult currentBest) {
        // 优先选择成功的结果
        if (newResult.isSuccess() && !currentBest.isSuccess()) {
            return true;
        }
        
        // 都成功时，选择更接近目标的
        if (newResult.isSuccess() && currentBest.isSuccess()) {
            // 保留更多内容的结果更好
            return newResult.getTokenCount() > currentBest.getTokenCount();
        }
        
        return false;
    }
    
    /**
     * 判断是否为理想结果
     */
    private boolean isIdealResult(CompressionResult result, long targetTokens, CompressionLevel level) {
        if (!result.isSuccess()) {
            return false;
        }
        
        long tokenCount = result.getTokenCount();
        
        // 在目标范围内
        if (tokenCount > targetTokens) {
            return false;
        }
        
        // 利用率足够高（至少使用了90%的目标Token）
        double utilization = (double) tokenCount / targetTokens;
        return utilization >= 0.9;
    }
    
    /**
     * 构建最终结果
     */
    private CompressionResult buildFinalResult(
            CompressionResult bestResult,
            long originalTokenCount,
            CompressionLevel level,
            ContentType contentType,
            List<CompressionAttempt> attempts,
            Instant startTime) {
        
        if (bestResult == null) {
            return CompressionResult.failure(
                "No suitable compression strategy found",
                "",
                originalTokenCount
            );
        }
        
        // 增强结果信息
        bestResult.setStrategyName(STRATEGY_NAME);
        bestResult.setCompressionLevel(level);
        bestResult.setStartTime(startTime);
        bestResult.setEndTime(Instant.now());
        bestResult.calculateDuration();
        
        // 添加元数据
        bestResult.addMetadata("contentType", contentType.name());
        bestResult.addMetadata("attemptsCount", attempts.size());
        bestResult.addMetadata("strategiesUsed", 
            attempts.stream()
                .map(a -> a.strategyName)
                .collect(Collectors.joining(", "))
        );
        
        // 添加最佳策略信息
        CompressionAttempt bestAttempt = attempts.stream()
            .filter(a -> a.result.isSuccess())
            .min(Comparator.comparingLong(a -> Math.abs(a.result.getTokenCount() - bestResult.getTokenCount())))
            .orElse(null);
        
        if (bestAttempt != null) {
            bestResult.addMetadata("bestStrategy", bestAttempt.strategyName);
        }
        
        log.info("Smart compression completed: {} - Content type: {}", 
                bestResult.getSummary(), contentType);
        
        return bestResult;
    }
    
    /**
     * 更新性能统计
     */
    private void updatePerformanceStats(ContentType contentType, CompressionResult result) {
        String key = contentType.name();
        StrategyPerformance performance = performanceCache.computeIfAbsent(
            key, 
            k -> new StrategyPerformance()
        );
        
        performance.recordResult(result);
        
        // 限制缓存大小
        if (performanceCache.size() > MAX_PERFORMANCE_CACHE_SIZE) {
            // 移除最旧的条目
            performanceCache.entrySet().stream()
                .min(Comparator.comparingLong(e -> e.getValue().lastUpdateTime))
                .ifPresent(e -> performanceCache.remove(e.getKey()));
        }
    }
    
    /**
     * 确定压缩级别
     */
    private CompressionLevel determineCompressionLevel(double compressionRatio) {
        if (compressionRatio >= 0.8) {
            return CompressionLevel.LIGHT;
        } else if (compressionRatio >= 0.6) {
            return CompressionLevel.MODERATE;
        } else if (compressionRatio >= 0.4) {
            return CompressionLevel.HEAVY;
        } else {
            return CompressionLevel.EXTREME;
        }
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String content, long targetTokens) {
        int contentHash = content.hashCode();
        return String.format("%s_%d_%d", STRATEGY_NAME, contentHash, targetTokens);
    }
    
    /**
     * 缓存结果
     */
    private void cacheResult(String key, CompressionResult result) {
        if (resultCache.size() >= MAX_RESULT_CACHE_SIZE) {
            Iterator<String> iterator = resultCache.keySet().iterator();
            if (iterator.hasNext()) {
                iterator.next();
                iterator.remove();
            }
        }
        resultCache.put(key, result);
    }
    
    /**
     * 创建空结果
     */
    private CompressionResult createEmptyResult(Instant startTime) {
        CompressionResult result = CompressionResult.builder()
            .content("")
            .tokenCount(0)
            .originalTokenCount(0)
            .compressionRatio(1.0)
            .compressionLevel(CompressionLevel.NONE)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        result.calculateDuration();
        return result;
    }
    
    /**
     * 创建未压缩结果
     */
    private CompressionResult createUncompressedResult(String content, long tokenCount, Instant startTime) {
        CompressionResult result = CompressionResult.builder()
            .content(content)
            .tokenCount(tokenCount)
            .originalTokenCount(tokenCount)
            .compressionRatio(1.0)
            .compressionLevel(CompressionLevel.NONE)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        result.calculateDuration();
        result.addMetadata("reason", "Content already within target tokens");
        return result;
    }
    
    @Override
    public boolean canCompress(ContextLayer layer) {
        // 智能压缩策略可以压缩所有可压缩的层
        return layer != null && layer.isCompressible();
    }
    
    @Override
    public int getCompressionPriority() {
        return COMPRESSION_PRIORITY;
    }
    
    @Override
    public String getName() {
        return STRATEGY_NAME;
    }
    
    @Override
    public String getDescription() {
        return "智能组合压缩策略：根据内容类型自动选择和组合最适合的压缩策略";
    }
    
    /**
     * 内容类型枚举
     */
    private enum ContentType {
        CODE("代码"),
        DIALOGUE("对话"),
        LOG("日志"),
        STRUCTURED_DATA("结构化数据"),
        PLAIN_TEXT("普通文本");
        
        private final String description;
        
        ContentType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
         * 内容段类
         */
        private record ContentSegment(String content, ContentType type) {
    }

    /**
         * 压缩尝试记录
         */
        private record CompressionAttempt(String strategyName, CompressionResult result) {
    }
    
    /**
     * 策略性能统计
     */
    private static class StrategyPerformance {
        private final Map<String, Integer> successCount = new HashMap<>();
        private final Map<String, Long> totalCompressionTime = new HashMap<>();
        private final Map<String, Double> averageCompressionRatio = new HashMap<>();
        private long lastUpdateTime = System.currentTimeMillis();
        
        void recordResult(CompressionResult result) {
            String strategy = result.getStrategyName();
            if (result.isSuccess()) {
                successCount.merge(strategy, 1, Integer::sum);
                totalCompressionTime.merge(strategy, result.getDurationMillis(), Long::sum);
                
                double currentAvg = averageCompressionRatio.getOrDefault(strategy, 0.0);
                int count = successCount.get(strategy);
                double newAvg = (currentAvg * (count - 1) + result.getCompressionRatio()) / count;
                averageCompressionRatio.put(strategy, newAvg);
            }
            lastUpdateTime = System.currentTimeMillis();
        }
        
        String getBestStrategy() {
            return successCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
        }
    }
}