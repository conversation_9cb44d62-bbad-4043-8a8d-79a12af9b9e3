package cn.genn.graph.context.compression.impl;

import cn.genn.graph.context.compression.CompressionLevel;
import cn.genn.graph.context.compression.CompressionResult;
import cn.genn.graph.context.compression.CompressionStrategy;
import cn.genn.graph.context.layer.ContextLayer;
import cn.genn.graph.context.token.TokenCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 摘要压缩策略
 * 对内容进行智能摘要，提取关键信息
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SummaryCompressionStrategy implements CompressionStrategy {
    
    private static final String STRATEGY_NAME = "SummaryCompression";
    private static final int COMPRESSION_PRIORITY = 10;
    
    /**
     * 句子分割模式
     */
    private static final Pattern SENTENCE_PATTERN = Pattern.compile(
        "[。！？.!?]+[\\s]*|[\\n]{2,}"
    );
    
    /**
     * 关键词模式（用于识别重要内容）
     */
    private static final Pattern KEYWORD_PATTERN = Pattern.compile(
        "(重要|关键|核心|主要|必须|需要|应该|结果|结论|总结|问题|解决|方案|建议|注意|警告|错误|成功|失败)"
    );
    
    /**
     * 对话模式（用于识别对话内容）
     */
    private static final Pattern DIALOGUE_PATTERN = Pattern.compile(
        "^(User|用户|Assistant|助手|System|系统|Human|AI)[:：]\\s*(.+)$",
        Pattern.MULTILINE
    );
    
    /**
     * 缓存最近的压缩结果
     */
    private final Map<String, CompressionResult> cache = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 100;
    
    @Autowired
    private TokenCalculator tokenCalculator;
    
    @Override
    public CompressionResult compress(String content, long targetTokens) {
        Instant startTime = Instant.now();
        
        try {
            // 检查输入
            if (content == null || content.isEmpty()) {
                return createEmptyResult(startTime);
            }
            
            // 计算原始Token数
            long originalTokenCount = tokenCalculator.calculate(content);
            
            // 如果原始内容已经满足要求，直接返回
            if (originalTokenCount <= targetTokens) {
                return createUncompressedResult(content, originalTokenCount, startTime);
            }
            
            // 检查缓存
            String cacheKey = generateCacheKey(content, targetTokens);
            CompressionResult cachedResult = cache.get(cacheKey);
            if (cachedResult != null) {
                log.debug("Using cached compression result for key: {}", cacheKey);
                return cachedResult;
            }
            
            // 根据压缩比例确定压缩级别
            double compressionRatio = (double) targetTokens / originalTokenCount;
            CompressionLevel level = determineCompressionLevel(compressionRatio);
            
            // 执行压缩
            String compressedContent = performCompression(content, targetTokens, level);
            
            // 创建压缩结果
            CompressionResult result = buildCompressionResult(
                compressedContent, 
                originalTokenCount, 
                level, 
                startTime
            );
            
            // 缓存结果
            cacheResult(cacheKey, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("Compression failed", e);
            return CompressionResult.failure(
                "Compression failed: " + e.getMessage(), 
                content, 
                tokenCalculator.calculate(content)
            );
        }
    }
    
    /**
     * 执行实际的压缩操作
     */
    private String performCompression(String content, long targetTokens, CompressionLevel level) {
        // 检测内容类型
        if (isDialogueContent(content)) {
            return compressDialogue(content, targetTokens, level);
        } else {
            return compressNormalText(content, targetTokens, level);
        }
    }
    
    /**
     * 压缩普通文本
     */
    private String compressNormalText(String content, long targetTokens, CompressionLevel level) {
        // 分割句子
        List<String> sentences = splitIntoSentences(content);
        
        // 计算每个句子的重要性分数
        Map<String, Double> sentenceScores = calculateSentenceScores(sentences);
        
        // 根据压缩级别选择句子
        List<String> selectedSentences = selectSentencesByLevel(
            sentences, 
            sentenceScores, 
            targetTokens, 
            level
        );
        
        // 生成摘要
        return generateSummary(selectedSentences, level);
    }
    
    /**
     * 压缩对话内容
     */
    private String compressDialogue(String content, long targetTokens, CompressionLevel level) {
        List<DialogueEntry> entries = parseDialogue(content);
        
        if (entries.isEmpty()) {
            return compressNormalText(content, targetTokens, level);
        }
        
        // 根据压缩级别选择保留的对话
        List<DialogueEntry> selectedEntries = selectDialogueEntries(entries, targetTokens, level);
        
        // 重建对话内容
        return rebuildDialogue(selectedEntries, level);
    }
    
    /**
     * 分割句子
     */
    private List<String> splitIntoSentences(String content) {
        List<String> sentences = new ArrayList<>();
        Matcher matcher = SENTENCE_PATTERN.matcher(content);
        int lastEnd = 0;
        
        while (matcher.find()) {
            String sentence = content.substring(lastEnd, matcher.end()).trim();
            if (!sentence.isEmpty()) {
                sentences.add(sentence);
            }
            lastEnd = matcher.end();
        }
        
        // 添加最后一个句子
        if (lastEnd < content.length()) {
            String lastSentence = content.substring(lastEnd).trim();
            if (!lastSentence.isEmpty()) {
                sentences.add(lastSentence);
            }
        }
        
        return sentences;
    }
    
    /**
     * 计算句子重要性分数
     */
    private Map<String, Double> calculateSentenceScores(List<String> sentences) {
        Map<String, Double> scores = new HashMap<>();
        
        for (int i = 0; i < sentences.size(); i++) {
            String sentence = sentences.get(i);
            double score = 0.0;
            
            // 位置权重（开头和结尾的句子更重要）
            if (i == 0 || i == sentences.size() - 1) {
                score += 2.0;
            } else if (i < 3 || i > sentences.size() - 4) {
                score += 1.0;
            }
            
            // 关键词权重
            Matcher keywordMatcher = KEYWORD_PATTERN.matcher(sentence);
            while (keywordMatcher.find()) {
                score += 1.5;
            }
            
            // 长度权重（适中长度的句子更重要）
            int length = sentence.length();
            if (length > 20 && length < 200) {
                score += 0.5;
            }
            
            // 包含数字或特殊符号的权重
            if (sentence.matches(".*\\d+.*")) {
                score += 0.5;
            }
            
            scores.put(sentence, score);
        }
        
        return scores;
    }
    
    /**
     * 根据压缩级别选择句子
     */
    private List<String> selectSentencesByLevel(
            List<String> sentences,
            Map<String, Double> scores,
            long targetTokens,
            CompressionLevel level) {
        
        // 根据分数排序
        List<String> sortedSentences = sentences.stream()
            .sorted((s1, s2) -> Double.compare(
                scores.getOrDefault(s2, 0.0),
                scores.getOrDefault(s1, 0.0)
            ))
            .collect(Collectors.toList());
        
        // 根据压缩级别确定保留比例
        double retentionRatio = level.getRetentionRatio();
        int maxSentences = Math.max(1, (int)(sentences.size() * retentionRatio));
        
        // 选择句子直到达到目标Token数
        List<String> selected = new ArrayList<>();
        long currentTokens = 0;
        
        for (int i = 0; i < Math.min(maxSentences, sortedSentences.size()); i++) {
            String sentence = sortedSentences.get(i);
            long sentenceTokens = tokenCalculator.calculate(sentence);
            
            if (currentTokens + sentenceTokens <= targetTokens) {
                selected.add(sentence);
                currentTokens += sentenceTokens;
            } else if (selected.isEmpty()) {
                // 至少保留一个句子
                String truncated = truncateSentence(sentence, targetTokens);
                selected.add(truncated);
                break;
            } else {
                break;
            }
        }
        
        // 按原始顺序重新排列
        return sentences.stream()
            .filter(selected::contains)
            .collect(Collectors.toList());
    }
    
    /**
     * 生成摘要
     */
    private String generateSummary(List<String> sentences, CompressionLevel level) {
        if (sentences.isEmpty()) {
            return "";
        }
        
        StringBuilder summary = new StringBuilder();
        
        // 根据压缩级别添加摘要标记
        if (level.isSevereCompression()) {
            summary.append("[高度压缩摘要]\n");
        } else if (level == CompressionLevel.MODERATE) {
            summary.append("[摘要]\n");
        }
        
        // 连接句子
        for (int i = 0; i < sentences.size(); i++) {
            if (i > 0) {
                summary.append(" ");
            }
            summary.append(sentences.get(i));
        }
        
        return summary.toString();
    }
    
    /**
     * 检测是否为对话内容
     */
    private boolean isDialogueContent(String content) {
        Matcher matcher = DIALOGUE_PATTERN.matcher(content);
        int dialogueCount = 0;
        while (matcher.find()) {
            dialogueCount++;
            if (dialogueCount >= 2) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 解析对话内容
     */
    private List<DialogueEntry> parseDialogue(String content) {
        List<DialogueEntry> entries = new ArrayList<>();
        Matcher matcher = DIALOGUE_PATTERN.matcher(content);
        
        while (matcher.find()) {
            String role = matcher.group(1);
            String message = matcher.group(2);
            entries.add(new DialogueEntry(role, message));
        }
        
        return entries;
    }
    
    /**
     * 选择对话条目
     */
    private List<DialogueEntry> selectDialogueEntries(
            List<DialogueEntry> entries,
            long targetTokens,
            CompressionLevel level) {
        
        // 根据压缩级别确定保留策略
        if (level == CompressionLevel.EXTREME) {
            // 极限压缩：只保留最重要的对话
            return selectMostImportantDialogues(entries, targetTokens);
        } else if (level.isSevereCompression()) {
            // 重度压缩：保留关键对话和摘要
            return selectKeyDialogues(entries, targetTokens);
        } else {
            // 轻度/中度压缩：保留最近的对话
            return selectRecentDialogues(entries, targetTokens, level);
        }
    }
    
    /**
     * 选择最重要的对话
     */
    private List<DialogueEntry> selectMostImportantDialogues(
            List<DialogueEntry> entries,
            long targetTokens) {
        
        List<DialogueEntry> selected = new ArrayList<>();
        long currentTokens = 0;
        
        // 优先保留包含关键词的对话
        for (DialogueEntry entry : entries) {
            if (KEYWORD_PATTERN.matcher(entry.message).find()) {
                long entryTokens = tokenCalculator.calculate(entry.toString());
                if (currentTokens + entryTokens <= targetTokens) {
                    selected.add(entry);
                    currentTokens += entryTokens;
                }
            }
        }
        
        // 如果还有空间，添加第一个和最后一个对话
        if (selected.isEmpty() && !entries.isEmpty()) {
            selected.add(entries.get(0));
            if (entries.size() > 1) {
                selected.add(entries.get(entries.size() - 1));
            }
        }
        
        return selected;
    }
    
    /**
     * 选择关键对话
     */
    private List<DialogueEntry> selectKeyDialogues(
            List<DialogueEntry> entries,
            long targetTokens) {
        
        List<DialogueEntry> selected = new ArrayList<>();
        long currentTokens = 0;
        
        // 保留前两个和后两个对话
        int keepFromStart = Math.min(2, entries.size());
        int keepFromEnd = Math.min(2, Math.max(0, entries.size() - keepFromStart));
        
        for (int i = 0; i < keepFromStart; i++) {
            DialogueEntry entry = entries.get(i);
            long entryTokens = tokenCalculator.calculate(entry.toString());
            if (currentTokens + entryTokens <= targetTokens) {
                selected.add(entry);
                currentTokens += entryTokens;
            }
        }
        
        for (int i = entries.size() - keepFromEnd; i < entries.size(); i++) {
            DialogueEntry entry = entries.get(i);
            long entryTokens = tokenCalculator.calculate(entry.toString());
            if (currentTokens + entryTokens <= targetTokens) {
                selected.add(entry);
                currentTokens += entryTokens;
            }
        }
        
        return selected;
    }
    
    /**
     * 选择最近的对话
     */
    private List<DialogueEntry> selectRecentDialogues(
            List<DialogueEntry> entries,
            long targetTokens,
            CompressionLevel level) {
        
        double retentionRatio = level.getRetentionRatio();
        int keepCount = Math.max(1, (int)(entries.size() * retentionRatio));
        
        List<DialogueEntry> selected = new ArrayList<>();
        long currentTokens = 0;
        
        // 从最后开始选择
        for (int i = Math.max(0, entries.size() - keepCount); i < entries.size(); i++) {
            DialogueEntry entry = entries.get(i);
            long entryTokens = tokenCalculator.calculate(entry.toString());
            
            if (currentTokens + entryTokens <= targetTokens) {
                selected.add(entry);
                currentTokens += entryTokens;
            } else {
                // 压缩当前条目
                String compressed = compressDialogueEntry(entry, targetTokens - currentTokens);
                selected.add(new DialogueEntry(entry.role, compressed));
                break;
            }
        }
        
        return selected;
    }
    
    /**
     * 重建对话内容
     */
    private String rebuildDialogue(List<DialogueEntry> entries, CompressionLevel level) {
        StringBuilder builder = new StringBuilder();
        
        if (level.isSevereCompression() && entries.size() > 0) {
            builder.append("[对话摘要 - 保留了")
                   .append(entries.size())
                   .append("条关键对话]\n\n");
        }
        
        for (DialogueEntry entry : entries) {
            builder.append(entry.role)
                   .append(": ")
                   .append(entry.message)
                   .append("\n\n");
        }
        
        return builder.toString().trim();
    }
    
    /**
     * 压缩单个对话条目
     */
    private String compressDialogueEntry(DialogueEntry entry, long maxTokens) {
        String message = entry.message;
        long currentTokens = tokenCalculator.calculate(message);
        
        if (currentTokens <= maxTokens) {
            return message;
        }
        
        // 提取关键句子
        List<String> sentences = splitIntoSentences(message);
        if (sentences.isEmpty()) {
            return truncateSentence(message, maxTokens);
        }
        
        Map<String, Double> scores = calculateSentenceScores(sentences);
        List<String> selected = selectSentencesByLevel(
            sentences, 
            scores, 
            maxTokens, 
            CompressionLevel.HEAVY
        );
        
        if (selected.isEmpty()) {
            return truncateSentence(sentences.get(0), maxTokens);
        }
        
        return String.join(" ", selected);
    }
    
    /**
     * 截断句子
     */
    private String truncateSentence(String sentence, long maxTokens) {
        long currentTokens = tokenCalculator.calculate(sentence);
        if (currentTokens <= maxTokens) {
            return sentence;
        }
        
        // 二分查找合适的截断点
        int left = 0;
        int right = sentence.length();
        String result = "";
        
        while (left < right) {
            int mid = (left + right) / 2;
            String truncated = sentence.substring(0, mid);
            long tokens = tokenCalculator.calculate(truncated);
            
            if (tokens <= maxTokens) {
                result = truncated;
                left = mid + 1;
            } else {
                right = mid;
            }
        }
        
        // 添加省略标记
        if (!result.isEmpty() && result.length() < sentence.length()) {
            result += "...";
        }
        
        return result;
    }
    
    /**
     * 确定压缩级别
     */
    private CompressionLevel determineCompressionLevel(double compressionRatio) {
        if (compressionRatio >= 0.8) {
            return CompressionLevel.LIGHT;
        } else if (compressionRatio >= 0.6) {
            return CompressionLevel.MODERATE;
        } else if (compressionRatio >= 0.4) {
            return CompressionLevel.HEAVY;
        } else {
            return CompressionLevel.EXTREME;
        }
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String content, long targetTokens) {
        int contentHash = content.hashCode();
        return String.format("%s_%d_%d", STRATEGY_NAME, contentHash, targetTokens);
    }
    
    /**
     * 缓存结果
     */
    private void cacheResult(String key, CompressionResult result) {
        // 限制缓存大小
        if (cache.size() >= MAX_CACHE_SIZE) {
            // 简单的LRU策略：移除最早的条目
            Iterator<String> iterator = cache.keySet().iterator();
            if (iterator.hasNext()) {
                iterator.next();
                iterator.remove();
            }
        }
        cache.put(key, result);
    }
    
    /**
     * 创建空结果
     */
    private CompressionResult createEmptyResult(Instant startTime) {
        CompressionResult result = CompressionResult.builder()
            .content("")
            .tokenCount(0)
            .originalTokenCount(0)
            .compressionRatio(1.0)
            .compressionLevel(CompressionLevel.NONE)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        result.calculateDuration();
        return result;
    }
    
    /**
     * 创建未压缩结果
     */
    private CompressionResult createUncompressedResult(String content, long tokenCount, Instant startTime) {
        CompressionResult result = CompressionResult.builder()
            .content(content)
            .tokenCount(tokenCount)
            .originalTokenCount(tokenCount)
            .compressionRatio(1.0)
            .compressionLevel(CompressionLevel.NONE)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        result.calculateDuration();
        result.addMetadata("reason", "Content already within target tokens");
        return result;
    }
    
    /**
     * 构建压缩结果
     */
    private CompressionResult buildCompressionResult(
            String compressedContent,
            long originalTokenCount,
            CompressionLevel level,
            Instant startTime) {
        
        long compressedTokenCount = tokenCalculator.calculate(compressedContent);
        
        CompressionResult result = CompressionResult.builder()
            .content(compressedContent)
            .tokenCount(compressedTokenCount)
            .originalTokenCount(originalTokenCount)
            .compressionLevel(level)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        
        result.calculateCompressionRatio();
        result.calculateDuration();
        
        // 添加统计信息
        result.addMetadata("compressionLevel", level.getDescription());
        result.addMetadata("tokenReduction", result.getTokenReduction());
        result.addMetadata("reductionPercentage", String.format("%.1f%%", result.getTokenReductionPercentage()));
        
        log.info("Compression completed: {}", result.getSummary());
        
        return result;
    }
    
    @Override
    public boolean canCompress(ContextLayer layer) {
        // 摘要压缩策略可以压缩所有可压缩的层
        return layer != null && layer.isCompressible();
    }
    
    @Override
    public int getCompressionPriority() {
        return COMPRESSION_PRIORITY;
    }
    
    @Override
    public String getName() {
        return STRATEGY_NAME;
    }
    
    @Override
    public String getDescription() {
        return "智能摘要压缩策略：通过提取关键信息和生成摘要来压缩内容";
    }
    
    /**
     * 对话条目内部类
     */
    private static class DialogueEntry {
        final String role;
        final String message;
        
        DialogueEntry(String role, String message) {
            this.role = role;
            this.message = message;
        }
        
        @Override
        public String toString() {
            return role + ": " + message;
        }
    }
}