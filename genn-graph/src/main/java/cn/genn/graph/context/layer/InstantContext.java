package cn.genn.graph.context.layer;

import cn.genn.graph.tools.internal.HumanTool;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.ToolResponseMessage;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;

/**
 * 瞬时状态信息
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class InstantContext {

    /**
     * 用户自定义全部工具集
     */
    private List<ToolCallback> allToolCallbacks;

    /**
     * LLM工具调度的结果
     */
    private AssistantMessage toolDispatchResult;

    /**
     * 本次工具执行的结果
     */
    private ToolResponseMessage toolExecutionResponse;

    /**
     * 人类交互请求，用于暂停图并等待用户输入
     */
    private HumanTool.HumanToolResult humanToolResult;

    /**
     * 最终的报告/答案
     */
    private Object finalResult;

    /**
     * 图执行过程中的错误信息
     */
    private String errorMessage;

    /**
     * 标记图是否应该终止
     */
    @Builder.Default
    private boolean shouldTerminate = false;
}
