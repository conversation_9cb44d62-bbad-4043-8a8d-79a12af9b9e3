package cn.genn.graph.context;

import cn.genn.graph.context.token.TokenBudget;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 上下文配置类
 * 管理三层上下文系统的配置参数
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContextConfiguration {
    
    /**
     * 模型类型
     */
    @Builder.Default
    private String modelType = "gpt-4";
    
    /**
     * 最大Token限制
     */
    @Builder.Default
    private long maxTokenLimit = 8192;
    
    /**
     * 响应预留Token数
     */
    @Builder.Default
    private long reservedTokens = 1000;
    
    /**
     * 是否启用自动压缩
     */
    @Builder.Default
    private boolean enableAutoCompression = true;
    
    /**
     * 压缩触发阈值（Token使用率）
     */
    @Builder.Default
    private double compressionThreshold = 0.8;
    
    /**
     * 是否启用缓存
     */
    @Builder.Default
    private boolean enableCache = true;
    
    /**
     * 缓存过期时间（秒）
     */
    @Builder.Default
    private long cacheExpireSeconds = 300;
    
    /**
     * 是否启用异步处理
     */
    @Builder.Default
    private boolean enableAsync = false;
    
    /**
     * 默认Prompt模板名称
     */
    @Builder.Default
    private String defaultTemplateName = "default_xml";
    
    /**
     * Token分配策略
     */
    @Builder.Default
    private TokenAllocationStrategy allocationStrategy = TokenAllocationStrategy.BALANCED;
    
    /**
     * 压缩策略优先级
     */
    @Builder.Default
    private CompressionPriority compressionPriority = CompressionPriority.QUALITY_FIRST;
    
    /**
     * 是否启用调试模式
     */
    @Builder.Default
    private boolean debugMode = false;
    
    /**
     * 自定义配置参数
     */
    @Builder.Default
    private Map<String, Object> customProperties = new HashMap<>();
    
    /**
     * Token分配策略枚举
     */
    public enum TokenAllocationStrategy {
        /**
         * 焦点优先 - 更多Token分配给焦点层
         */
        FOCUS_FIRST("focus_first", 0.5, 0.3, 0.2),
        
        /**
         * 平衡分配 - 三层均衡分配
         */
        BALANCED("balanced", 0.35, 0.35, 0.3),
        
        /**
         * 历史优先 - 更多Token分配给战略层
         */
        HISTORY_FIRST("history_first", 0.3, 0.3, 0.4),
        
        /**
         * 任务优先 - 更多Token分配给全景层
         */
        TASK_FIRST("task_first", 0.3, 0.45, 0.25);
        
        private final String name;
        private final double focusRatio;
        private final double panoramicRatio;
        private final double strategicRatio;
        
        TokenAllocationStrategy(String name, double focusRatio, double panoramicRatio, double strategicRatio) {
            this.name = name;
            this.focusRatio = focusRatio;
            this.panoramicRatio = panoramicRatio;
            this.strategicRatio = strategicRatio;
        }
        
        public String getName() {
            return name;
        }
        
        public double getFocusRatio() {
            return focusRatio;
        }
        
        public double getPanoramicRatio() {
            return panoramicRatio;
        }
        
        public double getStrategicRatio() {
            return strategicRatio;
        }
        
        public TokenBudget.TokenAllocationRatio toAllocationRatio() {
            return TokenBudget.TokenAllocationRatio.builder()
                    .focusRatio(focusRatio)
                    .panoramicRatio(panoramicRatio)
                    .strategicRatio(strategicRatio)
                    .build();
        }
    }
    
    /**
     * 压缩优先级枚举
     */
    public enum CompressionPriority {
        /**
         * 质量优先 - 尽可能保留内容质量
         */
        QUALITY_FIRST("quality_first"),
        
        /**
         * 速度优先 - 快速压缩
         */
        SPEED_FIRST("speed_first"),
        
        /**
         * 平衡模式 - 质量和速度平衡
         */
        BALANCED("balanced");
        
        private final String name;
        
        CompressionPriority(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
    
    /**
     * 创建Token预算
     */
    public TokenBudget createTokenBudget() {
        return TokenBudget.builder()
                .maxTokens(maxTokenLimit)
                .reservedTokens(reservedTokens)
                .allocationRatio(allocationStrategy.toAllocationRatio())
                .build();
    }
    
    /**
     * 获取可用Token数
     */
    public long getAvailableTokens() {
        return Math.max(0, maxTokenLimit - reservedTokens);
    }
    
    /**
     * 是否需要压缩
     */
    public boolean needsCompression(long currentTokens) {
        if (!enableAutoCompression) {
            return false;
        }
        double usageRatio = (double) currentTokens / getAvailableTokens();
        return usageRatio > compressionThreshold;
    }
    
    /**
     * 添加自定义属性
     */
    public void addCustomProperty(String key, Object value) {
        customProperties.put(key, value);
    }
    
    /**
     * 获取自定义属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getCustomProperty(String key, Class<T> type) {
        Object value = customProperties.get(key);
        if (type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 创建默认配置
     */
    public static ContextConfiguration createDefault() {
        return ContextConfiguration.builder().build();
    }
    
    /**
     * 创建GPT-3.5配置
     */
    public static ContextConfiguration createForGPT35() {
        return ContextConfiguration.builder()
                .modelType("gpt-3.5-turbo")
                .maxTokenLimit(4096)
                .reservedTokens(500)
                .allocationStrategy(TokenAllocationStrategy.FOCUS_FIRST)
                .build();
    }
    
    /**
     * 创建GPT-4配置
     */
    public static ContextConfiguration createForGPT4() {
        return ContextConfiguration.builder()
                .modelType("gpt-4")
                .maxTokenLimit(8192)
                .reservedTokens(1000)
                .allocationStrategy(TokenAllocationStrategy.BALANCED)
                .build();
    }
    
    /**
     * 创建Claude配置
     */
    public static ContextConfiguration createForClaude() {
        return ContextConfiguration.builder()
                .modelType("claude-3")
                .maxTokenLimit(100000)
                .reservedTokens(2000)
                .allocationStrategy(TokenAllocationStrategy.BALANCED)
                .enableAutoCompression(false)  // Claude有大容量，可以不压缩
                .build();
    }
    
    /**
     * 验证配置有效性
     */
    public boolean isValid() {
        return maxTokenLimit > 0 &&
               reservedTokens >= 0 &&
               reservedTokens < maxTokenLimit &&
               compressionThreshold > 0 && compressionThreshold <= 1.0 &&
               cacheExpireSeconds > 0;
    }
    
    /**
     * 获取配置摘要
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("ContextInput Configuration:\n");
        sb.append("  Model: ").append(modelType).append("\n");
        sb.append("  Max Tokens: ").append(maxTokenLimit).append("\n");
        sb.append("  Reserved: ").append(reservedTokens).append("\n");
        sb.append("  Available: ").append(getAvailableTokens()).append("\n");
        sb.append("  Auto Compression: ").append(enableAutoCompression);
        if (enableAutoCompression) {
            sb.append(" (threshold: ").append(String.format("%.0f%%", compressionThreshold * 100)).append(")");
        }
        sb.append("\n");
        sb.append("  Allocation Strategy: ").append(allocationStrategy.getName()).append("\n");
        sb.append("  Compression Priority: ").append(compressionPriority.getName()).append("\n");
        sb.append("  Cache: ").append(enableCache);
        if (enableCache) {
            sb.append(" (expire: ").append(cacheExpireSeconds).append("s)");
        }
        sb.append("\n");
        sb.append("  Async: ").append(enableAsync).append("\n");
        sb.append("  Debug: ").append(debugMode).append("\n");
        
        if (!customProperties.isEmpty()) {
            sb.append("  Custom Properties: ").append(customProperties.size()).append(" items\n");
        }
        
        return sb.toString();
    }
}