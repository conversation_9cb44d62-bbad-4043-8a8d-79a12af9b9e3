package cn.genn.graph.context.prompt.impl;

import cn.genn.graph.context.compression.CompressionLevel;
import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.context.layer.PanoramicLayer;
import cn.genn.graph.context.layer.StrategicLayer;
import cn.genn.graph.context.layer.XMLTagUtils;
import cn.genn.graph.context.layer.LayerPriority;
import cn.genn.graph.context.prompt.PromptBuilder;
import cn.genn.graph.context.prompt.PromptResult;
import cn.genn.graph.context.token.TokenBudget;
import cn.genn.graph.context.token.TokenCalculator;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;

import static cn.genn.graph.context.layer.XMLTagUtils.wrapContentWithNewLine;
import static cn.genn.graph.context.layer.XMLTagUtils.wrapContentWithTwoAttrs;

/**
 * XML格式Prompt构造器
 * 使用XML标签组织三层上下文内容
 * 
 * <AUTHOR>
 */
@Slf4j
public class XMLPromptBuilder implements PromptBuilder {
    
    private final TokenCalculator tokenCalculator;
    
    public XMLPromptBuilder(TokenCalculator tokenCalculator) {
        this.tokenCalculator = tokenCalculator;
    }
    
    @Override
    public PromptResult build(
            String systemPrompt,
            FocusLayer focusLayer,
            PanoramicLayer panoramicLayer,
            StrategicLayer strategicLayer,
            TokenBudget tokenBudget) {
        
        Instant startTime = Instant.now();
        PromptResult result = new PromptResult();
        result.setTimestamp(startTime);
        
        try {
            // 1. 提取系统提示词（来自焦点层）
            result.setSystemPrompt(systemPrompt);
            
            // 2. 构建用户消息
            String userMessage = buildUserMessage(focusLayer, panoramicLayer, strategicLayer, tokenBudget, result);
            result.setUserMessage(userMessage);
            
            // 3. 计算总Token数
            long totalTokens = tokenCalculator.calculate(systemPrompt) + tokenCalculator.calculate(userMessage);
            result.setTotalTokenCount(totalTokens);
            
            // 4. 构建完整Prompt
            result.buildFullPrompt();
            
            // 5. 验证Token预算
            if (!validateBudget(result.getFocusTokenCount(), result.getPanoramicTokenCount(), 
                              result.getStrategicTokenCount(), tokenBudget)) {
                log.warn("Token budget exceeded: {} > {}", totalTokens, tokenBudget.getAvailableTokens());
                result.addMetadata("budgetExceeded", true);
            }
            
            result.setSuccess(true);
            log.debug("XML Prompt built successfully: {} tokens", totalTokens);
            
        } catch (Exception e) {
            log.error("Failed to build XML prompt", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        } finally {
            long buildTime = Instant.now().toEpochMilli() - startTime.toEpochMilli();
            result.setBuildTimeMillis(buildTime);
        }
        
        return result;
    }
    
    /**
     * 构建用户消息
     */
    private String buildUserMessage(
            FocusLayer focusLayer,
            PanoramicLayer panoramicLayer,
            StrategicLayer strategicLayer,
            TokenBudget tokenBudget,
            PromptResult result) {
        
        StringBuilder userMessage = new StringBuilder();
        
        // 1. 焦点层内容（不压缩）
        String focusContent = buildFocusContent(focusLayer, result);
        if (!focusContent.isEmpty()) {
            userMessage.append(focusContent);
        }
        
        // 2. 全景层内容（可能压缩）
        String panoramicContent = buildPanoramicContent(panoramicLayer, result);
        if (!panoramicContent.isEmpty()) {
            userMessage.append(panoramicContent);
        }
        
        // 3. 战略层内容（可能压缩）
        String strategicContent = buildStrategicContent(strategicLayer, result);
        if (!strategicContent.isEmpty()) {
            userMessage.append(strategicContent);
        }

        // 当前用户问题放在最后，带显式优先级和重要性
        userMessage.append(wrapContentWithTwoAttrs("cur_question", "priority", 
                "high", "importance", "primary", focusLayer.getCurrentUserQuestion())).append("\n");
        
        return userMessage.toString();
    }
    
    /**
     * 构建焦点层内容
     */
    private String buildFocusContent(FocusLayer focusLayer, PromptResult result) {
        String content = focusLayer.getContent();
        
        if (content == null || content.trim().isEmpty()) {
            return "";
        }
        
        // 使用统一上下文容器，显式声明优先级和名称
        content = XMLTagUtils.wrapContext("focus_context", focusLayer.getPriority(), content);
        
        // 计算Token数
        long tokens = tokenCalculator.calculate(content);
        result.setFocusTokenCount(tokens);
        
        // 保存到结果
        result.setLayerContent("focus", content);
        result.setCompressionLevel("focus", CompressionLevel.NONE);
        
        return content;
    }
    
    /**
     * 构建全景层内容
     */
    private String buildPanoramicContent(PanoramicLayer panoramicLayer, PromptResult result) {
        String content = panoramicLayer.getContent();
        
        if (content == null || content.trim().isEmpty()) {
            result.setPanoramicTokenCount(0);
            return "";
        }
        
        // 使用统一上下文容器，显式声明优先级和名称
        content = XMLTagUtils.wrapContext("panoramic_context", panoramicLayer.getPriority(), content);
        
        // 计算Token数
        long tokens = tokenCalculator.calculate(content);
        result.setPanoramicTokenCount(tokens);
        
        // 获取压缩级别
        CompressionLevel compressionLevel = panoramicLayer.getCompressionLevel();
        
        // 保存到结果
        result.setLayerContent("panoramic", content);
        result.setCompressionLevel("panoramic", compressionLevel);
        
        return content;
    }
    
    /**
     * 构建战略层内容
     */
    private String buildStrategicContent(StrategicLayer strategicLayer, PromptResult result) {
        String content = strategicLayer.getContent();
        
        if (content == null || content.trim().isEmpty()) {
            result.setStrategicTokenCount(0);
            return "";
        }
        
        // 使用统一上下文容器，显式声明优先级和名称
        content = XMLTagUtils.wrapContext("strategic_context", strategicLayer.getPriority(), content);
        
        // 计算Token数
        long tokens = tokenCalculator.calculate(content);
        result.setStrategicTokenCount(tokens);
        
        // 获取压缩级别
        CompressionLevel compressionLevel = strategicLayer.getCompressionLevel();
        
        // 保存到结果
        result.setLayerContent("strategic", content);
        result.setCompressionLevel("strategic", compressionLevel);
        
        return content;
    }
    
    /**
     * 验证Token预算
     */
    @Override
    public boolean validateBudget(long focusTokens, long panoramicTokens, long strategicTokens, TokenBudget tokenBudget) {
        long totalTokens = focusTokens + panoramicTokens + strategicTokens;
        return totalTokens <= tokenBudget.getAvailableTokens();
    }
    
    @Override
    public String getName() {
        return "XMLPromptBuilder";
    }
    
    @Override
    public String getDescription() {
        return "Builds prompts using XML format to structure three-layer context";
    }
    
}