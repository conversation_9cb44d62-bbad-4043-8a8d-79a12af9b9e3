package cn.genn.graph.context.compression.impl;

import cn.genn.graph.context.compression.CompressionLevel;
import cn.genn.graph.context.compression.CompressionResult;
import cn.genn.graph.context.compression.CompressionStrategy;
import cn.genn.graph.context.layer.ContextLayer;
import cn.genn.graph.context.token.TokenCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 滑动窗口压缩策略
 * 保留最近的N条记录，支持基于时间戳的窗口滑动
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SlidingWindowCompressionStrategy implements CompressionStrategy {
    
    private static final String STRATEGY_NAME = "SlidingWindowCompression";
    private static final int COMPRESSION_PRIORITY = 30;
    
    /**
     * 默认窗口大小（记录数）
     */
    private static final int DEFAULT_WINDOW_SIZE = 10;
    
    /**
     * 默认时间窗口（分钟）
     */
    private static final int DEFAULT_TIME_WINDOW_MINUTES = 30;
    
    /**
     * 时间戳模式
     */
    private static final Pattern TIMESTAMP_PATTERN = Pattern.compile(
        "\\[(\\d{4}-\\d{2}-\\d{2}[T\\s]\\d{2}:\\d{2}:\\d{2}(?:\\.\\d{3})?(?:Z|[+-]\\d{2}:\\d{2})?)\\]|" +
        "\\b(\\d{4}-\\d{2}-\\d{2}[T\\s]\\d{2}:\\d{2}:\\d{2})\\b|" +
        "\\b(\\d{2}:\\d{2}:\\d{2})\\b"
    );
    
    /**
     * 对话轮次模式
     */
    private static final Pattern TURN_PATTERN = Pattern.compile(
        "^(User|用户|Human|Assistant|助手|AI|System|系统)[:：]\\s*",
        Pattern.MULTILINE | Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 消息分隔模式
     */
    private static final Pattern MESSAGE_SEPARATOR_PATTERN = Pattern.compile(
        "\n{2,}|^---+$|^===+$|^\\*{3,}$",
        Pattern.MULTILINE
    );
    
    /**
     * 窗口配置
     */
    private final Map<String, WindowConfig> windowConfigs = new ConcurrentHashMap<>();
    
    /**
     * 缓存
     */
    private final Map<String, CompressionResult> cache = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 50;
    
    /**
     * 历史记录队列（用于维护滑动窗口）
     */
    private final ConcurrentLinkedDeque<WindowRecord> historyQueue = new ConcurrentLinkedDeque<>();
    
    @Autowired
    private TokenCalculator tokenCalculator;
    
    /**
     * 构造函数
     */
    public SlidingWindowCompressionStrategy() {
        initializeDefaultConfigs();
    }
    
    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfigs() {
        // 默认配置
        windowConfigs.put("default", new WindowConfig(
            DEFAULT_WINDOW_SIZE,
            DEFAULT_TIME_WINDOW_MINUTES,
            WindowType.COUNT_BASED
        ));
        
        // 对话配置
        windowConfigs.put("dialogue", new WindowConfig(
            20,
            60,
            WindowType.TURN_BASED
        ));
        
        // 日志配置
        windowConfigs.put("log", new WindowConfig(
            100,
            120,
            WindowType.TIME_BASED
        ));
    }
    
    @Override
    public CompressionResult compress(String content, long targetTokens) {
        Instant startTime = Instant.now();
        
        try {
            // 检查输入
            if (content == null || content.isEmpty()) {
                return createEmptyResult(startTime);
            }
            
            // 计算原始Token数
            long originalTokenCount = tokenCalculator.calculate(content);
            
            // 如果原始内容已经满足要求，直接返回
            if (originalTokenCount <= targetTokens) {
                return createUncompressedResult(content, originalTokenCount, startTime);
            }
            
            // 检查缓存
            String cacheKey = generateCacheKey(content, targetTokens);
            CompressionResult cachedResult = cache.get(cacheKey);
            if (cachedResult != null) {
                log.debug("Using cached compression result for key: {}", cacheKey);
                return cachedResult;
            }
            
            // 根据压缩比例确定压缩级别
            double compressionRatio = (double) targetTokens / originalTokenCount;
            CompressionLevel level = determineCompressionLevel(compressionRatio);
            
            // 检测内容类型并选择合适的窗口配置
            WindowConfig config = selectWindowConfig(content, level);
            
            // 执行压缩
            String compressedContent = performCompression(content, targetTokens, level, config);
            
            // 创建压缩结果
            CompressionResult result = buildCompressionResult(
                compressedContent,
                originalTokenCount,
                level,
                config,
                startTime
            );
            
            // 缓存结果
            cacheResult(cacheKey, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("Compression failed", e);
            return CompressionResult.failure(
                "Compression failed: " + e.getMessage(),
                content,
                tokenCalculator.calculate(content)
            );
        }
    }
    
    /**
     * 选择窗口配置
     */
    private WindowConfig selectWindowConfig(String content, CompressionLevel level) {
        // 检测内容类型
        if (isDialogueContent(content)) {
            WindowConfig config = windowConfigs.get("dialogue");
            return adjustConfigByLevel(config, level);
        } else if (isLogContent(content)) {
            WindowConfig config = windowConfigs.get("log");
            return adjustConfigByLevel(config, level);
        } else {
            WindowConfig config = windowConfigs.get("default");
            return adjustConfigByLevel(config, level);
        }
    }
    
    /**
     * 根据压缩级别调整配置
     */
    private WindowConfig adjustConfigByLevel(WindowConfig baseConfig, CompressionLevel level) {
        double retentionRatio = level.getRetentionRatio();
        
        int adjustedSize = Math.max(1, (int)(baseConfig.windowSize * retentionRatio));
        int adjustedTime = Math.max(1, (int)(baseConfig.timeWindowMinutes * retentionRatio));
        
        return new WindowConfig(adjustedSize, adjustedTime, baseConfig.type);
    }
    
    /**
     * 执行压缩
     */
    private String performCompression(String content, long targetTokens, CompressionLevel level, WindowConfig config) {
        // 解析内容为记录
        List<WindowRecord> records = parseRecords(content, config.type);
        
        if (records.isEmpty()) {
            return content;
        }
        
        // 应用滑动窗口
        List<WindowRecord> windowedRecords = applyWindow(records, config, targetTokens);
        
        // 处理窗口外的内容
        String summary = "";
        if (level.isSevereCompression() && records.size() > windowedRecords.size()) {
            summary = generateSummaryForExcluded(records, windowedRecords);
        }
        
        // 重建内容
        return rebuildContent(windowedRecords, summary, level, config);
    }
    
    /**
     * 解析记录
     */
    private List<WindowRecord> parseRecords(String content, WindowType type) {
        List<WindowRecord> records = new ArrayList<>();
        
        switch (type) {
            case TURN_BASED:
                records = parseTurnBasedRecords(content);
                break;
            case TIME_BASED:
                records = parseTimeBasedRecords(content);
                break;
            case COUNT_BASED:
            default:
                records = parseCountBasedRecords(content);
                break;
        }
        
        return records;
    }
    
    /**
     * 解析基于轮次的记录（对话）
     */
    private List<WindowRecord> parseTurnBasedRecords(String content) {
        List<WindowRecord> records = new ArrayList<>();
        Matcher matcher = TURN_PATTERN.matcher(content);
        
        int lastEnd = 0;
        int turnIndex = 0;
        
        while (matcher.find()) {
            if (lastEnd > 0) {
                // 保存上一个对话轮次
                String turnContent = content.substring(lastEnd, matcher.start()).trim();
                if (!turnContent.isEmpty()) {
                    WindowRecord record = new WindowRecord(
                        turnContent,
                        turnIndex++,
                        null,
                        RecordType.DIALOGUE_TURN
                    );
                    records.add(record);
                }
            }
            lastEnd = matcher.start();
        }
        
        // 添加最后一个轮次
        if (lastEnd < content.length()) {
            String lastTurn = content.substring(lastEnd).trim();
            if (!lastTurn.isEmpty()) {
                WindowRecord record = new WindowRecord(
                    lastTurn,
                    turnIndex,
                    null,
                    RecordType.DIALOGUE_TURN
                );
                records.add(record);
            }
        }
        
        // 如果没有找到对话模式，按段落分割
        if (records.isEmpty()) {
            return parseCountBasedRecords(content);
        }
        
        return records;
    }
    
    /**
     * 解析基于时间的记录
     */
    private List<WindowRecord> parseTimeBasedRecords(String content) {
        List<WindowRecord> records = new ArrayList<>();
        
        // 按行分割
        String[] lines = content.split("\n");
        StringBuilder currentRecord = new StringBuilder();
        Instant currentTimestamp = null;
        int index = 0;
        
        for (String line : lines) {
            // 尝试提取时间戳
            Instant timestamp = extractTimestamp(line);
            
            if (timestamp != null) {
                // 如果找到新的时间戳，保存当前记录
                if (currentRecord.length() > 0) {
                    WindowRecord record = new WindowRecord(
                        currentRecord.toString().trim(),
                        index++,
                        currentTimestamp,
                        RecordType.TIMESTAMPED
                    );
                    records.add(record);
                    currentRecord = new StringBuilder();
                }
                currentTimestamp = timestamp;
            }
            
            currentRecord.append(line).append("\n");
        }
        
        // 添加最后一条记录
        if (currentRecord.length() > 0) {
            WindowRecord record = new WindowRecord(
                currentRecord.toString().trim(),
                index,
                currentTimestamp,
                RecordType.TIMESTAMPED
            );
            records.add(record);
        }
        
        // 如果没有找到时间戳，按段落分割
        if (records.isEmpty()) {
            return parseCountBasedRecords(content);
        }
        
        return records;
    }
    
    /**
     * 解析基于计数的记录（默认）
     */
    private List<WindowRecord> parseCountBasedRecords(String content) {
        List<WindowRecord> records = new ArrayList<>();
        
        // 使用分隔符分割
        String[] segments = MESSAGE_SEPARATOR_PATTERN.split(content);
        
        for (int i = 0; i < segments.length; i++) {
            String segment = segments[i].trim();
            if (!segment.isEmpty()) {
                WindowRecord record = new WindowRecord(
                    segment,
                    i,
                    null,
                    RecordType.PARAGRAPH
                );
                records.add(record);
            }
        }
        
        // 如果分割结果太少，按句子分割
        if (records.size() <= 3 && content.length() > 500) {
            records.clear();
            String[] sentences = content.split("(?<=[。！？.!?])[\\s]*");
            for (int i = 0; i < sentences.length; i++) {
                String sentence = sentences[i].trim();
                if (!sentence.isEmpty()) {
                    WindowRecord record = new WindowRecord(
                        sentence,
                        i,
                        null,
                        RecordType.SENTENCE
                    );
                    records.add(record);
                }
            }
        }
        
        return records;
    }
    
    /**
     * 提取时间戳
     */
    private Instant extractTimestamp(String text) {
        Matcher matcher = TIMESTAMP_PATTERN.matcher(text);
        if (matcher.find()) {
            String timestampStr = matcher.group(1);
            if (timestampStr == null) {
                timestampStr = matcher.group(2);
            }
            if (timestampStr == null) {
                // 只有时间，使用今天的日期
                timestampStr = LocalDateTime.now().toLocalDate() + "T" + matcher.group(3);
            }
            
            try {
                // 尝试解析不同格式的时间戳
                return parseTimestamp(timestampStr);
            } catch (Exception e) {
                log.trace("Failed to parse timestamp: {}", timestampStr);
            }
        }
        return null;
    }
    
    /**
     * 解析时间戳字符串
     */
    private Instant parseTimestamp(String timestampStr) {
        // 移除可能的方括号
        timestampStr = timestampStr.replaceAll("[\\[\\]]", "").trim();
        
        // 尝试不同的时间格式
        List<DateTimeFormatter> formatters = Arrays.asList(
            DateTimeFormatter.ISO_INSTANT,
            DateTimeFormatter.ISO_LOCAL_DATE_TIME,
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")
        );
        
        for (DateTimeFormatter formatter : formatters) {
            try {
                LocalDateTime ldt = LocalDateTime.parse(timestampStr, formatter);
                return ldt.atZone(ZoneId.systemDefault()).toInstant();
            } catch (Exception e) {
                // 继续尝试下一个格式
            }
        }
        
        // 如果都失败了，返回当前时间
        return Instant.now();
    }
    
    /**
     * 应用滑动窗口
     */
    private List<WindowRecord> applyWindow(List<WindowRecord> records, WindowConfig config, long targetTokens) {
        List<WindowRecord> result = new ArrayList<>();
        
        switch (config.type) {
            case TIME_BASED:
                result = applyTimeWindow(records, config, targetTokens);
                break;
            case TURN_BASED:
                result = applyTurnWindow(records, config, targetTokens);
                break;
            case COUNT_BASED:
            default:
                result = applyCountWindow(records, config, targetTokens);
                break;
        }
        
        return result;
    }
    
    /**
     * 应用时间窗口
     */
    private List<WindowRecord> applyTimeWindow(List<WindowRecord> records, WindowConfig config, long targetTokens) {
        List<WindowRecord> result = new ArrayList<>();
        Instant cutoffTime = Instant.now().minusSeconds(config.timeWindowMinutes * 60);
        
        // 从最新的记录开始
        long currentTokens = 0;
        for (int i = records.size() - 1; i >= 0; i--) {
            WindowRecord record = records.get(i);
            
            // 检查时间戳
            if (record.timestamp != null && record.timestamp.isBefore(cutoffTime)) {
                break;
            }
            
            long recordTokens = tokenCalculator.calculate(record.content);
            if (currentTokens + recordTokens <= targetTokens) {
                result.add(0, record);
                currentTokens += recordTokens;
            } else {
                break;
            }
        }
        
        return result;
    }
    
    /**
     * 应用轮次窗口
     */
    private List<WindowRecord> applyTurnWindow(List<WindowRecord> records, WindowConfig config, long targetTokens) {
        List<WindowRecord> result = new ArrayList<>();
        
        // 保留最近的N个轮次
        int startIndex = Math.max(0, records.size() - config.windowSize);
        long currentTokens = 0;
        
        for (int i = startIndex; i < records.size(); i++) {
            WindowRecord record = records.get(i);
            long recordTokens = tokenCalculator.calculate(record.content);
            
            if (currentTokens + recordTokens <= targetTokens) {
                result.add(record);
                currentTokens += recordTokens;
            } else if (result.isEmpty()) {
                // 至少保留一条记录，但可能需要截断
                String truncated = truncateContent(record.content, targetTokens);
                record = new WindowRecord(truncated, record.index, record.timestamp, record.type);
                result.add(record);
                break;
            } else {
                break;
            }
        }
        
        return result;
    }
    
    /**
     * 应用计数窗口
     */
    private List<WindowRecord> applyCountWindow(List<WindowRecord> records, WindowConfig config, long targetTokens) {
        List<WindowRecord> result = new ArrayList<>();
        
        // 根据窗口大小确定起始位置
        int windowSize = Math.min(config.windowSize, records.size());
        int startIndex = records.size() - windowSize;
        
        long currentTokens = 0;
        for (int i = startIndex; i < records.size(); i++) {
            WindowRecord record = records.get(i);
            long recordTokens = tokenCalculator.calculate(record.content);
            
            if (currentTokens + recordTokens <= targetTokens) {
                result.add(record);
                currentTokens += recordTokens;
            } else if (result.isEmpty()) {
                // 至少保留一条记录
                String truncated = truncateContent(record.content, targetTokens);
                record = new WindowRecord(truncated, record.index, record.timestamp, record.type);
                result.add(record);
                break;
            } else {
                break;
            }
        }
        
        return result;
    }
    
    /**
     * 生成排除内容的摘要
     */
    private String generateSummaryForExcluded(List<WindowRecord> allRecords, List<WindowRecord> includedRecords) {
        Set<Integer> includedIndices = includedRecords.stream()
            .map(r -> r.index)
            .collect(Collectors.toSet());
        
        List<WindowRecord> excludedRecords = allRecords.stream()
            .filter(r -> !includedIndices.contains(r.index))
            .collect(Collectors.toList());
        
        if (excludedRecords.isEmpty()) {
            return "";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("[已省略 ").append(excludedRecords.size()).append(" 条较早的记录");
        
        // 统计省略内容的类型
        Map<RecordType, Long> typeCounts = excludedRecords.stream()
            .collect(Collectors.groupingBy(r -> r.type, Collectors.counting()));
        
        if (!typeCounts.isEmpty()) {
            summary.append("（");
            typeCounts.forEach((type, count) -> {
                summary.append(type.getDescription()).append(": ").append(count).append("条 ");
            });
            summary.append("）");
        }
        
        summary.append("]\n");
        
        // 如果有重要内容，提取关键词
        List<String> keywords = extractKeywords(excludedRecords);
        if (!keywords.isEmpty()) {
            summary.append("关键词: ").append(String.join(", ", keywords)).append("\n");
        }
        
        return summary.toString();
    }
    
    /**
     * 提取关键词
     */
    private List<String> extractKeywords(List<WindowRecord> records) {
        Map<String, Integer> wordFreq = new HashMap<>();
        Pattern wordPattern = Pattern.compile("\\b[\\w\\u4e00-\\u9fa5]{2,}\\b");
        
        for (WindowRecord record : records) {
            Matcher matcher = wordPattern.matcher(record.content);
            while (matcher.find()) {
                String word = matcher.group().toLowerCase();
                wordFreq.merge(word, 1, Integer::sum);
            }
        }
        
        // 选择频率最高的前5个词
        return wordFreq.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .limit(5)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
    }
    
    /**
     * 重建内容
     */
    private String rebuildContent(List<WindowRecord> records, String summary, CompressionLevel level, WindowConfig config) {
        StringBuilder builder = new StringBuilder();
        
        // 添加压缩信息头
        if (level.isSevereCompression()) {
            builder.append("[滑动窗口压缩 - ");
            builder.append(config.type.getDescription());
            builder.append(" - 保留最近 ").append(records.size()).append(" 条记录]\n\n");
        }
        
        // 添加摘要
        if (!summary.isEmpty()) {
            builder.append(summary).append("\n");
        }
        
        // 添加保留的记录
        for (int i = 0; i < records.size(); i++) {
            WindowRecord record = records.get(i);
            
            // 添加时间戳（如果有）
            if (record.timestamp != null && config.type == WindowType.TIME_BASED) {
                builder.append("[").append(formatTimestamp(record.timestamp)).append("] ");
            }
            
            builder.append(record.content);
            
            // 添加分隔符
            if (i < records.size() - 1) {
                if (record.type == RecordType.DIALOGUE_TURN) {
                    builder.append("\n\n");
                } else if (record.type == RecordType.PARAGRAPH) {
                    builder.append("\n\n");
                } else {
                    builder.append(" ");
                }
            }
        }
        
        return builder.toString().trim();
    }
    
    /**
     * 格式化时间戳
     */
    private String formatTimestamp(Instant timestamp) {
        LocalDateTime ldt = LocalDateTime.ofInstant(timestamp, ZoneId.systemDefault());
        return ldt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 截断内容
     */
    private String truncateContent(String content, long maxTokens) {
        long currentTokens = tokenCalculator.calculate(content);
        if (currentTokens <= maxTokens) {
            return content;
        }
        
        // 二分查找合适的截断点
        int left = 0;
        int right = content.length();
        String result = "";
        
        while (left < right) {
            int mid = (left + right) / 2;
            String truncated = content.substring(0, mid);
            long tokens = tokenCalculator.calculate(truncated);
            
            if (tokens <= maxTokens) {
                result = truncated;
                left = mid + 1;
            } else {
                right = mid;
            }
        }
        
        // 添加省略标记
        if (!result.isEmpty() && result.length() < content.length()) {
            result += "...";
        }
        
        return result;
    }
    
    /**
     * 检测是否为对话内容
     */
    private boolean isDialogueContent(String content) {
        Matcher matcher = TURN_PATTERN.matcher(content);
        int count = 0;
        while (matcher.find()) {
            count++;
            if (count >= 2) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检测是否为日志内容
     */
    private boolean isLogContent(String content) {
        Matcher matcher = TIMESTAMP_PATTERN.matcher(content);
        int count = 0;
        int maxCheck = Math.min(10, content.split("\n").length);
        
        while (matcher.find() && count < maxCheck) {
            count++;
            if (count >= 3) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 确定压缩级别
     */
    private CompressionLevel determineCompressionLevel(double compressionRatio) {
        if (compressionRatio >= 0.8) {
            return CompressionLevel.LIGHT;
        } else if (compressionRatio >= 0.6) {
            return CompressionLevel.MODERATE;
        } else if (compressionRatio >= 0.4) {
            return CompressionLevel.HEAVY;
        } else {
            return CompressionLevel.EXTREME;
        }
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String content, long targetTokens) {
        int contentHash = content.hashCode();
        return String.format("%s_%d_%d", STRATEGY_NAME, contentHash, targetTokens);
    }
    
    /**
     * 缓存结果
     */
    private void cacheResult(String key, CompressionResult result) {
        if (cache.size() >= MAX_CACHE_SIZE) {
            Iterator<String> iterator = cache.keySet().iterator();
            if (iterator.hasNext()) {
                iterator.next();
                iterator.remove();
            }
        }
        cache.put(key, result);
    }
    
    /**
     * 创建空结果
     */
    private CompressionResult createEmptyResult(Instant startTime) {
        CompressionResult result = CompressionResult.builder()
            .content("")
            .tokenCount(0)
            .originalTokenCount(0)
            .compressionRatio(1.0)
            .compressionLevel(CompressionLevel.NONE)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        result.calculateDuration();
        return result;
    }
    
    /**
     * 创建未压缩结果
     */
    private CompressionResult createUncompressedResult(String content, long tokenCount, Instant startTime) {
        CompressionResult result = CompressionResult.builder()
            .content(content)
            .tokenCount(tokenCount)
            .originalTokenCount(tokenCount)
            .compressionRatio(1.0)
            .compressionLevel(CompressionLevel.NONE)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        result.calculateDuration();
        result.addMetadata("reason", "Content already within target tokens");
        return result;
    }
    
    /**
     * 构建压缩结果
     */
    private CompressionResult buildCompressionResult(
            String compressedContent,
            long originalTokenCount,
            CompressionLevel level,
            WindowConfig config,
            Instant startTime) {
        
        long compressedTokenCount = tokenCalculator.calculate(compressedContent);
        
        CompressionResult result = CompressionResult.builder()
            .content(compressedContent)
            .tokenCount(compressedTokenCount)
            .originalTokenCount(originalTokenCount)
            .compressionLevel(level)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        
        result.calculateCompressionRatio();
        result.calculateDuration();
        
        // 添加统计信息
        result.addMetadata("compressionLevel", level.getDescription());
        result.addMetadata("windowType", config.type.getDescription());
        result.addMetadata("windowSize", config.windowSize);
        result.addMetadata("timeWindowMinutes", config.timeWindowMinutes);
        result.addMetadata("tokenReduction", result.getTokenReduction());
        result.addMetadata("reductionPercentage", String.format("%.1f%%", result.getTokenReductionPercentage()));
        
        log.info("Sliding window compression completed: {}", result.getSummary());
        
        return result;
    }
    
    @Override
    public boolean canCompress(ContextLayer layer) {
        // 滑动窗口压缩策略可以压缩所有可压缩的层
        return layer != null && layer.isCompressible();
    }
    
    @Override
    public int getCompressionPriority() {
        return COMPRESSION_PRIORITY;
    }
    
    @Override
    public String getName() {
        return STRATEGY_NAME;
    }
    
    @Override
    public String getDescription() {
        return "滑动窗口压缩策略：保留最近的记录，支持基于时间、轮次和计数的窗口";
    }
    
    /**
     * 添加自定义窗口配置
     */
    public void addWindowConfig(String name, WindowConfig config) {
        windowConfigs.put(name, config);
        log.info("Added window config: {} - {}", name, config);
    }
    
    /**
     * 获取窗口配置
     */
    public WindowConfig getWindowConfig(String name) {
        return windowConfigs.getOrDefault(name, windowConfigs.get("default"));
    }
    
    /**
     * 窗口配置类
     */
    public static class WindowConfig {
        private final int windowSize;
        private final int timeWindowMinutes;
        private final WindowType type;
        
        public WindowConfig(int windowSize, int timeWindowMinutes, WindowType type) {
            this.windowSize = windowSize;
            this.timeWindowMinutes = timeWindowMinutes;
            this.type = type;
        }
        
        @Override
        public String toString() {
            return String.format("WindowConfig[size=%d, timeMinutes=%d, type=%s]",
                windowSize, timeWindowMinutes, type);
        }
    }
    
    /**
     * 窗口类型枚举
     */
    public enum WindowType {
        COUNT_BASED("基于计数"),
        TIME_BASED("基于时间"),
        TURN_BASED("基于轮次");
        
        private final String description;
        
        WindowType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 窗口记录类
     */
    private static class WindowRecord {
        final String content;
        final int index;
        final Instant timestamp;
        final RecordType type;
        
        WindowRecord(String content, int index, Instant timestamp, RecordType type) {
            this.content = content;
            this.index = index;
            this.timestamp = timestamp;
            this.type = type;
        }
    }
    
    /**
     * 记录类型枚举
     */
    private enum RecordType {
        PARAGRAPH("段落"),
        SENTENCE("句子"),
        DIALOGUE_TURN("对话轮次"),
        TIMESTAMPED("时间戳记录");
        
        private final String description;
        
        RecordType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}