package cn.genn.graph.context.token.impl;

import cn.genn.graph.context.token.TokenCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 简单Token计算器实现
 * 基于字符数估算Token数量
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SimpleTokenCalculator implements TokenCalculator {
    
    /**
     * 默认最大Token限制（基于GPT-4的限制）
     */
    private static final long DEFAULT_MAX_TOKENS = 8192;
    
    /**
     * 中文字符模式
     */
    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");
    
    /**
     * 标点符号模式
     */
    private static final Pattern PUNCTUATION_PATTERN = Pattern.compile("[\\p{Punct}\\s]+");
    
    /**
     * 平均字符数/Token比例
     */
    private static final double AVG_CHARS_PER_TOKEN = 3.5;
    
    /**
     * 中文字符的Token权重
     */
    private static final double CHINESE_CHAR_WEIGHT = 2.0;
    
    /**
     * 英文字符的Token权重
     */
    private static final double ENGLISH_CHAR_WEIGHT = 4.0;
    
    private final long maxTokenLimit;
    
    public SimpleTokenCalculator() {
        this(DEFAULT_MAX_TOKENS);
    }
    
    public SimpleTokenCalculator(long maxTokenLimit) {
        this.maxTokenLimit = maxTokenLimit;
    }
    
    @Override
    public long calculate(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        
        // 统计不同类型字符
        int chineseCount = 0;
        int englishCount = 0;
        int punctuationCount = 0;
        int otherCount = 0;
        
        for (char c : text.toCharArray()) {
            String charStr = String.valueOf(c);
            if (CHINESE_PATTERN.matcher(charStr).matches()) {
                chineseCount++;
            } else if (PUNCTUATION_PATTERN.matcher(charStr).matches()) {
                punctuationCount++;
            } else if (Character.isLetter(c)) {
                englishCount++;
            } else {
                otherCount++;
            }
        }
        
        // 根据不同字符类型计算Token数
        double tokens = 0;
        tokens += chineseCount / CHINESE_CHAR_WEIGHT;
        tokens += englishCount / ENGLISH_CHAR_WEIGHT;
        tokens += punctuationCount / 6.0;  // 标点符号权重较低
        tokens += otherCount / 4.0;  // 其他字符（数字等）
        
        // 添加一些额外的开销（如特殊标记等）
        tokens = tokens * 1.1 + 3;
        
        long result = Math.round(tokens);
        
        log.trace("Calculated tokens for text (length={}): {} tokens", text.length(), result);
        
        return result;
    }
    
    @Override
    public long calculateMessages(List<Message> messages) {
        if (messages == null || messages.isEmpty()) {
            return 0;
        }
        
        long totalTokens = 0;
        
        for (Message message : messages) {
            // 计算消息内容的Token
            String content = message.getText();
            long contentTokens = calculate(content);
            
            // 添加消息元数据的开销（角色、类型等）
            long metadataTokens = 4;  // 估算元数据占用
            
            totalTokens += contentTokens + metadataTokens;
        }
        
        // 添加消息格式的开销
        totalTokens += messages.size() * 3L;
        
        log.trace("Calculated tokens for {} messages: {} tokens", messages.size(), totalTokens);
        
        return totalTokens;
    }
    
    @Override
    public List<Long> calculateBatch(List<String> texts) {
        if (texts == null || texts.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Long> results = new ArrayList<>(texts.size());
        for (String text : texts) {
            results.add(calculate(text));
        }
        
        return results;
    }
    
    @Override
    public long estimate(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        
        // 快速估算：直接按平均字符数计算
        return Math.round(text.length() / AVG_CHARS_PER_TOKEN);
    }
    
    @Override
    public long getMaxTokenLimit() {
        return maxTokenLimit;
    }
    
    @Override
    public String getName() {
        return "SimpleTokenCalculator";
    }
    
    /**
     * 计算文本是否主要是中文
     */
    private boolean isMainlyChinese(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        int chineseCount = 0;
        int totalCount = 0;
        
        for (char c : text.toCharArray()) {
            if (!Character.isWhitespace(c)) {
                totalCount++;
                if (CHINESE_PATTERN.matcher(String.valueOf(c)).matches()) {
                    chineseCount++;
                }
            }
        }
        
        return totalCount > 0 && (double) chineseCount / totalCount > 0.3;
    }
    
    /**
     * 根据文本内容调整Token计算策略
     */
    public long calculateAdaptive(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }
        
        // 判断文本类型并使用不同的计算策略
        if (isMainlyChinese(text)) {
            // 中文为主的文本
            return Math.round(text.length() / 2.0);
        } else {
            // 英文为主的文本
            return Math.round(text.length() / 4.0);
        }
    }
    
    /**
     * 验证Token数是否在限制范围内
     */
    public boolean isWithinLimit(long tokens) {
        return tokens <= maxTokenLimit;
    }
    
    /**
     * 计算超出的Token数
     */
    public long calculateExcess(long tokens) {
        return Math.max(0, tokens - maxTokenLimit);
    }
    
    /**
     * 获取Token使用率
     */
    public double getUsageRatio(long tokens) {
        if (maxTokenLimit == 0) {
            return 1.0;
        }
        return (double) tokens / maxTokenLimit;
    }
    
    /**
     * 根据目标Token数截断文本
     */
    public String truncateToTokenLimit(String text, long targetTokens) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        
        long currentTokens = calculate(text);
        if (currentTokens <= targetTokens) {
            return text;
        }
        
        // 二分查找合适的截断点
        int left = 0;
        int right = text.length();
        String result = text;
        
        while (left < right) {
            int mid = (left + right) / 2;
            String truncated = text.substring(0, mid);
            long tokens = calculate(truncated);
            
            if (tokens <= targetTokens) {
                result = truncated;
                left = mid + 1;
            } else {
                right = mid;
            }
        }
        
        // 添加省略标记
        if (result.length() < text.length()) {
            result += "...";
        }
        
        return result;
    }
}