package cn.genn.graph.context.layer;

/**
 * 上下文层优先级枚举
 * 数值越小优先级越高
 * 
 * <AUTHOR>
 */
public enum LayerPriority {
    
    /**
     * 焦点层 - 最高优先级
     */
    FOCUS(1, "焦点层"),
    
    /**
     * 全景层 - 中等优先级
     */
    PANORAMIC(2, "全景层"),
    
    /**
     * 战略层 - 最低优先级
     */
    STRATEGIC(3, "战略层");
    
    private final int value;
    private final String description;
    
    LayerPriority(int value, String description) {
        this.value = value;
        this.description = description;
    }
    
    public int getValue() {
        return value;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据优先级值获取枚举
     * @param value 优先级值
     * @return 对应的枚举，如果不存在返回null
     */
    public static LayerPriority fromValue(int value) {
        for (LayerPriority priority : values()) {
            if (priority.value == value) {
                return priority;
            }
        }
        return null;
    }
}