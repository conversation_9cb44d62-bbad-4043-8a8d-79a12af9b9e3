package cn.genn.graph.context.compression;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 压缩结果类
 * 包含压缩后的内容和相关统计信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompressionResult {
    
    /**
     * 压缩后的内容
     */
    private String content;
    
    /**
     * 压缩后的Token数量
     */
    private long tokenCount;
    
    /**
     * 原始Token数量
     */
    private long originalTokenCount;
    
    /**
     * 压缩比例（压缩后/原始）
     */
    private double compressionRatio;
    
    /**
     * 应用的压缩级别
     */
    private CompressionLevel compressionLevel;
    
    /**
     * 使用的压缩策略名称
     */
    private String strategyName;
    
    /**
     * 压缩开始时间
     */
    private Instant startTime;
    
    /**
     * 压缩结束时间
     */
    private Instant endTime;
    
    /**
     * 压缩耗时（毫秒）
     */
    private long durationMillis;
    
    /**
     * 是否成功
     */
    @Builder.Default
    private boolean success = true;
    
    /**
     * 错误信息（如果压缩失败）
     */
    private String errorMessage;
    
    /**
     * 被移除的内容摘要
     */
    @Builder.Default
    private List<String> removedContent = new ArrayList<>();
    
    /**
     * 保留的关键信息
     */
    @Builder.Default
    private List<String> preservedKeyInfo = new ArrayList<>();
    
    /**
     * 额外的元数据
     */
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    /**
     * 计算压缩比例
     */
    public void calculateCompressionRatio() {
        if (originalTokenCount > 0) {
            this.compressionRatio = (double) tokenCount / originalTokenCount;
        } else {
            this.compressionRatio = 1.0;
        }
    }
    
    /**
     * 计算压缩耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMillis = Duration.between(startTime, endTime).toMillis();
        }
    }
    
    /**
     * 获取Token减少量
     */
    public long getTokenReduction() {
        return originalTokenCount - tokenCount;
    }
    
    /**
     * 获取Token减少百分比
     */
    public double getTokenReductionPercentage() {
        if (originalTokenCount > 0) {
            return (1.0 - compressionRatio) * 100;
        }
        return 0.0;
    }
    
    /**
     * 判断是否达到目标压缩
     * 
     * @param targetTokens 目标Token数
     * @return true表示达到目标
     */
    public boolean meetsTarget(long targetTokens) {
        return tokenCount <= targetTokens;
    }
    
    /**
     * 添加被移除的内容描述
     */
    public void addRemovedContent(String description) {
        if (description != null && !description.isEmpty()) {
            this.removedContent.add(description);
        }
    }
    
    /**
     * 添加保留的关键信息
     */
    public void addPreservedKeyInfo(String info) {
        if (info != null && !info.isEmpty()) {
            this.preservedKeyInfo.add(info);
        }
    }
    
    /**
     * 添加元数据
     */
    public void addMetadata(String key, Object value) {
        this.metadata.put(key, value);
    }
    
    /**
     * 创建成功的压缩结果
     */
    public static CompressionResult success(String content, long tokenCount, long originalTokenCount) {
        CompressionResult result = CompressionResult.builder()
                .content(content)
                .tokenCount(tokenCount)
                .originalTokenCount(originalTokenCount)
                .success(true)
                .build();
        result.calculateCompressionRatio();
        return result;
    }
    
    /**
     * 创建失败的压缩结果
     */
    public static CompressionResult failure(String errorMessage, String originalContent, long originalTokenCount) {
        return CompressionResult.builder()
                .content(originalContent)
                .tokenCount(originalTokenCount)
                .originalTokenCount(originalTokenCount)
                .compressionRatio(1.0)
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
    
    /**
     * 获取压缩统计摘要
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("Compression Result: ");
        
        if (success) {
            sb.append("SUCCESS");
            sb.append(" | Original: ").append(originalTokenCount).append(" tokens");
            sb.append(" | Compressed: ").append(tokenCount).append(" tokens");
            sb.append(" | Reduction: ").append(String.format("%.1f%%", getTokenReductionPercentage()));
            if (compressionLevel != null) {
                sb.append(" | Level: ").append(compressionLevel.getDescription());
            }
            if (strategyName != null) {
                sb.append(" | Strategy: ").append(strategyName);
            }
            if (durationMillis > 0) {
                sb.append(" | Time: ").append(durationMillis).append("ms");
            }
        } else {
            sb.append("FAILED");
            if (errorMessage != null) {
                sb.append(" | Error: ").append(errorMessage);
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 获取详细报告
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== Compression Report ===\n");
        report.append("Status: ").append(success ? "SUCCESS" : "FAILED").append("\n");
        
        if (success) {
            report.append("Original Tokens: ").append(originalTokenCount).append("\n");
            report.append("Compressed Tokens: ").append(tokenCount).append("\n");
            report.append("Token Reduction: ").append(getTokenReduction())
                  .append(" (").append(String.format("%.1f%%", getTokenReductionPercentage())).append(")\n");
            report.append("Compression Ratio: ").append(String.format("%.2f", compressionRatio)).append("\n");
            
            if (compressionLevel != null) {
                report.append("Compression Level: ").append(compressionLevel.getDescription()).append("\n");
            }
            
            if (strategyName != null) {
                report.append("Strategy Used: ").append(strategyName).append("\n");
            }
            
            if (durationMillis > 0) {
                report.append("Processing Time: ").append(durationMillis).append(" ms\n");
            }
            
            if (!removedContent.isEmpty()) {
                report.append("\nRemoved Content:\n");
                removedContent.forEach(item -> report.append("  - ").append(item).append("\n"));
            }
            
            if (!preservedKeyInfo.isEmpty()) {
                report.append("\nPreserved Key Information:\n");
                preservedKeyInfo.forEach(item -> report.append("  - ").append(item).append("\n"));
            }
            
            if (!metadata.isEmpty()) {
                report.append("\nAdditional Metadata:\n");
                metadata.forEach((key, value) -> 
                    report.append("  ").append(key).append(": ").append(value).append("\n"));
            }
        } else {
            report.append("Error: ").append(errorMessage != null ? errorMessage : "Unknown error").append("\n");
        }
        
        report.append("========================\n");
        return report.toString();
    }
}