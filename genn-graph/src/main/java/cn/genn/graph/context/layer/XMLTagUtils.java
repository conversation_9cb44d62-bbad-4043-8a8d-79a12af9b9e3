package cn.genn.graph.context.layer;

/**
 * XML标签工具类，提供字段名到XML标签的映射和格式化功能
 * 
 * <AUTHOR>
 */
public final class XMLTagUtils {
    
    private XMLTagUtils() {
        // Utility class
    }
    
    /**
     * 将驼峰命名转换为小写下划线格式
     */
    public static String camelToSnakeCase(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    
    /**
     * 根据字段名获取对应的XML标签名
     */
    public static String getTag(String fieldName) {
        return camelToSnakeCase(fieldName);
    }
    
    /**
     * 创建开始标签
     */
    public static String openTag(String fieldName) {
        return "<" + getTag(fieldName) + ">";
    }
    
    /**
     * 创建结束标签
     */
    public static String closeTag(String fieldName) {
        return "</" + getTag(fieldName) + ">";
    }
    
    /**
     * 创建带属性的开始标签
     */
    public static String openTagWithAttr(String fieldName, String attrName, String attrValue) {
        return "<" + getTag(fieldName) + " " + attrName + "=\"" + attrValue + "\">";
    }
    
    /**
     * 包装内容为完整标签
     */
    public static String wrapContent(String fieldName, String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }
        return openTag(fieldName) + content + closeTag(fieldName);
    }
    
    /**
     * 包装内容为完整标签（带换行）
     */
    public static String wrapContentWithNewLine(String fieldName, String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }
        return openTag(fieldName) + content + closeTag(fieldName) + "\n";
    }
    
    /**
     * 包装内容为完整标签（带属性）
     */
    public static String wrapContentWithAttr(String fieldName, String attrName, String attrValue, String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }
        return openTagWithAttr(fieldName, attrName, attrValue) + content + closeTag(fieldName);
    }
    
    /**
     * 包装三层上下文容器标签：<context priority="n" name="layer_name"> ... </context>\n
     */
    public static String wrapContext(String layerName, int priority, String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }
        return "<context priority=\"" + priority + "\" name=\"" + layerName + "\">" + content + "</context>\n";
    }
    
    /**
     * 包装内容为完整标签（带两个属性）
     */
    public static String wrapContentWithTwoAttrs(String fieldName, String attrName1, String attrValue1,
                                                 String attrName2, String attrValue2, String content) {
        if (content == null || content.isEmpty()) {
            return "";
        }
        return "<" + getTag(fieldName) + " " + attrName1 + "=\"" + attrValue1 + "\" "
                + attrName2 + "=\"" + attrValue2 + "\">" + content + closeTag(fieldName);
    }
}