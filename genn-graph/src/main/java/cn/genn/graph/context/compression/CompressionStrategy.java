package cn.genn.graph.context.compression;

import cn.genn.graph.context.layer.ContextLayer;

/**
 * 压缩策略接口
 * 定义了上下文内容压缩的标准行为
 * 
 * <AUTHOR>
 */
public interface CompressionStrategy {
    
    /**
     * 压缩内容到目标Token数量
     * 
     * @param content 原始内容
     * @param targetTokens 目标Token数量
     * @return 压缩结果
     */
    CompressionResult compress(String content, long targetTokens);
    
    /**
     * 判断是否可以压缩指定的上下文层
     * 
     * @param layer 上下文层
     * @return true表示可以压缩，false表示不能压缩
     */
    boolean canCompress(ContextLayer layer);
    
    /**
     * 获取压缩策略的优先级
     * 数值越小优先级越高
     * 
     * @return 优先级值
     */
    int getCompressionPriority();
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getName();
    
    /**
     * 获取策略描述
     * 
     * @return 策略描述
     */
    default String getDescription() {
        return "Compression strategy: " + getName();
    }
}