package cn.genn.graph.context.token;

import org.springframework.ai.chat.messages.Message;

import java.util.List;

/**
 * Token计算器接口
 * 用于计算文本内容的Token数量
 * 
 * <AUTHOR>
 */
public interface TokenCalculator {
    
    /**
     * 计算文本的Token数量
     * 
     * @param text 文本内容
     * @return Token数量
     */
    long calculate(String text);
    
    /**
     * 计算消息列表的Token数量
     * 
     * @param messages 消息列表
     * @return 总Token数量
     */
    long calculateMessages(List<Message> messages);
    
    /**
     * 批量计算多个文本的Token数量
     * 
     * @param texts 文本列表
     * @return Token数量列表
     */
    List<Long> calculateBatch(List<String> texts);
    
    /**
     * 估算Token数量（快速但不精确）
     * 
     * @param text 文本内容
     * @return 估算的Token数量
     */
    default long estimate(String text) {
        // 默认实现：按字符数估算（中文约2字符/token，英文约4字符/token）
        if (text == null || text.isEmpty()) {
            return 0;
        }
        // 简单估算：平均3个字符一个token
        return (text.length() + 2) / 3;
    }
    
    /**
     * 获取模型的最大Token限制
     * 
     * @return 最大Token数
     */
    long getMaxTokenLimit();
    
    /**
     * 获取计算器名称
     * 
     * @return 计算器名称
     */
    String getName();
}