package cn.genn.graph.context;

import cn.genn.graph.core.StateKey;
import com.alibaba.cloud.ai.graph.OverAllState;
import org.springframework.ai.chat.messages.Message;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class ContextUtils {
    
    public static AgentContextState getContext(OverAllState state) {
        Optional<AgentContextState> contextOptional = state.value(StateKey.CONTEXT_AGENT.getKey(), AgentContextState.class);
        return contextOptional.orElse(null);
    }

    public static List<Message> extractKeyInteractionLogs(List<Message> messages) {
        if (messages == null || messages.isEmpty()) {
            return new ArrayList<>();
        }

        // 保留最近的5条关键消息
        int maxKeyLogs = 5;
        int startIndex = Math.max(0, messages.size() - maxKeyLogs);

        return messages.subList(startIndex, messages.size());
    }
}
