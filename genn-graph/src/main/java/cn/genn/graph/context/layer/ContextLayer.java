package cn.genn.graph.context.layer;

import java.util.Map;

/**
 * 上下文层接口
 * 定义了上下文层的基本行为和属性
 * 
 * <AUTHOR>
 */
public interface ContextLayer {
    
    /**
     * 获取层名称
     * @return 层名称
     */
    String getName();
    
    /**
     * 获取优先级
     * 数值越小优先级越高
     * @return 优先级值
     */
    int getPriority();
    
    /**
     * 获取Token数量
     * @return token数量
     */
    long getTokenCount();
    
    /**
     * 设置Token数量
     * @param tokenCount token数量
     */
    void setTokenCount(long tokenCount);
    
    /**
     * 获取内容
     * @return 格式化后的内容
     */
    String getContent();
    
    /**
     * 设置内容
     * @param content 内容
     */
    void setContent(String content);
    
    /**
     * 是否可压缩
     * @return true表示可压缩，false表示不可压缩
     */
    boolean isCompressible();
    
    /**
     * 克隆当前层
     * @return 克隆的层对象
     */
    ContextLayer clone();
}