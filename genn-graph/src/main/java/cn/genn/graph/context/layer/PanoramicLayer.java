package cn.genn.graph.context.layer;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.graph.context.compression.CompressionLevel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.tool.ToolCallback;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.genn.graph.context.layer.XMLTagUtils.*;

/**
 * 全景层实现
 * 对应 AgentContextState.TaskPanoramaContext
 * 提供完成当前整个任务（思维树）所需的背景信息和关联知识
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PanoramicLayer implements ContextLayer, Serializable, Cloneable {
    
    private static final long serialVersionUID = 1L;
    
    // ==================== 任务全局视图 (Global Task View) ====================
    
    /**
     * 任务参与者清单 - 执行图中所有涉及的智能体的角色和能力摘要
     */
    @Builder.Default
    @JsonIgnore
    private List<ToolCallback> allAgentTools = new ArrayList<>();
    
    // ==================== 任务历史产出物 (Historical Outputs) ====================
    
    /**
     * 模型输出摘要 - 记录各思维节点的关键输出和决策摘要
     */
    @Builder.Default
    private List<String> llmOutputDigest = new ArrayList<>();
    
    /**
     * 关键会话片段 - 最近3-5轮与任务直接相关的核心交互记录
     */
    @Builder.Default
    private List<Message> keyInteractionLogs = new ArrayList<>();
    
    // ==================== 任务追踪 (Task Tracking) ====================
    
    /**
     * 任务里程碑记录 - 记录任务执行过程中的关键节点和已达成的阶段性目标
     */
    @Builder.Default
    private List<Map<String, Object>> taskMilestones = new ArrayList<>();
    
    // ==================== 层管理属性 ====================
    
    /**
     * 压缩级别
     */
    @Builder.Default
    private CompressionLevel compressionLevel = CompressionLevel.NONE;
    
    /**
     * Token数量
     */
    private long tokenCount;
    
    /**
     * 原始Token数量（压缩前）
     */
    private long originalTokenCount;
    
    /**
     * 原始内容
     */
    private String rawContent;
    
    @Override
    public String getName() {
        return "panoramic";
    }
    
    @Override
    public int getPriority() {
        return LayerPriority.PANORAMIC.getValue();
    }
    
    @Override
    public boolean isCompressible() {
        return true;
    }
    
    @Override
    public String getContent() {
        return formatContent();
    }
    
    @Override
    public void setContent(String content) {
        this.rawContent = content;
        parseContent(content);
    }
    
    
    @Override
    public PanoramicLayer clone() {
        try {
            PanoramicLayer cloned = (PanoramicLayer) super.clone();
            // 深拷贝集合类型
            cloned.allAgentTools = new ArrayList<>(this.allAgentTools);
            cloned.llmOutputDigest = new ArrayList<>(this.llmOutputDigest);
            cloned.keyInteractionLogs = new ArrayList<>(this.keyInteractionLogs);
            cloned.taskMilestones = new ArrayList<>(this.taskMilestones);
            return cloned;
        } catch (CloneNotSupportedException e) {
            log.error("Failed to clone PanoramicLayer", e);
            return PanoramicLayer.builder()
                    .allAgentTools(new ArrayList<>(this.allAgentTools))
                    .llmOutputDigest(new ArrayList<>(this.llmOutputDigest))
                    .keyInteractionLogs(new ArrayList<>(this.keyInteractionLogs))
                    .taskMilestones(new ArrayList<>(this.taskMilestones))
                    .compressionLevel(this.compressionLevel)
                    .tokenCount(this.tokenCount)
                    .originalTokenCount(this.originalTokenCount)
                    .rawContent(this.rawContent)
                    .build();
        }
    }
    
    /**
     * 格式化内容为XML结构
     */
    private String formatContent() {
        StringBuilder sb = new StringBuilder();
        
        // 任务里程碑（短标签）
        if (!taskMilestones.isEmpty()) {
            sb.append(openTag("task_milestones")).append("\n");
            List<Map<String, Object>> displayMilestones = limitList(taskMilestones,
                calculateLimit(taskMilestones.size()));
            
            for (Map<String, Object> milestone : displayMilestones) {
                sb.append(openTagWithAttr("item", "status", String.valueOf(milestone.get("status"))));
                if (milestone.containsKey("timestamp")) {
                    sb.append(" timestamp=\"").append(milestone.get("timestamp")).append("\"");
                }
                sb.append(">");
                if (milestone.containsKey("description")) {
                    sb.append(milestone.get("description"));
                }
                sb.append(closeTag("item")).append("\n");
            }
            
            if (taskMilestones.size() > displayMilestones.size()) {
                sb.append(wrapContentWithAttr("more_info", "count", 
                    String.valueOf(taskMilestones.size() - displayMilestones.size()),
                    "more milestones")).append("\n");
            }
            sb.append(closeTag("task_milestones")).append("\n");
        }
        
        // 节点输出摘要（短标签，统一结构）
        if (!llmOutputDigest.isEmpty()) {
            sb.append(openTag("llm_digest")).append("\n");
            int limit = calculateLimit(llmOutputDigest.size());
            List<String> displayDigests = limitList(llmOutputDigest, limit);
            for (String summary : displayDigests) {
                sb.append(wrapContentWithNewLine("summary", summary));
            }
            if (llmOutputDigest.size() > displayDigests.size()) {
                sb.append(wrapContentWithAttr("more_info", "count", String.valueOf(llmOutputDigest.size() - displayDigests.size()), "more summaries")).append("\n");
            }
            sb.append(closeTag("llm_digest")).append("\n");
        }
        
        // 关键交互日志（短标签，统一turn结构）
        if (!keyInteractionLogs.isEmpty() && compressionLevel.ordinal() <= CompressionLevel.MODERATE.ordinal()) {
            sb.append(openTag("key_logs")).append("\n");
            int limit = calculateLimit(Math.min(5, keyInteractionLogs.size()));
            keyInteractionLogs.stream()
                .limit(limit)
                .forEach(message -> {
                    String role = message.getMessageType().name().toLowerCase();
                    sb.append(openTagWithAttr("turn", "role", role))
                      .append(message.getText())
                      .append(closeTag("turn")).append("\n");
                });
            if (keyInteractionLogs.size() > limit) {
                sb.append(wrapContentWithAttr("more_info", "count", String.valueOf(keyInteractionLogs.size() - limit), "earlier turns omitted")).append("\n");
            }
            sb.append(closeTag("key_logs")).append("\n");
        }
        
        // 如果有原始内容且上述格式化内容为空，则返回原始内容
        if (sb.isEmpty() && rawContent != null) {
            return wrapContent("rawContent", rawContent);
        }
        
        return sb.toString().trim();
    }
    
    /**
     * 截断内容
     */
    private String truncateContent(String content, int maxLength) {
        if (content == null || content.length() <= maxLength) {
            return content;
        }
        return content.substring(0, maxLength) + "...";
    }
    
    /**
     * 根据压缩级别计算显示限制
     */
    private int calculateLimit(int totalSize) {
        switch (compressionLevel) {
            case EXTREME:
                return Math.min(2, totalSize);
            case HEAVY:
                return Math.min(3, totalSize);
            case MODERATE:
                return Math.min(5, totalSize);
            case LIGHT:
                return Math.min(8, totalSize);
            default:
                return totalSize;
        }
    }
    
    /**
     * 限制列表大小
     */
    private <T> List<T> limitList(List<T> list, int limit) {
        if (list.size() <= limit) {
            return list;
        }
        // 返回最新的N个元素
        return list.subList(Math.max(0, list.size() - limit), list.size());
    }
    
    /**
     * 解析内容
     */
    private void parseContent(String content) {
        if (content == null || content.isEmpty()) {
        }
        // 简化实现，实际可能需要更复杂的解析逻辑
    }
    
    /**
     * 添加里程碑
     */
    public void addMilestone(Map<String, Object> milestone) {
        if (milestone != null) {
            this.taskMilestones.add(milestone);
        }
    }
    
    
    /**
     * 添加关键交互日志
     */
    public void addKeyInteraction(Message message) {
        if (message != null) {
            this.keyInteractionLogs.add(message);
            // 保持最近的5-10条
            if (keyInteractionLogs.size() > 10) {
                keyInteractionLogs.remove(0);
            }
        }
    }
    
    /**
     * 应用压缩
     */
    public void applyCompression(CompressionLevel level) {
        if (this.originalTokenCount == 0) {
            this.originalTokenCount = this.tokenCount;
        }
        this.compressionLevel = level;
        log.info("Applied {} to panoramic layer", level.getDescription());
    }
    
    /**
     * 获取压缩后的里程碑数量
     */
    public int getCompressedMilestoneCount() {
        return calculateLimit(taskMilestones.size());
    }
    
    /**
     * 获取压缩后的输出数量
     */
    public int getCompressedOutputCount() {
        return calculateLimit(llmOutputDigest.size());
    }
    
    /**
     * 创建里程碑
     */
    public static Map<String, Object> createMilestone(String description, String status) {
        Map<String, Object> milestone = new HashMap<>();
        milestone.put("description", description);
        milestone.put("status", status);
        milestone.put("timestamp", LocalDateTime.now());
        return milestone;
    }
    
    /**
     * 创建节点输出摘要
     */
    public static Map<String, Object> createNodeOutputDigest(String nodeName, String summary) {
        Map<String, Object> digest = new HashMap<>();
        digest.put("nodeName", nodeName);
        digest.put("summary", summary);
        digest.put("timestamp", LocalDateTime.now());
        return digest;
    }
}