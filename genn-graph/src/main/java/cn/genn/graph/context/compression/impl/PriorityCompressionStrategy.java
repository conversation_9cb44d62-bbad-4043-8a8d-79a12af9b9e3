package cn.genn.graph.context.compression.impl;

import cn.genn.graph.context.compression.CompressionLevel;
import cn.genn.graph.context.compression.CompressionResult;
import cn.genn.graph.context.compression.CompressionStrategy;
import cn.genn.graph.context.layer.ContextLayer;
import cn.genn.graph.context.token.TokenCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 优先级压缩策略
 * 根据内容的优先级进行选择性保留
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class PriorityCompressionStrategy implements CompressionStrategy {
    
    private static final String STRATEGY_NAME = "PriorityCompression";
    private static final int COMPRESSION_PRIORITY = 20;
    
    /**
     * 优先级标记模式
     */
    private static final Pattern PRIORITY_MARKER_PATTERN = Pattern.compile(
        "\\[(CRITICAL|HIGH|MEDIUM|LOW|INFO)\\]|" +
        "^#{1,3}\\s+|" +  // Markdown标题
        "^\\*\\*(.+?)\\*\\*|" +  // 粗体文本
        "^>\\s+|" +  // 引用
        "^\\d+\\.|" +  // 有序列表
        "^[-*+]\\s+"  // 无序列表
    );
    
    /**
     * 关键内容模式
     */
    private static final Pattern CRITICAL_CONTENT_PATTERN = Pattern.compile(
        "(错误|异常|失败|警告|危险|紧急|重要|关键|核心|必须|禁止|" +
        "ERROR|EXCEPTION|FAIL|WARNING|DANGER|URGENT|IMPORTANT|CRITICAL|MUST|FORBIDDEN)",
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 结果和结论模式
     */
    private static final Pattern RESULT_PATTERN = Pattern.compile(
        "(结果|结论|总结|答案|解决方案|建议|推荐|决定|" +
        "RESULT|CONCLUSION|SUMMARY|ANSWER|SOLUTION|SUGGESTION|RECOMMENDATION|DECISION)",
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * 代码块模式
     */
    private static final Pattern CODE_BLOCK_PATTERN = Pattern.compile(
        "```[\\s\\S]*?```|`[^`]+`"
    );
    
    /**
     * URL和路径模式
     */
    private static final Pattern URL_PATH_PATTERN = Pattern.compile(
        "(https?://[\\S]+|/[\\w/.-]+|[A-Za-z]:\\\\[\\w\\\\.-]+)"
    );
    
    /**
     * 数字和数据模式
     */
    private static final Pattern DATA_PATTERN = Pattern.compile(
        "\\b\\d+(\\.\\d+)?%?\\b|" +  // 数字和百分比
        "\\$[\\d,]+(\\.\\d{2})?|" +  // 金额
        "\\d{4}-\\d{2}-\\d{2}|" +  // 日期
        "\\d{2}:\\d{2}(:\\d{2})?"  // 时间
    );
    
    /**
     * 优先级评分规则
     */
    private final Map<String, PriorityRule> priorityRules = new ConcurrentHashMap<>();
    
    /**
     * 缓存
     */
    private final Map<String, CompressionResult> cache = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 100;
    
    @Autowired
    private TokenCalculator tokenCalculator;
    
    /**
     * 构造函数，初始化默认优先级规则
     */
    public PriorityCompressionStrategy() {
        initializeDefaultRules();
    }
    
    /**
     * 初始化默认优先级规则
     */
    private void initializeDefaultRules() {
        // 关键内容规则
        priorityRules.put("critical", new PriorityRule(
            "Critical Content",
            100,
            content -> CRITICAL_CONTENT_PATTERN.matcher(content).find()
        ));
        
        // 结果和结论规则
        priorityRules.put("result", new PriorityRule(
            "Results and Conclusions",
            90,
            content -> RESULT_PATTERN.matcher(content).find()
        ));
        
        // 代码块规则
        priorityRules.put("code", new PriorityRule(
            "Code Blocks",
            80,
            content -> CODE_BLOCK_PATTERN.matcher(content).find()
        ));
        
        // URL和路径规则
        priorityRules.put("url", new PriorityRule(
            "URLs and Paths",
            70,
            content -> URL_PATH_PATTERN.matcher(content).find()
        ));
        
        // 数据规则
        priorityRules.put("data", new PriorityRule(
            "Numeric Data",
            60,
            content -> DATA_PATTERN.matcher(content).find()
        ));
        
        // 标题规则
        priorityRules.put("heading", new PriorityRule(
            "Headings",
            50,
            content -> content.startsWith("#") || content.matches("^={3,}$|^-{3,}$")
        ));
        
        // 列表项规则
        priorityRules.put("list", new PriorityRule(
            "List Items",
            40,
            content -> content.matches("^\\s*[-*+•]\\s+.*|^\\s*\\d+\\.\\s+.*")
        ));
        
        // 引用规则
        priorityRules.put("quote", new PriorityRule(
            "Quotes",
            30,
            content -> content.startsWith(">") || content.matches("^[\"'].*[\"']$")
        ));
    }
    
    @Override
    public CompressionResult compress(String content, long targetTokens) {
        Instant startTime = Instant.now();
        
        try {
            // 检查输入
            if (content == null || content.isEmpty()) {
                return createEmptyResult(startTime);
            }
            
            // 计算原始Token数
            long originalTokenCount = tokenCalculator.calculate(content);
            
            // 如果原始内容已经满足要求，直接返回
            if (originalTokenCount <= targetTokens) {
                return createUncompressedResult(content, originalTokenCount, startTime);
            }
            
            // 检查缓存
            String cacheKey = generateCacheKey(content, targetTokens);
            CompressionResult cachedResult = cache.get(cacheKey);
            if (cachedResult != null) {
                log.debug("Using cached compression result for key: {}", cacheKey);
                return cachedResult;
            }
            
            // 根据压缩比例确定压缩级别
            double compressionRatio = (double) targetTokens / originalTokenCount;
            CompressionLevel level = determineCompressionLevel(compressionRatio);
            
            // 执行压缩
            String compressedContent = performCompression(content, targetTokens, level);
            
            // 创建压缩结果
            CompressionResult result = buildCompressionResult(
                compressedContent,
                originalTokenCount,
                level,
                startTime
            );
            
            // 缓存结果
            cacheResult(cacheKey, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("Compression failed", e);
            return CompressionResult.failure(
                "Compression failed: " + e.getMessage(),
                content,
                tokenCalculator.calculate(content)
            );
        }
    }
    
    /**
     * 执行压缩
     */
    private String performCompression(String content, long targetTokens, CompressionLevel level) {
        // 将内容分割成段落或行
        List<ContentSegment> segments = segmentContent(content);
        
        // 计算每个段落的优先级分数
        calculateSegmentPriorities(segments, level);
        
        // 根据优先级选择要保留的内容
        List<ContentSegment> selectedSegments = selectSegmentsByPriority(segments, targetTokens, level);
        
        // 重建内容
        return rebuildContent(selectedSegments, level);
    }
    
    /**
     * 分割内容为段落
     */
    private List<ContentSegment> segmentContent(String content) {
        List<ContentSegment> segments = new ArrayList<>();
        
        // 按段落分割（双换行）
        String[] paragraphs = content.split("\n\n+");
        
        for (int i = 0; i < paragraphs.length; i++) {
            String paragraph = paragraphs[i].trim();
            if (!paragraph.isEmpty()) {
                // 如果段落太长，进一步按行分割
                if (paragraph.length() > 500) {
                    String[] lines = paragraph.split("\n");
                    for (String line : lines) {
                        if (!line.trim().isEmpty()) {
                            segments.add(new ContentSegment(line.trim(), i));
                        }
                    }
                } else {
                    segments.add(new ContentSegment(paragraph, i));
                }
            }
        }
        
        return segments;
    }
    
    /**
     * 计算段落优先级
     */
    private void calculateSegmentPriorities(List<ContentSegment> segments, CompressionLevel level) {
        for (ContentSegment segment : segments) {
            double priority = calculatePriority(segment.content, segment.position, segments.size(), level);
            segment.setPriority(priority);
        }
    }
    
    /**
     * 计算单个内容的优先级分数
     */
    private double calculatePriority(String content, int position, int totalSegments, CompressionLevel level) {
        double score = 0.0;
        
        // 应用优先级规则
        for (PriorityRule rule : priorityRules.values()) {
            if (rule.matches(content)) {
                score += rule.score;
            }
        }
        
        // 位置权重（开头和结尾更重要）
        double positionWeight = calculatePositionWeight(position, totalSegments);
        score += positionWeight * 20;
        
        // 长度权重（适中长度的内容更重要）
        double lengthWeight = calculateLengthWeight(content.length());
        score += lengthWeight * 10;
        
        // 根据压缩级别调整分数
        score = adjustScoreByLevel(score, level);
        
        // 检查是否包含优先级标记
        Matcher markerMatcher = PRIORITY_MARKER_PATTERN.matcher(content);
        if (markerMatcher.find()) {
            String marker = markerMatcher.group(1);
            if (marker != null) {
                switch (marker) {
                    case "CRITICAL":
                        score += 1000;
                        break;
                    case "HIGH":
                        score += 500;
                        break;
                    case "MEDIUM":
                        score += 200;
                        break;
                    case "LOW":
                        score += 50;
                        break;
                    case "INFO":
                        score += 10;
                        break;
                }
            }
        }
        
        return score;
    }
    
    /**
     * 计算位置权重
     */
    private double calculatePositionWeight(int position, int total) {
        if (total <= 1) {
            return 1.0;
        }
        
        // 开头和结尾权重最高
        if (position == 0 || position == total - 1) {
            return 1.0;
        }
        
        // 前1/3和后1/3权重较高
        double relativePosition = (double) position / total;
        if (relativePosition < 0.33 || relativePosition > 0.67) {
            return 0.7;
        }
        
        // 中间部分权重较低
        return 0.4;
    }
    
    /**
     * 计算长度权重
     */
    private double calculateLengthWeight(int length) {
        // 太短或太长的内容权重较低
        if (length < 20) {
            return 0.3;
        } else if (length < 50) {
            return 0.6;
        } else if (length < 200) {
            return 1.0;
        } else if (length < 500) {
            return 0.8;
        } else {
            return 0.5;
        }
    }
    
    /**
     * 根据压缩级别调整分数
     */
    private double adjustScoreByLevel(double score, CompressionLevel level) {
        switch (level) {
            case EXTREME:
                // 极限压缩：大幅提高阈值
                return score * 0.3;
            case HEAVY:
                // 重度压缩：提高阈值
                return score * 0.5;
            case MODERATE:
                // 中度压缩：适度调整
                return score * 0.7;
            case LIGHT:
                // 轻度压缩：小幅调整
                return score * 0.9;
            default:
                return score;
        }
    }
    
    /**
     * 根据优先级选择段落
     */
    private List<ContentSegment> selectSegmentsByPriority(
            List<ContentSegment> segments,
            long targetTokens,
            CompressionLevel level) {
        
        // 根据优先级排序
        List<ContentSegment> sortedSegments = new ArrayList<>(segments);
        sortedSegments.sort((a, b) -> Double.compare(b.priority, a.priority));
        
        // 根据压缩级别确定保留阈值
        double threshold = calculateThreshold(sortedSegments, level);
        
        // 选择高于阈值的段落
        List<ContentSegment> selected = new ArrayList<>();
        long currentTokens = 0;
        
        for (ContentSegment segment : sortedSegments) {
            // 检查是否必须保留（优先级极高）
            if (segment.priority >= 1000) {
                selected.add(segment);
                currentTokens += segment.getTokenCount();
                continue;
            }
            
            // 检查是否超过目标Token数
            long segmentTokens = segment.getTokenCount();
            if (currentTokens + segmentTokens > targetTokens) {
                // 如果是第一个段落，至少保留部分内容
                if (selected.isEmpty()) {
                    String truncated = truncateContent(segment.content, targetTokens);
                    segment.content = truncated;
                    selected.add(segment);
                }
                break;
            }
            
            // 检查是否高于阈值
            if (segment.priority >= threshold) {
                selected.add(segment);
                currentTokens += segmentTokens;
            }
        }
        
        // 按原始顺序重新排列
        selected.sort(Comparator.comparingInt(s -> s.position));
        
        return selected;
    }
    
    /**
     * 计算保留阈值
     */
    private double calculateThreshold(List<ContentSegment> segments, CompressionLevel level) {
        if (segments.isEmpty()) {
            return 0.0;
        }
        
        // 获取所有优先级分数
        List<Double> scores = segments.stream()
            .map(s -> s.priority)
            .sorted(Comparator.reverseOrder())
            .collect(Collectors.toList());
        
        // 根据压缩级别确定保留比例
        double retentionRatio = level.getRetentionRatio();
        int keepCount = Math.max(1, (int)(scores.size() * retentionRatio));
        
        // 返回第keepCount个分数作为阈值
        if (keepCount >= scores.size()) {
            return 0.0;
        }
        
        return scores.get(keepCount - 1);
    }
    
    /**
     * 重建内容
     */
    private String rebuildContent(List<ContentSegment> segments, CompressionLevel level) {
        if (segments.isEmpty()) {
            return "";
        }
        
        StringBuilder builder = new StringBuilder();
        
        // 添加压缩标记
        if (level.isSevereCompression()) {
            builder.append("[优先级压缩 - 保留高优先级内容]\n\n");
        }
        
        // 记录被移除的段落位置
        Set<Integer> includedPositions = segments.stream()
            .map(s -> s.position)
            .collect(Collectors.toSet());
        
        int lastPosition = -1;
        for (ContentSegment segment : segments) {
            // 如果有跳过的段落，添加省略标记
            if (lastPosition >= 0 && segment.position > lastPosition + 1) {
                builder.append("\n[...已省略低优先级内容...]\n\n");
            }
            
            builder.append(segment.content);
            builder.append("\n\n");
            
            lastPosition = segment.position;
        }
        
        return builder.toString().trim();
    }
    
    /**
     * 截断内容
     */
    private String truncateContent(String content, long maxTokens) {
        long currentTokens = tokenCalculator.calculate(content);
        if (currentTokens <= maxTokens) {
            return content;
        }
        
        // 二分查找合适的截断点
        int left = 0;
        int right = content.length();
        String result = "";
        
        while (left < right) {
            int mid = (left + right) / 2;
            String truncated = content.substring(0, mid);
            long tokens = tokenCalculator.calculate(truncated);
            
            if (tokens <= maxTokens) {
                result = truncated;
                left = mid + 1;
            } else {
                right = mid;
            }
        }
        
        // 添加省略标记
        if (!result.isEmpty() && result.length() < content.length()) {
            result += "...";
        }
        
        return result;
    }
    
    /**
     * 添加自定义优先级规则
     */
    public void addPriorityRule(String name, PriorityRule rule) {
        priorityRules.put(name, rule);
        log.info("Added priority rule: {}", name);
    }
    
    /**
     * 移除优先级规则
     */
    public void removePriorityRule(String name) {
        priorityRules.remove(name);
        log.info("Removed priority rule: {}", name);
    }
    
    /**
     * 获取所有优先级规则
     */
    public Map<String, PriorityRule> getPriorityRules() {
        return new HashMap<>(priorityRules);
    }
    
    /**
     * 确定压缩级别
     */
    private CompressionLevel determineCompressionLevel(double compressionRatio) {
        if (compressionRatio >= 0.8) {
            return CompressionLevel.LIGHT;
        } else if (compressionRatio >= 0.6) {
            return CompressionLevel.MODERATE;
        } else if (compressionRatio >= 0.4) {
            return CompressionLevel.HEAVY;
        } else {
            return CompressionLevel.EXTREME;
        }
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String content, long targetTokens) {
        int contentHash = content.hashCode();
        int rulesHash = priorityRules.hashCode();
        return String.format("%s_%d_%d_%d", STRATEGY_NAME, contentHash, targetTokens, rulesHash);
    }
    
    /**
     * 缓存结果
     */
    private void cacheResult(String key, CompressionResult result) {
        // 限制缓存大小
        if (cache.size() >= MAX_CACHE_SIZE) {
            // 简单的LRU策略：移除最早的条目
            Iterator<String> iterator = cache.keySet().iterator();
            if (iterator.hasNext()) {
                iterator.next();
                iterator.remove();
            }
        }
        cache.put(key, result);
    }
    
    /**
     * 创建空结果
     */
    private CompressionResult createEmptyResult(Instant startTime) {
        CompressionResult result = CompressionResult.builder()
            .content("")
            .tokenCount(0)
            .originalTokenCount(0)
            .compressionRatio(1.0)
            .compressionLevel(CompressionLevel.NONE)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        result.calculateDuration();
        return result;
    }
    
    /**
     * 创建未压缩结果
     */
    private CompressionResult createUncompressedResult(String content, long tokenCount, Instant startTime) {
        CompressionResult result = CompressionResult.builder()
            .content(content)
            .tokenCount(tokenCount)
            .originalTokenCount(tokenCount)
            .compressionRatio(1.0)
            .compressionLevel(CompressionLevel.NONE)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        result.calculateDuration();
        result.addMetadata("reason", "Content already within target tokens");
        return result;
    }
    
    /**
     * 构建压缩结果
     */
    private CompressionResult buildCompressionResult(
            String compressedContent,
            long originalTokenCount,
            CompressionLevel level,
            Instant startTime) {
        
        long compressedTokenCount = tokenCalculator.calculate(compressedContent);
        
        CompressionResult result = CompressionResult.builder()
            .content(compressedContent)
            .tokenCount(compressedTokenCount)
            .originalTokenCount(originalTokenCount)
            .compressionLevel(level)
            .strategyName(STRATEGY_NAME)
            .startTime(startTime)
            .endTime(Instant.now())
            .success(true)
            .build();
        
        result.calculateCompressionRatio();
        result.calculateDuration();
        
        // 添加统计信息
        result.addMetadata("compressionLevel", level.getDescription());
        result.addMetadata("tokenReduction", result.getTokenReduction());
        result.addMetadata("reductionPercentage", String.format("%.1f%%", result.getTokenReductionPercentage()));
        result.addMetadata("rulesApplied", priorityRules.size());
        
        // 记录保留的关键信息类型
        for (Map.Entry<String, PriorityRule> entry : priorityRules.entrySet()) {
            if (entry.getValue().matches(compressedContent)) {
                result.addPreservedKeyInfo(entry.getValue().description);
            }
        }
        
        log.info("Priority compression completed: {}", result.getSummary());
        
        return result;
    }
    
    @Override
    public boolean canCompress(ContextLayer layer) {
        // 优先级压缩策略可以压缩所有可压缩的层
        return layer != null && layer.isCompressible();
    }
    
    @Override
    public int getCompressionPriority() {
        return COMPRESSION_PRIORITY;
    }
    
    @Override
    public String getName() {
        return STRATEGY_NAME;
    }
    
    @Override
    public String getDescription() {
        return "优先级压缩策略：根据内容优先级选择性保留重要信息";
    }
    
    /**
     * 内容段落类
     */
    private class ContentSegment {
        String content;
        final int position;
        double priority;
        Long tokenCount;
        
        ContentSegment(String content, int position) {
            this.content = content;
            this.position = position;
            this.priority = 0.0;
        }
        
        void setPriority(double priority) {
            this.priority = priority;
        }
        
        long getTokenCount() {
            if (tokenCount == null) {
                tokenCount = tokenCalculator.calculate(content);
            }
            return tokenCount;
        }
    }
    
    /**
     * 优先级规则类
     */
    public static class PriorityRule {
        private final String description;
        private final double score;
        private final java.util.function.Predicate<String> matcher;
        
        public PriorityRule(String description, double score, java.util.function.Predicate<String> matcher) {
            this.description = description;
            this.score = score;
            this.matcher = matcher;
        }
        
        public boolean matches(String content) {
            return matcher.test(content);
        }
        
        public String getDescription() {
            return description;
        }
        
        public double getScore() {
            return score;
        }
    }
}