package cn.genn.graph.context.compression;

/**
 * 压缩级别枚举
 * 定义了不同程度的压缩级别
 * 
 * <AUTHOR>
 */
public enum CompressionLevel {
    
    /**
     * 无压缩 - 保留100%内容
     */
    NONE(1.0, "无压缩", 0),
    
    /**
     * 轻度压缩 - 保留80%内容
     */
    LIGHT(0.8, "轻度压缩", 1),
    
    /**
     * 中度压缩 - 保留60%内容
     */
    MODERATE(0.6, "中度压缩", 2),
    
    /**
     * 重度压缩 - 保留40%内容
     */
    HEAVY(0.4, "重度压缩", 3),
    
    /**
     * 极限压缩 - 保留20%内容
     */
    EXTREME(0.2, "极限压缩", 4);
    
    /**
     * 内容保留比例
     */
    private final double retentionRatio;
    
    /**
     * 级别描述
     */
    private final String description;
    
    /**
     * 级别序号（用于比较）
     */
    private final int ordinal;
    
    CompressionLevel(double retentionRatio, String description, int ordinal) {
        this.retentionRatio = retentionRatio;
        this.description = description;
        this.ordinal = ordinal;
    }
    
    /**
     * 获取内容保留比例
     * @return 保留比例（0.0-1.0）
     */
    public double getRetentionRatio() {
        return retentionRatio;
    }
    
    /**
     * 获取级别描述
     * @return 描述文本
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取级别序号
     * @return 序号值
     */
    public int getOrdinal() {
        return ordinal;
    }
    
    /**
     * 判断是否需要压缩
     * @return true表示需要压缩
     */
    public boolean needsCompression() {
        return this != NONE;
    }
    
    /**
     * 判断是否为严重压缩（重度或极限）
     * @return true表示严重压缩
     */
    public boolean isSevereCompression() {
        return this == HEAVY || this == EXTREME;
    }
    
    /**
     * 根据Token使用率自动选择压缩级别
     * 
     * @param usageRatio Token使用率（已用/最大）
     * @return 推荐的压缩级别
     */
    public static CompressionLevel fromUsageRatio(double usageRatio) {
        if (usageRatio <= 0.6) {
            return NONE;
        } else if (usageRatio <= 0.75) {
            return LIGHT;
        } else if (usageRatio <= 0.85) {
            return MODERATE;
        } else if (usageRatio <= 0.95) {
            return HEAVY;
        } else {
            return EXTREME;
        }
    }
    
    /**
     * 获取下一个更严格的压缩级别
     * @return 下一级别，如果已是最严格则返回自身
     */
    public CompressionLevel nextLevel() {
        switch (this) {
            case NONE:
                return LIGHT;
            case LIGHT:
                return MODERATE;
            case MODERATE:
                return HEAVY;
            case HEAVY:
                return EXTREME;
            default:
                return EXTREME;
        }
    }
    
    /**
     * 获取上一个更宽松的压缩级别
     * @return 上一级别，如果已是最宽松则返回自身
     */
    public CompressionLevel previousLevel() {
        switch (this) {
            case EXTREME:
                return HEAVY;
            case HEAVY:
                return MODERATE;
            case MODERATE:
                return LIGHT;
            case LIGHT:
                return NONE;
            default:
                return NONE;
        }
    }
}