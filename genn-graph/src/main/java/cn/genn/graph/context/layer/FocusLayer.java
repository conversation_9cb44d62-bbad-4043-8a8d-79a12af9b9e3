package cn.genn.graph.context.layer;

import cn.genn.core.model.KVStruct;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.graph.tot.ToTStructure;
import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.ToolCallback;

import java.io.Serializable;
import java.util.*;

import static cn.genn.graph.context.layer.XMLTagUtils.*;

/**
 * 焦点层实现
 * 对应 AgentContextState.TaskFocusContext
 * 为当前思维节点的单步推理提供必需的、即时的执行上下文
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FocusLayer implements ContextLayer, Serializable, Cloneable {
    
    private static final long serialVersionUID = 1L;
    
    // ==================== 执行指令 (Execution Directives) ====================
    
    /**
     * 定义当前智能体的核心角色、目标和行为约束
     */
    private String agentDefinition;
    
    /**
     * 当前轮次用户问题 - 当前轮次用户提出的问题
     */
    private String currentUserQuestion;
    
    /**
     * 用户问题历史 - 多轮对话中用户问题的历史记录
     */
    @Builder.Default
    private List<ConversationTurn> userQuestionHistories = new ArrayList<>();
    
    /**
     * 人类补充信息历史 - 多轮对话中人类补充信息的历史记录
     */
    @Builder.Default
    private List<UserFillInfo> userFillInfos = new ArrayList<>();
    
    /**
     * 上游节点指令 - 由上游节点传递给当前节点的具体、可执行的子任务指令
     */
    private String upstreamPrompt;
    
    // ==================== 执行状态 (Execution State) ====================
    
    /**
     * 当前agent ToT 思维链
     */
    @Builder.Default
    private ToTStructure toTStructure = new ToTStructure();
    
    /**
     * 当前执行的tot节点id
     */
    private String currToTNodeId;
    
    /**
     * 任务执行记录 - 当前任务的关键进展摘要和状态更新
     */
    private String taskExecutionRecord;
    
    // ==================== 可用资源 (Available Resources) ====================
    
    /**
     * 基础Agent集 - 当前智能体的内置基础工具（Tools）
     */
    @Builder.Default
    @JsonIgnore
    private List<ToolCallback> baseToolkit = new ArrayList<>();
    
    /**
     * 动态Agent集 - 当前步骤引用的智能体所能调用的子Agent（Tools）
     */
    @Builder.Default
    @JsonIgnore
    private List<ToolCallback> dynamicAgentToolkit = new ArrayList<>();
    
    /**
     * 工具执行结果 - 子Agent输出的核心数据或摘要，作为任务补充信息
     * key -> 已执行agent名称
     * value -> 每次产出物详细内容
     */
    @Builder.Default
    private Map<String, List<ToolExecuteResult>> toolExecuteResults = new LinkedHashMap<>();
    
    // ==================== 层管理属性 ====================
    
    /**
     * Token数量
     */
    private long tokenCount;
    
    /**
     * 原始内容（用于内容设置）
     */
    private String rawContent;
    
    
    @Override
    public String getName() {
        return "focus";
    }
    
    @Override
    public int getPriority() {
        return LayerPriority.FOCUS.getValue();
    }
    
    @Override
    public boolean isCompressible() {
        return false; // 焦点层永不压缩
    }
    
    @Override
    public String getContent() {
        return formatContent();
    }
    
    @Override
    public void setContent(String content) {
        this.rawContent = content;
        parseContent(content);
    }
    
    @Override
    public FocusLayer clone() {
        try {
            FocusLayer cloned = (FocusLayer) super.clone();
            // 深拷贝集合类型
            cloned.taskExecutionRecord = this.taskExecutionRecord;
            cloned.dynamicAgentToolkit = new ArrayList<>(this.dynamicAgentToolkit);
            cloned.toolExecuteResults = new HashMap<>(this.toolExecuteResults);
            cloned.toTStructure = this.toTStructure; // ToTStructure 可能需要自己的克隆方法
            cloned.userQuestionHistories = new ArrayList<>(this.userQuestionHistories);
            return cloned;
        } catch (CloneNotSupportedException e) {
            log.error("Failed to clone FocusLayer", e);
            return FocusLayer.builder()
                    .agentDefinition(this.agentDefinition)
                    .currentUserQuestion(this.currentUserQuestion)
                    .userQuestionHistories(new ArrayList<>(this.userQuestionHistories))
                    .upstreamPrompt(this.upstreamPrompt)
                    .toTStructure(this.toTStructure)
                    .taskExecutionRecord(this.taskExecutionRecord)
                    .dynamicAgentToolkit(new ArrayList<>(this.dynamicAgentToolkit))
                    .toolExecuteResults(new HashMap<>(this.toolExecuteResults))
                    .tokenCount(this.tokenCount)
                    .rawContent(this.rawContent)
                    .build();
        }
    }
    
    /**
     * 格式化内容为XML结构
     */
    private String formatContent() {
        StringBuilder sb = new StringBuilder();
        
        // 智能体定义部分（短标签）
        if (agentDefinition != null && !agentDefinition.isEmpty()) {
            sb.append(wrapContentWithNewLine("agent_def", agentDefinition));
        }
        
        // 对话历史（最近几轮）统一结构，避免JSON
        if (!userQuestionHistories.isEmpty()) {
            sb.append(openTag("user_question_list")).append("\n");
            for (ConversationTurn turn : userQuestionHistories) {
                String ts = turn.getTimestamp() == null ? "" : turn.getTimestamp();
                String uq = turn.getUserQuestion() == null ? "" : turn.getUserQuestion();
                sb.append(openTagWithAttr("question", "time", ts)).append(uq).append(closeTag("question")).append("\n");
            }
            sb.append(closeTag("user_question_list")).append("\n");
        }
        
        // ToT 思维链部分（短标签）
        if (toTStructure != null) {
            sb.append(wrapContentWithNewLine("tot_structure", JsonUtils.toJsonNotNull(toTStructure)));
        }
        
        // 当前执行的totId（短标签）
        if (currToTNodeId != null) {
            sb.append(wrapContentWithNewLine("curr_tot_node_id", currToTNodeId));
        }
        
        // 上游上下文部分（短标签）
        if (upstreamPrompt != null && !upstreamPrompt.isEmpty()) {
            sb.append(wrapContentWithNewLine("upstream_prompt", upstreamPrompt));
        }

        // 任务执行记录（短标签）
        if (CharSequenceUtil.isNotEmpty(taskExecutionRecord)) {
            sb.append(wrapContentWithNewLine("task_exec_rec", taskExecutionRecord));
        }
        
        // 人类补充信息历史（短标签）
        if (!userFillInfos.isEmpty()) {
            sb.append(openTag("user_fill_infos")).append("\n");
            sb.append(JsonUtils.toJson(userFillInfos)).append("\n");
            sb.append(closeTag("user_fill_infos")).append("\n");
        }

        // 下游产出物（短标签）
        if (!toolExecuteResults.isEmpty()) {
            sb.append(openTag("tool_exec_res")).append("\n");
            sb.append(JsonUtils.toJson(toolExecuteResults)).append("\n");
            sb.append(closeTag("tool_exec_res")).append("\n");
        }

        // 如果有原始内容且上述格式化内容为空，则返回原始内容
        if (sb.isEmpty() && rawContent != null) {
            return wrapContent("rawContent", rawContent);
        }

        return sb.toString();
    }
    
    
    /**
     * 解析内容并更新字段
     * 这是一个简化的实现，实际使用时可能需要更复杂的解析逻辑
     */
    private void parseContent(String content) {
        if (content == null || content.isEmpty()) {
            return;
        }
        
        // 这里可以实现更复杂的内容解析逻辑
        // 例如：从结构化文本中提取各个部分
        // 当前简单地将整个内容作为当前用户问题
        if (this.currentUserQuestion == null || this.currentUserQuestion.isEmpty()) {
            this.currentUserQuestion = content;
        }
    }
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ToolExecuteResult {
        private String executeTime;
        private String result;
    }
    
    /**
     * 对话轮次记录
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ConversationTurn {
        /**
         * 用户问题
         */
        private String userQuestion;
        
        /**
         * 轮次时间戳
         */
        private String timestamp;
        
    }
    
    /**
     * 用户补充信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserFillInfo {
        private List<KVStruct<String, String>> infoList;
    }
    
    
    
}