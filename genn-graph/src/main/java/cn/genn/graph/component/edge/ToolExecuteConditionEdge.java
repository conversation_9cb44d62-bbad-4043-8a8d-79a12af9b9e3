package cn.genn.graph.component.edge;

import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.context.ContextUtils;
import cn.genn.graph.context.layer.InstantContext;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.EdgeAction;

/**
 * 判断模型返回的工具情况
 * 
 * <AUTHOR>
 */
public class ToolExecuteConditionEdge implements EdgeAction {
    
    public static final String CONTINUE = "continue";
    public static final String HUMAN = "human";
    public static final String TERMINATE = "terminate";

    @Override
    public String apply(OverAllState state) throws Exception {
        AgentContextState agentContextState = ContextUtils.getContext(state);
        InstantContext instant = agentContextState.getInstant();
        if (instant.isShouldTerminate()) {
            return TERMINATE;
        }
        if (instant.getHumanToolResult() != null) {
            return HUMAN;
        }
        return CONTINUE;
    }
}
