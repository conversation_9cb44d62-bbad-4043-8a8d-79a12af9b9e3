package cn.genn.graph.component.node.tot;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.core.StateKey;
import cn.genn.graph.tools.ToolUtils;
import cn.genn.graph.tot.ToTStructure;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * ToT解析节点
 * 负责解析ToT原始JSON信息为结构化对象
 *
 * 输入：
 * - tot.origin_info: ToT原始JSON信息
 *
 * 输出：
 * - tot.structure: 解析得到的ToT结构
 *
 * <AUTHOR>
 */
@Slf4j
public class ToTParserNode implements NodeAction {
    
    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        log.info("Executing ToT Parser Node");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 使用StateKey枚举获取ToT原始信息
            String totJson = state.value(StateKey.TOT_ORIGIN_INFO.getKey(), "");
            
            // 解析ToT结构
            ToTStructure totStructure = parseToTStructure(totJson);
            if (totStructure == null) {
                throw new IllegalStateException("Parsed ToT structure is null");
            }

            Optional<AgentContextState> agentContextStateOptional = state.value(StateKey.CONTEXT_AGENT.getKey(), AgentContextState.class);
            agentContextStateOptional.ifPresent(agentContextState -> {
                agentContextState.getFocus().setToTStructure(totStructure);
                agentContextState.getFocus().setDynamicAgentToolkit(ToolUtils.filterTool(totStructure.initializeToolList(), agentContextState.getInstant().getAllToolCallbacks()));
                result.put(StateKey.CONTEXT_AGENT.getKey(), agentContextState);
            });

            result.put(StateKey.TOT_STRUCTURE.getKey(), totStructure);

        } catch (Exception e) {
            log.error("ToT parsing failed", e);
            throw e;
        }
        return result;
    }
    
    /**
     * 解析ToT结构
     */
    private ToTStructure parseToTStructure(String totJson) throws Exception {
        return JsonUtils.parse(totJson, ToTStructure.class);
    }
}
