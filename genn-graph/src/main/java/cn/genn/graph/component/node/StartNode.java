package cn.genn.graph.component.node;

import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.context.layer.InstantContext;
import cn.genn.graph.core.StateKey;
import cn.genn.graph.core.ToolCallbackResolver;
import cn.genn.graph.tools.internal.InternalToolFactory;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 开始节点,做一些初始化工作
 * <AUTHOR>
 */
public class StartNode implements NodeAction {

    private final ContextInput contextInput;

    public StartNode(ContextInput contextInput) {
        this.contextInput = contextInput;
    }
    
    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        
        Map<String, Object> result = new HashMap<>();
        
        Optional<AgentContextState> agentContextStateOptional = state.value(StateKey.CONTEXT_AGENT.getKey(), AgentContextState.class);
        if (agentContextStateOptional.isEmpty()) {
            //初始化上下文
            result.put(StateKey.CONTEXT_AGENT.getKey(), initContext(state));
        }else {
            //多轮会话更新上下文
            result.put(StateKey.CONTEXT_AGENT.getKey(), updateContextWithMultiConversation(agentContextStateOptional.get(), state));
        }
        
        return result;
    }

    private AgentContextState initContext(OverAllState state) {
        //1. 初始化焦点层
        FocusLayer focusLayer = createFocusLayer(state);
        return AgentContextState.builder()
                .focus(focusLayer)
                .instant(InstantContext.builder()
                        .allToolCallbacks(contextInput.getToolCallbackResolver().getAllToolCallbacks().stream().toList())
                        .build())
                .build();
    }

    private FocusLayer createFocusLayer(OverAllState state) {
        String userInput = state.value(StateKey.CONTEXT_USER_INPUT.getKey(), "");
        String upStreamToolInput = state.value(StateKey.TOOL_UPSTREAM_INPUT_KEY.getKey(), "");

        FocusLayer.FocusLayerBuilder builder = FocusLayer
                .builder()
                .agentDefinition(contextInput.getAgentDefinition())
                .currentUserQuestion(userInput)  // 使用新的字段名
                .upstreamPrompt(upStreamToolInput)
                .baseToolkit(InternalToolFactory.getInstance().getAllToolList());

        FocusLayer focusLayer = builder.build();
        if (userInput != null && !userInput.isEmpty()) {
            focusLayer.getUserQuestionHistories().add(
                    new FocusLayer.ConversationTurn(userInput, DateUtil.now())
            );
        }

        return focusLayer;
    }


    private AgentContextState updateContextWithMultiConversation(AgentContextState agentContextState, OverAllState state) {
        //多轮对话场景
        //焦点层需要重建
        FocusLayer focusLayer = createFocusLayer(state);
        //复制之前的会话历史到新的焦点层,添加到前面
        focusLayer.getUserQuestionHistories().addAll(0, agentContextState.getFocus().getUserQuestionHistories());
        //完善用户补充信息
        List<FocusLayer.UserFillInfo> oldFillInfos = agentContextState.getFocus().getUserFillInfos();
        if (CollUtil.isNotEmpty(oldFillInfos)) {
            focusLayer.getUserFillInfos().addAll(oldFillInfos);
        }
        Optional<FocusLayer.UserFillInfo> userFillInfoOptional = state.value(StateKey.CONTEXT_USER_FILL_INFO.getKey(), FocusLayer.UserFillInfo.class);
        userFillInfoOptional.ifPresent(info -> {
            if (CollUtil.isNotEmpty(info.getInfoList())) {
                focusLayer.getUserFillInfos().add(info);
            }
        });
        focusLayer.getUserFillInfos().addAll(oldFillInfos);
        agentContextState.setFocus(focusLayer);
        
        //更新瞬时状态
        agentContextState.getInstant().setAllToolCallbacks(contextInput.getToolCallbackResolver().getAllToolCallbacks().stream().toList());
        return agentContextState;
    }

    @Data
    @Accessors(chain = true)
    public static class ContextInput {
        private String agentDefinition;
        private ToolCallbackResolver toolCallbackResolver;
    }
}
