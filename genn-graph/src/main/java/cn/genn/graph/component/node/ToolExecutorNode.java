package cn.genn.graph.component.node;

import cn.genn.ai.tools.StreamToolCallback;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.graph.adapter.StreamingToolResultGenerator;
import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.context.ContextUtils;
import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.context.layer.PanoramicLayer;
import cn.genn.graph.core.StateKey;
import cn.genn.graph.core.ToolCallbackResolver;
import cn.genn.graph.tools.internal.HumanTool;
import cn.genn.graph.tools.internal.TerminateTool;
import cn.hutool.core.date.DateUtil;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.ToolResponseMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 工具执行节点
 * <p>
 * 支持并行工具调用、流式工具调用和结果收集
 * 流式工具优先串行执行，非流式工具并行执行
 *
 * <AUTHOR>
 */
@Slf4j
public class ToolExecutorNode implements NodeAction {

    private final ExecuteConfig executeConfig;
    private final ToolCallbackResolver toolCallbackResolver;
    private final ExecutorService executorService;

    public ToolExecutorNode(ToolCallbackResolver toolCallbackResolver, ExecuteConfig executeConfig) {
        this.toolCallbackResolver = toolCallbackResolver;
        this.executeConfig = executeConfig;
        this.executorService = executeConfig.getExecutorService();
    }

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        AgentContextState context = ContextUtils.getContext(state);
        // 获取工具调度消息
        AssistantMessage toolDispatchResult = context.getInstant().getToolDispatchResult();
        List<AssistantMessage.ToolCall> toolCalls = toolDispatchResult.getToolCalls();

        log.info("Executing {} tool calls", toolCalls.size());

        // 将工具调用分为流式和非流式两类
        Map<Boolean, List<AssistantMessage.ToolCall>> groupedCalls = groupToolCallsByStreamingSupport(toolCalls);
        List<AssistantMessage.ToolCall> streamingCalls = groupedCalls.getOrDefault(true, new ArrayList<>());
        List<AssistantMessage.ToolCall> nonStreamingCalls = groupedCalls.getOrDefault(false, new ArrayList<>());

        // 检查是否有流式工具调用
        if (!streamingCalls.isEmpty()) {
            log.info("Found {} streaming tool calls, executing in streaming mode", streamingCalls.size());
            // 如果有流式工具，返回StreamingChatGenerator
            return executeStreamingToolCalls(streamingCalls, nonStreamingCalls, state, context);
        } else {
            // 如果没有流式工具，执行普通的并行调用
            log.info("No streaming tool calls found, executing {} non-streaming calls in parallel", nonStreamingCalls.size());
            ToolResponseMessage toolResponseMessage = executeToolCallsParallel(nonStreamingCalls, state);
            updateContext(toolResponseMessage, context);

            Map<String, Object> updatedState = new HashMap<>();
            updatedState.put(StateKey.CONTEXT_AGENT.getKey(), context);
            log.info("Tool execution completed successfully with {} responses",
                    toolResponseMessage.getResponses().size());
            
            return updatedState;
        }
    }

    /**
     * 根据工具是否支持流式调用进行分组
     */
    private Map<Boolean, List<AssistantMessage.ToolCall>> groupToolCallsByStreamingSupport(
            List<AssistantMessage.ToolCall> toolCalls) {
        return toolCalls.stream().collect(Collectors.groupingBy(toolCall -> {
            ToolCallback toolCallback = resolveTool(toolCall.name());
            if (toolCallback instanceof StreamToolCallback streamTool) {
                return streamTool.enabledStreaming();
            }
            return false;
        }));
    }

    /**
     * 执行流式工具调用
     * 流式工具串行执行，非流式工具并行执行
     */
    private Map<String, Object> executeStreamingToolCalls(
            List<AssistantMessage.ToolCall> streamingCalls,
            List<AssistantMessage.ToolCall> nonStreamingCalls,
            OverAllState state,
            AgentContextState context) throws Exception {
        
        // 存储非流式工具的执行结果
        List<ToolResponseMessage.ToolResponse> nonStreamingResponses = new ArrayList<>();
        
        // 先并行执行所有非流式工具（如果有）
        if (!nonStreamingCalls.isEmpty()) {
            try {
                ToolResponseMessage nonStreamingResult = executeToolCallsParallel(nonStreamingCalls, state);
                nonStreamingResponses.addAll(nonStreamingResult.getResponses());
                log.info("Executed {} non-streaming tools in parallel", nonStreamingCalls.size());
            } catch (Exception e) {
                log.error("Failed to execute non-streaming tools", e);
                // 即使非流式工具失败，也继续执行流式工具
                for (AssistantMessage.ToolCall toolCall : nonStreamingCalls) {
                    nonStreamingResponses.add(new ToolResponseMessage.ToolResponse(
                            toolCall.id(), toolCall.name(), "Error: " + e.getMessage()));
                }
            }
        }
        
        // 创建一个Flux来串行执行流式工具调用
        List<ToolResponseMessage.ToolResponse> streamingResponses = new ArrayList<>();
        StringBuilder combinedStreamOutput = new StringBuilder();
        
        Flux<String> streamFlux = Flux.defer(() -> {
            // 串行执行流式工具
            Flux<String> combinedFlux = Flux.empty();
            
            for (int i = 0; i < streamingCalls.size(); i++) {
                AssistantMessage.ToolCall toolCall = streamingCalls.get(i);

                // 为每个流式工具创建一个收集器
                StringBuilder toolOutput = new StringBuilder();
                
                Flux<String> singleStreamFlux = executeStreamingToolCall(toolCall, state)
                        .doOnNext(chunk -> {
                            toolOutput.append(chunk);
                            combinedStreamOutput.append(chunk);
                        })
                        .doOnComplete(() -> {
                            // 当单个工具完成时，保存其结果
                            streamingResponses.add(new ToolResponseMessage.ToolResponse(
                                    toolCall.id(), toolCall.name(), toolOutput.toString()));
                            log.debug("Completed streaming tool {}: {}", toolCall.name(), 
                                    toolOutput.length() > 100 ? toolOutput.substring(0, 100) + "..." : toolOutput);
                        })
                        .onErrorResume(error -> {
                            log.error("Stream tool execution failed for {}: {}", 
                                    toolCall.name(), error.getMessage(), error);
                            String errorMsg = "Error: " + error.getMessage();
                            streamingResponses.add(new ToolResponseMessage.ToolResponse(
                                    toolCall.id(), toolCall.name(), errorMsg));
                            return Flux.just(errorMsg);
                        });
                
                // 如果不是第一个流式工具，添加分隔符
                if (i > 0) {
                    singleStreamFlux = Flux.just("\n\n---\n\n").concatWith(singleStreamFlux);
                }
                
                // 串行连接每个流式工具的输出
                combinedFlux = combinedFlux.concatWith(singleStreamFlux);
            }
            
            return combinedFlux;
        });
        
        // 使用StreamingChatGenerator包装流式输出
        var generator = StreamingToolResultGenerator.builder()
                .startingNode("toolExecutor")
                .startingState(state)
                .mapResult(result -> {
                    // 当流式执行完成后，合并所有结果并更新上下文
                    List<ToolResponseMessage.ToolResponse> allResponses = new ArrayList<>();
                    allResponses.addAll(nonStreamingResponses);
                    allResponses.addAll(streamingResponses);
                    
                    ToolResponseMessage toolResponseMessage = new ToolResponseMessage(allResponses, Map.of());
                    updateContext(toolResponseMessage, context);
                    
                    Map<String, Object> updatedState = new HashMap<>();
                    updatedState.put(StateKey.CONTEXT_AGENT.getKey(), context);
                    
                    log.info("Stream tool execution completed with {} total responses", allResponses.size());
                    return updatedState;
                })
                .build(streamFlux);
        
        return Map.of(StateKey.CONTEXT_AGENT.getKey(), context, 
                     StateKey.RESULT_FINAL.getKey(), generator);
    }

    /**
     * 执行单个流式工具调用
     */
    private Flux<String> executeStreamingToolCall(AssistantMessage.ToolCall toolCall, OverAllState state) {
        return Flux.defer(() -> {
            try {
                String toolName = toolCall.name();
                String toolArgs = toolCall.arguments();
                
                log.debug("Executing streaming tool call: {} with args: {}", toolName, toolArgs);
                
                ToolCallback toolCallback = resolveTool(toolName);
                if (!(toolCallback instanceof StreamToolCallback streamTool)) {
                    String errorMsg = "Tool does not support streaming: " + toolName;
                    log.error(errorMsg);
                    return Flux.error(new IllegalStateException(errorMsg));
                }

                // 创建工具上下文
                ToolContext toolContext = new ToolContext(Map.of("state", state));
                
                // 执行流式工具调用
                return streamTool.callStream(toolArgs, toolContext)
                        .doOnSubscribe(sub -> log.debug("Started streaming tool call: {}", toolName))
                        .doOnComplete(() -> log.debug("Completed streaming tool call: {}", toolName))
                        .onErrorResume(error -> {
                            log.error("Stream tool execution error for {}: {}", toolName, error.getMessage(), error);
                            return Flux.just("Error: " + error.getMessage());
                        });
                        
            } catch (Exception e) {
                String errorMsg = "Failed to execute streaming tool: " + e.getMessage();
                log.error("Stream tool call setup error for {}: {}", toolCall.name(), errorMsg, e);
                return Flux.error(new RuntimeException(errorMsg, e));
            }
        });
    }

    /**
     * 并行执行工具调用
     */
    private ToolResponseMessage executeToolCallsParallel(List<AssistantMessage.ToolCall> toolCalls,
                                                         OverAllState state) throws Exception {

        // 创建并行任务
        List<CompletableFuture<ToolResponseMessage.ToolResponse>> futures = toolCalls.stream()
                .map(toolCall -> CompletableFuture.supplyAsync(
                        () -> executeToolCallSafely(toolCall, state), executorService))
                .toList();

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        // 设置超时
        Duration timeout = getExecutionTimeout();
        allFutures.get(timeout.toMillis(), TimeUnit.MILLISECONDS);

        // 收集结果
        List<ToolResponseMessage.ToolResponse> toolResponses = new ArrayList<>();
        for (CompletableFuture<ToolResponseMessage.ToolResponse> future : futures) {
            try {
                toolResponses.add(future.get());
            } catch (ExecutionException e) {
                // 处理单个工具执行异常
                Throwable cause = e.getCause();
                log.error("Tool execution failed in parallel mode", cause);
                // 继续收集其他结果，不中断整个流程
            }
        }

        return new ToolResponseMessage(toolResponses, Map.of());

    }

    private Duration getExecutionTimeout() {
        return executeConfig.getTimeout();
    }

    /**
     * 安全执行单个工具调用
     */
    private ToolResponseMessage.ToolResponse executeToolCallSafely(AssistantMessage.ToolCall toolCall,
                                                                   OverAllState state) {
        try {
            String toolName = toolCall.name();
            String toolArgs = toolCall.arguments();

            log.debug("Executing tool call: {} with args: {}", toolName, toolArgs);

            ToolCallback toolCallback = resolveTool(toolName);
            if (toolCallback == null) {
                String errorMsg = "Tool not found: " + toolName;
                log.error(errorMsg);
                return new ToolResponseMessage.ToolResponse(toolCall.id(), toolName, "Error: " + errorMsg);
            }

            // 创建工具上下文
            // todo 考虑传递更多上下文
            ToolContext toolContext = new ToolContext(Map.of("state", state));

            // 执行工具调用
            String toolResult = toolCallback.call(toolArgs, toolContext);

            log.debug("Tool call completed: {} -> {}", toolName,
                    toolResult.length() > 100 ? toolResult.substring(0, 100) + "..." : toolResult);

            return new ToolResponseMessage.ToolResponse(toolCall.id(), toolName, toolResult);

        } catch (Exception e) {
            String errorMsg = "Tool execution failed: " + e.getMessage();
            log.error("Tool call execution error for {}: {}", toolCall.name(), errorMsg, e);
            return new ToolResponseMessage.ToolResponse(toolCall.id(), toolCall.name(), "Error: " + errorMsg);
        }
    }

    /**
     * 解析工具
     */
    private ToolCallback resolveTool(String toolName) {
        return toolCallbackResolver.getToolCallback(toolName);
    }

    /**
     * 更新上下文
     */
    private void updateContext(ToolResponseMessage toolResponseMessage, AgentContextState context) {
        FocusLayer focusLayer = context.getFocus();
        PanoramicLayer panoramicLayer = context.getPanorama();
        AssistantMessage.ToolCall toolCall = context.getInstant().getToolDispatchResult().getToolCalls().getFirst();
        context.getInstant().setToolExecutionResponse(toolResponseMessage);
        if (HumanTool.NAME.equals(toolCall.name())) {
            //1.1 人类节点调用,本轮对话结束
            String result = toolResponseMessage.getResponses().getFirst().responseData();
            HumanTool.HumanToolResult humanToolResult = JsonUtils.parse(result, HumanTool.HumanToolResult.class);
            context.getInstant()
                    .setHumanToolResult(humanToolResult)
                    .setFinalResult(humanToolResult.getPlainText());
            List<Message> keyInteractionLogs = panoramicLayer.getKeyInteractionLogs();
            //加入一问一答
            keyInteractionLogs.addAll(List.of(
                    new UserMessage(focusLayer.getCurrentUserQuestion()),
                    new AssistantMessage(humanToolResult.getPlainText())
            ));
            //提取关键交互日志
            List<Message> updatedKeyLogs = ContextUtils.extractKeyInteractionLogs(keyInteractionLogs);
            panoramicLayer.setKeyInteractionLogs(updatedKeyLogs);
        }else if (TerminateTool.NAME.equals(toolCall.name())) {
            //终止工具
            context.getInstant().setShouldTerminate(true);
        }else {
            // 1.2 更新下游产出物 - 从工具执行结果中提取
            for (ToolResponseMessage.ToolResponse response : toolResponseMessage.getResponses()) {
                focusLayer.getToolExecuteResults().computeIfAbsent(response.name(), k -> new ArrayList<>())
                        .add(new FocusLayer.ToolExecuteResult(DateUtil.now(), response.responseData()));
            }
        }

    }


    @Data
    @Accessors(chain = true)
    public static class ExecuteConfig {
        private Duration timeout = Duration.ofSeconds(60);
        private ExecutorService executorService;
    }
}
