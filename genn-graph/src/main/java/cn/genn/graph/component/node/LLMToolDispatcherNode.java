package cn.genn.graph.component.node;

import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.context.layer.PanoramicLayer;
import cn.genn.graph.context.prompt.PromptBuilder;
import cn.genn.graph.context.prompt.PromptResult;
import cn.genn.graph.context.token.TokenBudget;
import cn.genn.graph.core.StateKey;
import cn.genn.graph.tools.ToolUtils;
import cn.genn.graph.tools.internal.InternalToolFactory;
import cn.genn.graph.tot.ToTStructure;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.tool.ToolCallback;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * LLM工具分发节点,负责选择合适的工具,如果不需要调用工具,使用默认的终止工具
 * 
 * <AUTHOR>
 */
@Slf4j
public class LLMToolDispatcherNode implements NodeAction {

    private final ChatClient chatClient;
    
    @Setter
    private String systemPrompt = """
            你是一个智能体工具调度器，负责根据当前推理步骤（ToT 节点）以及本次请求中提供的工具列表，选择并调用最合适的工具来完成任务。
            
            你将获得以下信息（以统一的 XML 容器提供）：
            - <context priority="1" name="focus_context">... </context>
            - <context priority="2" name="panoramic_context">... </context>
            - <context priority="3" name="strategic_context">... </context>
            注意：priority 数字越小优先级越高，请优先使用 focus_context 中的信息。
            
            另外，你还会获得 ToT 当前节点信息（节点类型/提示词/可用工具清单），以及本次 function call 请求中注册的工具列表（带参数 schema）。
            
            ---
            
            ### 三层上下文标签说明（重要字段）：
            1) focus_context（当前推理步骤的核心信息与即时执行状态）
               - <agent_def>：当前智能体的核心角色定义和行为约束
               - <user_question_list>：用户问题历史（统一结构）
                   例如：<question time="..."></question>
               - <tot_structure>：当前思维树（ToT）的完整结构（JSON文本）
               - <curr_tot_node_id>：当前正在执行的 ToT 节点 ID
               - <upstream_prompt>：上游节点传递的具体可执行子任务指令
               - <task_exec_rec>：任务执行过程中的关键进展和状态记录
               - <tool_exec_res>：已执行工具的输出结果和产出物（JSON文本）
               - <user_fill_infos>: 用户补充的信息
               - <cur_question priority="high" importance="primary">当前轮次用户问题</cur_question>
            
            2) panoramic_context（任务所需的背景信息与历史摘要）
               - <task_milestones>
                   若干 <item status="..." timestamp="...">描述</item>
               - <llm_digest>
                   若干 <summary>节点输出摘要</summary>
                   可能存在 <more_info count="n">more summaries</more_info>
               - <key_logs>: 最近的历史对话内容,一问一答
                   若干 <turn role="user|assistant">内容</turn>
                   可能存在 <more_info count="n">earlier turns omitted</more_info>
            
            3) strategic_context（长期记忆与背景知识）
               - <domain_knowledge>：领域知识（可能被压缩）
               - <downstreamProgress>：下游智能体中间过程（条目可能被截断，存在 moreInfo 计数）
               （会话历史在此层可被高度压缩或省略）
            
            ---
            
            ### 工具选择与调用规则：
            1. 只能调用同时出现在「ToT 节点工具列表」与「function call 请求工具列表」中的工具。
            2. 工具名称（name）必须完全匹配。
            3. 参数（arguments）必须严格符合各工具的参数 schema：
               - 参数名与 schema 定义一致
               - 必填字段齐全
               - 参数类型正确
            4. 若无可用工具但提供了默认结束工具（terminate_tool），则调用之。
            5. 禁止调用不存在或不匹配的工具。
            6. 若任务已完成，必须调用 terminate_tool。
            7. 除非是重试/补充信息，不得重复调用已成功执行过的工具（可参考 focus_context 中的 tool_exec_res）。
            8. 优先使用 focus_context 与 ToT 当前节点上下文进行判断；必要时参考 panoramic_context/strategic_context 提供的摘要与背景。
            
            ### 其他规则
            - 如果用户或上下文已经提供了必要信息（例如某工具的结果已在 tool_exec_res 中），请据此推进 ToT 的流转，而不是重复调用。
            
            ### 输出要求：
            - 必须产生一次有效的工具调用（function call）。
            
            请严格遵守以上规范。
            """;
    
    @Setter
    private int maxRetries = 3;

    private final PromptBuilder promptBuilder;
    
    public LLMToolDispatcherNode(ChatClient chatClient, PromptBuilder promptBuilder) {
        this.chatClient = chatClient;
        this.promptBuilder = promptBuilder;
    }
    
    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        log.info("Executing LLM Call Node");
        Optional<AgentContextState> contextOptional = state.value(StateKey.CONTEXT_AGENT.getKey(), AgentContextState.class);
        if (contextOptional.isEmpty()) {
            throw new IllegalStateException("AgentContextState is missing in the state");
        }
        AgentContextState context = contextOptional.get();
        //1. 构建本步骤的提示词
        PromptResult promptResult = promptBuilder.build(systemPrompt, context.getFocus(), context.getPanorama(), context.getStrategic(), new TokenBudget());
        UserMessage userMessage = UserMessage.builder().text(promptResult.getUserMessage()).build();
        Prompt prompt = Prompt.builder().messages(List.of(SystemMessage.builder().text(promptResult.getSystemPrompt()).build(), userMessage)).build();
        //2. 调用LLM
        ChatClient.ChatClientRequestSpec clientRequestSpec = chatClient
                .prompt(prompt);
        List<ToolCallback> tools = new ArrayList<>(context.getFocus().getBaseToolkit());
        List<ToolCallback> dynamicAgentToolkit = context.getFocus().getDynamicAgentToolkit();
        if (CollUtil.isNotEmpty(dynamicAgentToolkit)) {
            tools.addAll(dynamicAgentToolkit);
        }
        ChatResponse chatResponse = clientRequestSpec
                .toolCallbacks(tools)
                .call()
                .chatResponse();
        //3. 解析LLM返回结果并检查工具调用
        int retryCount = 0;
        
        while (retryCount < maxRetries) {
            //4. 判断是否包含工具调用
            boolean hasToolCalls = chatResponse != null && chatResponse.getResult().getOutput().hasToolCalls();
            
            if (hasToolCalls) {
                log.info("Tool calls detected in LLM response");
                break;
            } else {
                retryCount++;
                log.warn("No tool calls detected in LLM response, retry count: {}/{}", retryCount, maxRetries);
                
                if (retryCount < maxRetries) {
                    // 重新调用LLM
                    chatResponse = clientRequestSpec
                            .call()
                            .chatResponse();
                } else {
                    // 达到最大重试次数，抛出异常
                    throw new RuntimeException("Failed to get tool calls from LLM after " + maxRetries + " retries");
                }
            }
        }

        assert chatResponse != null;
        
        updateContext(context, chatResponse);

        return Map.of(StateKey.CONTEXT_AGENT.getKey(), context);
    }

    private void updateContext(AgentContextState context, ChatResponse chatResponse) {
        FocusLayer focusLayer = context.getFocus();
        PanoramicLayer panoramicLayer = context.getPanorama();
        AssistantMessage assistantMessage = chatResponse.getResult().getOutput();
        context.getInstant().setToolDispatchResult(assistantMessage);

        //1. 更新焦点层 
        //1.1 tot节点和动态工具集
        AssistantMessage.ToolCall toolCall = assistantMessage.getToolCalls().getFirst();
        List<ToolCallback> baseTools = InternalToolFactory.getInstance().getAllToolList();
        if (!ToolUtils.isBaseTool(toolCall.name(), baseTools)) {
            ToTStructure toTStructure = focusLayer.getToTStructure();
            focusLayer.setCurrToTNodeId(toTStructure.findNodeByToolName(toolCall.name()).getId());
            focusLayer.setDynamicAgentToolkit(ToolUtils.filterTool(toTStructure.extractToolList(focusLayer.getCurrToTNodeId()), context.getInstant().getAllToolCallbacks()));
        }
        //2. 更新全景层
        //2.1 更新LLM输出摘要
        String text = assistantMessage.getText();
        panoramicLayer.getLlmOutputDigest().add(text);
    }

}
