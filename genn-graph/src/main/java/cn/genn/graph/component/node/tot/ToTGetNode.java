package cn.genn.graph.component.node.tot;

import cn.genn.graph.core.StateKey;
import cn.genn.graph.tot.ToTQueryService;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;

import java.util.Map;

/**
 * ToT获取节点
 * 负责获取ToT（Tree of Thoughts）的原始信息
 *
 * <AUTHOR>
 */
public class ToTGetNode implements NodeAction {
    
    private final String totOriginInfo;

    public ToTGetNode(String totOriginInfo) {
        this.totOriginInfo = totOriginInfo;
    }
    
    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        // 使用StateKey枚举来获取key
        return Map.of(StateKey.TOT_ORIGIN_INFO.getKey(), totOriginInfo);
    }
    
}
