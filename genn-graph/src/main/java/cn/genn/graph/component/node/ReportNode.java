package cn.genn.graph.component.node;

import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.context.ContextUtils;
import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.context.layer.PanoramicLayer;
import cn.genn.graph.context.prompt.PromptBuilder;
import cn.genn.graph.context.prompt.PromptResult;
import cn.genn.graph.context.token.TokenBudget;
import cn.genn.graph.core.StateKey;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.streaming.StreamingChatGenerator;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 报告生成节点，负责根据三层上下文信息生成最终的任务执行报告
 *
 * <AUTHOR>
 */
@Slf4j
public class ReportNode implements NodeAction {

    private final ChatClient chatClient;

    @Setter
    private String systemPrompt = """
            你是一个智能助手，负责根据用户的问题和当前的执行上下文生成最终的回答报告。
            
            ### 核心原则：
            1. **以用户问题为中心**：优先回答用户的原始问题，而不是描述执行流程
            2. **基于执行结果**：利用已执行的工具和Agent的结果来回答用户问题
            3. **直接有效**：提供用户真正需要的信息，避免冗余的技术细节
            
            ### 你将获得的信息：
            1. **用户原始问题**：从焦点层的用户指令中获取
            2. **执行结果**：从各层上下文中获取的工具执行结果和产出物
            3. **相关背景**：任务执行过程中收集的相关信息
            
            ### 回答要求：
            1. **直接回答用户问题**：
               - 基于执行结果直接回答用户的问题
               - 如果问题有多个方面，逐一清晰回答
               - 避免描述"我执行了什么工具"，而是说明"找到了什么结果"
            
            2. **结构化组织**：
               - 使用清晰的标题和段落组织内容
               - 重要信息用**粗体**突出显示
               - 必要时使用列表、表格等格式
            
            3. **内容质量**：
               - 准确：基于实际执行数据，不编造信息
               - 完整：覆盖用户问题的所有重要方面
               - 简洁：去除不必要的技术细节和流程描述
               - 实用：提供可操作的信息和建议
            
            4. **特殊情况处理**：
               - 如果无法完全回答用户问题，说明原因和已获得的部分信息
               - 如果执行过程中出现错误，重点说明对最终结果的影响
               - 如果需要用户进一步澄清，明确指出需要的信息
            
            ### 输出格式：
            - 使用Markdown格式
            - 以回答用户问题为主线组织内容
            - 长度适中，重点突出
            
            请生成一个直接、有用、以用户问题为中心的回答。
            """;

    @Setter
    private boolean stream = true;

    private final PromptBuilder promptBuilder;
    

    /**
     * 完整构造函数 - 用于需要LLM生成报告的场景
     */
    public ReportNode(ChatClient chatClient, PromptBuilder promptBuilder) {
        this.chatClient = chatClient;
        this.promptBuilder = promptBuilder;
    }

    /**
     * 执行报告节点的逻辑
     *
     * @param state 当前的整体状态
     * @return 返回执行结果
     * @throws Exception 可能抛出的异常
     */
    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        log.info("Executing Report Node");

        // 1. 获取Agent上下文状态
        Optional<AgentContextState> contextOptional = state.value(StateKey.CONTEXT_AGENT.getKey(), AgentContextState.class);
        if (contextOptional.isEmpty()) {
            throw new IllegalStateException("AgentContextState is missing in the state");
        }
        AgentContextState context = contextOptional.get();

        // 2. 检查是否有从状态中传入的自定义系统提示词

        // 4. 构建报告生成的提示词
        PromptResult promptResult = promptBuilder.build(
                systemPrompt,
                context.getFocus(),
                context.getPanorama(),
                context.getStrategic(),
                new TokenBudget()
        );

        // 5. 构建聊天消息
        UserMessage userMessage = UserMessage.builder()
                .text(promptResult.getUserMessage())
                .build();

        Prompt prompt = Prompt.builder()
                .messages(List.of(
                        SystemMessage.builder().text(promptResult.getSystemPrompt()).build(),
                        userMessage
                ))
                .build();

        // 6. 调用LLM生成报告
        if (!stream) {
            ChatResponse chatResponse = chatClient
                    .prompt(prompt)
                    .call()
                    .chatResponse();

            // 7. 提取生成的报告内容
            String report = chatResponse.getResult().getOutput().getText();
            updateContext(context, report);
            return Map.of(StateKey.RESULT_FINAL.getKey(), report, StateKey.CONTEXT_AGENT.getKey(), context);
        }else {
            Flux<ChatResponse> chatResponseFlux = chatClient
                    .prompt(prompt)
                    .stream().chatResponse();
            var generator = StreamingChatGenerator.builder()
                    .startingNode("reportNode")
                    .startingState(state)
                    .mapResult(response -> {
                        String report = response.getResult().getOutput().getText();
                        updateContext(context, report);
                        return Map.of(StateKey.RESULT_FINAL.getKey(), Objects.requireNonNull(report), StateKey.CONTEXT_AGENT.getKey(), context);
                    })
                    .build(chatResponseFlux);
            return Map.of(StateKey.RESULT_FINAL.getKey(), generator);
        }
    }

    private void updateContext(AgentContextState context, String report) {
        context.getInstant().setFinalResult(report);
        PanoramicLayer panoramicLayer = context.getPanorama();
        FocusLayer focusLayer = context.getFocus();
        List<Message> keyInteractionLogs = panoramicLayer.getKeyInteractionLogs();
        //加入一问一答
        keyInteractionLogs.addAll(List.of(
                new UserMessage(focusLayer.getCurrentUserQuestion()),
                new AssistantMessage(report)
        ));
        //提取关键交互日志
        List<Message> updatedKeyLogs = ContextUtils.extractKeyInteractionLogs(keyInteractionLogs);
        panoramicLayer.setKeyInteractionLogs(updatedKeyLogs);
    }

}
