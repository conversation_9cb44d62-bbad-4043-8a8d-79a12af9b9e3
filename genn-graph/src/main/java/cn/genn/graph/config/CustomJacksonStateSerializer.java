package cn.genn.graph.config;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.serializer.plain_text.PlainTextStateSerializer;
import com.alibaba.cloud.ai.graph.state.AgentStateFactory;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.ai.chat.messages.Message;

import java.io.IOException;
import java.io.ObjectInput;
import java.io.ObjectOutput;
import java.nio.charset.StandardCharsets;

public class CustomJacksonStateSerializer extends PlainTextStateSerializer {

    protected final ObjectMapper objectMapper;

    public CustomJacksonStateSerializer() {
        this(OverAllState::new, new ObjectMapper());
    }

    protected CustomJacksonStateSerializer(AgentStateFactory<OverAllState> stateFactory, ObjectMapper objectMapper) {
        super(stateFactory);
        this.objectMapper = objectMapper;
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // register MessageDeserializer
        SimpleModule messageModule = new SimpleModule();
        messageModule.addDeserializer(Message.class, new MessageDeserializer());
        objectMapper.registerModule(messageModule);

        // register GennGraphOverAllStateDeserializer
        SimpleModule stateModule = new SimpleModule();
        stateModule.addDeserializer(OverAllState.class, new GennGraphOverAllStateDeserializer(objectMapper));
        objectMapper.registerModule(stateModule);

        // other properties
        objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Override
    public String contentType() {
        return "application/json";
    }

    @Override
    public void write(OverAllState object, ObjectOutput out) throws IOException {
        String json = objectMapper.writeValueAsString(object);

        // 这边修改的原因在于，序列化长度限制的问题，当数据量过大时，可能会导致序列化失败。修改`CustomJacksonStateSerializer`使用字节数组方式避免UTF长度限制
        byte[] jsonBytes = json.getBytes(StandardCharsets.UTF_8);
        out.writeInt(jsonBytes.length);
        out.write(jsonBytes);
    }

    @Override
    public OverAllState read(ObjectInput in) throws IOException {

        // 这边修改的原因在于，序列化长度限制的问题，当数据量过大时，可能会导致序列化失败。修改`CustomJacksonStateSerializer`使用字节数组方式避免UTF长度限制
        int length = in.readInt();
        byte[] jsonBytes = new byte[length];
        in.readFully(jsonBytes);
        String json = new String(jsonBytes, StandardCharsets.UTF_8);
        return objectMapper.readValue(json, OverAllState.class);
    }
}
