package cn.genn.graph.config;

import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.core.StateKey;
import cn.genn.graph.tot.ToTStructure;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class GennGraphOverAllStateDeserializer extends JsonDeserializer<OverAllState> {

    private final ObjectMapper objectMapper;

    public GennGraphOverAllStateDeserializer(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    @Override
    public OverAllState deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        ObjectNode node = objectMapper.readTree(p);

        Map<String, Object> data = objectMapper.convertValue(node.get("data"), new TypeReference<>() {
        });
        Map<String, Object> newData = new HashMap<>();

        // 处理上下文
        AgentContextState agentContextState = objectMapper.convertValue(data.get(StateKey.CONTEXT_AGENT.getKey()), AgentContextState.class);
        newData.put(StateKey.CONTEXT_AGENT.getKey(), agentContextState);
        
        //处理totStructure
        ToTStructure toTStructure = objectMapper.convertValue(data.get(StateKey.TOT_STRUCTURE.getKey()), ToTStructure.class);
        newData.put(StateKey.TOT_STRUCTURE.getKey(), toTStructure);

        // 处理其他数据
        data.forEach((key, value) -> {
            if (!newData.containsKey(key)) {
                newData.put(key, value);
            }
        });

        return new OverAllState(newData);
    }
}
