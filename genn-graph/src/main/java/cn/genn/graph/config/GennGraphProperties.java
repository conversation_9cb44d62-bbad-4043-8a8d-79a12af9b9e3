package cn.genn.graph.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * Genn Graph框架配置属性
 * 
 * 定义了框架的所有可配置参数，支持通过application.yml或application.properties进行配置。
 * 
 * <AUTHOR> Framework Team
 */
@Data
@ConfigurationProperties(prefix = "genn.graph")
public class GennGraphProperties {
    
    /**
     * 框架是否启用
     */
    private boolean enabled = true;
    
    /**
     * 执行配置
     */
    private Execution execution = new Execution();
    
    /**
     * 上下文管理配置
     */
    private Context context = new Context();
    
    /**
     * ToT配置
     */
    private ToT tot = new ToT();
    
    /**
     * Agent配置
     */
    private Agent agent = new Agent();

    /**
     * 工具配置
     */
    private Tool tool = new Tool();

    /**
     * 集成配置
     */
    private Integration integration = new Integration();

    /**
     * StateGraph缓存配置
     */
    private StateGraphCache stateGraphCache = new StateGraphCache();
    
    /**
     * 执行配置
     */
    @Data
    public static class Execution {
        /**
         * 默认执行超时时间
         */
        private Duration timeout = Duration.ofMinutes(5);
        
        /**
         * 最大重试次数
         */
        private int maxRetryAttempts = 3;
        
        /**
         * 是否启用并行执行
         */
        private boolean parallelExecutionEnabled = false;
        
        /**
         * 最大并行度
         */
        private int maxParallelism = 4;
        
        /**
         * 是否启用详细日志
         */
        private boolean detailedLoggingEnabled = false;
    }
    
    /**
     * 上下文管理配置
     */
    @Data
    public static class Context {
        /**
         * 上下文最大空闲时间
         */
        private Duration maxIdleTime = Duration.ofHours(1);
        
        /**
         * 上下文清理间隔
         */
        private Duration cleanupInterval = Duration.ofMinutes(10);
        
        /**
         * 是否启用上下文持久化
         */
        private boolean persistenceEnabled = false;
        
        /**
         * 上下文存储类型
         */
        private String storageType = "memory";
    }
    
    /**
     * ToT配置
     */
    @Data
    public static class ToT {
        /**
         * ToT缓存大小
         */
        private int cacheSize = 100;
        
        /**
         * ToT缓存过期时间
         */
        private Duration cacheExpiration = Duration.ofHours(1);
        
        /**
         * 是否启用ToT验证
         */
        private boolean validationEnabled = true;
        
        /**
         * 最大ToT深度
         */
        private int maxDepth = 10;
        
        /**
         * 最大节点数量
         */
        private int maxNodes = 50;
    }
    
    /**
     * Agent配置
     */
    @Data
    public static class Agent {
        /**
         * 默认Agent超时时间
         */
        private Duration defaultTimeout = Duration.ofMinutes(2);

        /**
         * Agent注册表大小限制
         */
        private int registryMaxSize = 1000;

        /**
         * 是否启用Agent监控
         */
        private boolean monitoringEnabled = true;

        /**
         * Agent执行统计保留时间
         */
        private Duration statsRetentionTime = Duration.ofDays(7);
    }

    /**
     * StateGraph缓存配置
     */
    @Data
    public static class StateGraphCache {
        /**
         * 缓存最大大小
         */
        private int maxSize = 100;

        /**
         * 缓存过期时间
         */
        private Duration expiration = Duration.ofHours(1);

        /**
         * 是否启用定时清理
         */
        private boolean cleanupEnabled = true;

        /**
         * 是否启用统计日志
         */
        private boolean statsLoggingEnabled = false;
    }
    
    /**
     * 工具配置
     */
    @Data
    public static class Tool {
        /**
         * 工具执行超时时间
         */
        private Duration executionTimeout = Duration.ofMinutes(1);
        
        /**
         * 工具注册表大小限制
         */
        private int registryMaxSize = 500;
        
        /**
         * 是否启用工具验证
         */
        private boolean validationEnabled = true;
        
        /**
         * 工具执行重试次数
         */
        private int maxRetryAttempts = 2;
    }
    
    /**
     * 集成配置
     */
    @Data
    public static class Integration {
        /**
         * 是否启用Spring AI Graph集成
         */
        private boolean springAiGraphEnabled = true;
        
        /**
         * 是否启用状态同步
         */
        private boolean stateSyncEnabled = true;
        
        /**
         * 是否启用检查点
         */
        private boolean checkpointingEnabled = false;
        
        /**
         * 检查点存储类型
         */
        private String checkpointStorageType = "memory";
    }
    
    /**
     * 获取配置摘要
     */
    public String getConfigSummary() {
        return String.format(
            "GennGraphProperties{enabled=%s, executionTimeout=%s, contextMaxIdleTime=%s, totCacheSize=%d, agentRegistryMaxSize=%d}",
            enabled, 
            execution.timeout, 
            context.maxIdleTime, 
            tot.cacheSize, 
            agent.registryMaxSize
        );
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (execution.timeout.isNegative() || execution.timeout.isZero()) {
            throw new IllegalArgumentException("Execution timeout must be positive");
        }
        
        if (execution.maxRetryAttempts < 0) {
            throw new IllegalArgumentException("Max retry attempts cannot be negative");
        }
        
        if (context.maxIdleTime.isNegative()) {
            throw new IllegalArgumentException("ContextInput max idle time cannot be negative");
        }
        
        if (tot.cacheSize <= 0) {
            throw new IllegalArgumentException("ToT cache size must be positive");
        }
        
        if (tot.maxDepth <= 0) {
            throw new IllegalArgumentException("ToT max depth must be positive");
        }
        
        if (tot.maxNodes <= 0) {
            throw new IllegalArgumentException("ToT max nodes must be positive");
        }
        
        if (agent.registryMaxSize <= 0) {
            throw new IllegalArgumentException("Agent registry max size must be positive");
        }
        
        if (tool.registryMaxSize <= 0) {
            throw new IllegalArgumentException("Tool registry max size must be positive");
        }
    }
}
