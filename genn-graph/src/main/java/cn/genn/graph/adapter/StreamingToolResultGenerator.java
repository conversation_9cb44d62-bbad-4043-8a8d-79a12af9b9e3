/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.genn.graph.adapter;

import com.alibaba.cloud.ai.graph.NodeOutput;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.async.AsyncGenerator;
import com.alibaba.cloud.ai.graph.async.FlowGenerator;
import com.alibaba.cloud.ai.graph.streaming.StreamingOutput;
import lombok.Getter;
import org.reactivestreams.FlowAdapters;
import reactor.core.publisher.Flux;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 用于处理字符串流式响应的生成器接口。
 * 提供流式API来配置和构建处理字符串响应并产生输出的流式生成器。
 * 
 * <AUTHOR>
 */
public interface StreamingToolResultGenerator {

    /**
     * Builder类用于创建处理字符串响应的AsyncGenerator实例。
     * 该构建器允许设置映射逻辑、起始节点和初始状态。
     */
    class Builder {

        private Function<String, Map<String, Object>> mapResult;
        private String startingNode;
        private OverAllState startingState;

        /**
         * 设置将完整字符串结果转换为Map结果的映射函数。
         * 
         * @param mapResult 将最终字符串响应转换为结果映射的函数
         * @return builder实例，用于方法链式调用
         */
        public Builder mapResult(Function<String, Map<String, Object>> mapResult) {
            this.mapResult = mapResult;
            return this;
        }

        /**
         * 设置流式处理的起始节点。
         * 
         * @param node 流程中起始节点的标识符
         * @return builder实例，用于方法链式调用
         */
        public Builder startingNode(String node) {
            this.startingNode = node;
            return this;
        }

        /**
         * 设置流式处理的初始状态。
         * 
         * @param state 开始时的整体状态
         * @return builder实例，用于方法链式调用
         */
        public Builder startingState(OverAllState state) {
            this.startingState = state;
            return this;
        }

        /**
         * 构建并返回处理字符串响应的AsyncGenerator实例。
         * 生成器会合并部分响应并将其映射到最终输出。
         * 
         * @param flux 字符串对象的Flux流
         * @return 生成NodeOutput实例的AsyncGenerator
         */
        public AsyncGenerator<? extends NodeOutput> build(Flux<String> flux) {
            return buildInternal(flux, 
                    chunk -> new StreamingOutput(chunk, startingNode, startingState));
        }

        /**
         * 构建并返回处理字符串响应的AsyncGenerator实例，
         * 包装每个字符串片段到StreamingStringOutput对象中。
         * 这个方法允许下游消费者访问每个流式结果的完整上下文信息。
         * 
         * @param flux 字符串对象的Flux流
         * @return 生成StreamingStringOutput实例的AsyncGenerator
         */
        public AsyncGenerator<? extends NodeOutput> buildWithContext(Flux<String> flux) {
            return buildInternal(flux, 
                    chunk -> new StreamingStringOutput(chunk, startingNode, startingState));
        }

        private AsyncGenerator<? extends NodeOutput> buildInternal(Flux<String> flux,
                Function<String, ? extends NodeOutput> outputMapper) {
            Objects.requireNonNull(flux, "flux cannot be null");
            Objects.requireNonNull(mapResult, "mapResult cannot be null");

            // 使用AtomicReference来累积字符串结果
            var accumulatedResult = new AtomicReference<StringBuilder>(new StringBuilder());

            Consumer<String> appendChunk = (chunk) -> {
                accumulatedResult.updateAndGet(builder -> {
                    if (chunk != null) {
                        builder.append(chunk);
                    }
                    return builder;
                });
            };

            var processedFlux = flux
                    .filter(Objects::nonNull)  // 过滤掉null值
                    .doOnNext(appendChunk)      // 累积每个字符串片段
                    .map(outputMapper);         // 转换为输出对象

            return FlowGenerator.fromPublisher(FlowAdapters.toFlowPublisher(processedFlux), () -> {
                StringBuilder finalBuilder = accumulatedResult.get();
                String finalResult = finalBuilder.toString();
                
                if (finalResult.isEmpty()) {
                    // 如果没有收到任何响应，返回空的结果
                    return Map.of();
                }
                return mapResult.apply(finalResult);
            });
        }
    }

    /**
     * 自定义的StreamingOutput实现，专门用于字符串流。
     * 提供更丰富的上下文信息。
     */
    @Getter
    class StreamingStringOutput extends NodeOutput {
        private final String chunk;
        private final String nodeId;
        private final OverAllState state;
        private final long timestamp;

        public StreamingStringOutput(String chunk, String nodeId, OverAllState state) {
            super(nodeId, state);
            this.chunk = chunk;
            this.nodeId = nodeId;
            this.state = state;
            this.timestamp = System.currentTimeMillis();
        }

        @Override
        public OverAllState state() {
            return state;
        }

        @Override
        public String toString() {
            return "StreamingStringOutput{" +
                    "chunk='" + (chunk != null && chunk.length() > 100 ? 
                        chunk.substring(0, 100) + "..." : chunk) + '\'' +
                    ", nodeId='" + nodeId + '\'' +
                    ", timestamp=" + timestamp +
                    '}';
        }
    }

    /**
     * 返回StreamingStringGenerator构建器的新实例。
     * 
     * @return 新的构建器实例
     */
    static Builder builder() {
        return new Builder();
    }
}
