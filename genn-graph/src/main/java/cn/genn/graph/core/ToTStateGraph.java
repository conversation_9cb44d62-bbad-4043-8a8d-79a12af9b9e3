package cn.genn.graph.core;

import cn.genn.graph.component.edge.ToolExecuteConditionEdge;
import cn.genn.graph.component.node.*;
import cn.genn.graph.component.node.tot.ToTGetNode;
import cn.genn.graph.component.node.tot.ToTParserNode;
import cn.genn.graph.config.CustomJacksonStateSerializer;
import cn.genn.graph.context.prompt.PromptBuilder;
import cn.genn.graph.context.prompt.impl.XMLPromptBuilder;
import cn.genn.graph.context.token.impl.SimpleTokenCalculator;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.alibaba.cloud.ai.graph.serializer.plain_text.PlainTextStateSerializer;
import lombok.Getter;
import lombok.Setter;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static cn.genn.graph.component.edge.ToolExecuteConditionEdge.*;
import static com.alibaba.cloud.ai.graph.action.AsyncEdgeAction.edge_async;
import static com.alibaba.cloud.ai.graph.action.AsyncNodeAction.node_async;

/**
 * 基于TOT思维链的状态图
 *
 * 使用StateKey枚举统一管理所有的key和策略
 *
 * <AUTHOR>
 */
public class ToTStateGraph extends ModularStateGraph {

    public static final String name = "ToTStateGraph";
    
    /**
     * 开始节点
     */
    public static final String START_NODE = "start_node";

    /**
     * TOT获取节点
     */
    public static final String TOT_GET_NODE = "tot_get_node";

    /**
     * TOT解析节点
     */
    public static final String TOT_PARSE_NODE = "tot_parse_node";

    /**
     * 强制返回工具的模型调用节点
     */
    public static final String LLM_TOOL_DISPATCHER_NODE = "context_llm_return_tool_node";

    /**
     * 工具执行节点
     */
    public static final String TOOL_EXECUTOR_NODE = "tool_executor_node";

    /**
     * 结束节点
     */
    public static final String END_NODE = "end_node";

    /**
     * 报告节点
     * <p>
     * 用于生成最终报告或结果输出
     */
    public static final String REPORT_NODE = "report_node";
    
    /**
     * 强制返回工具的模型调用的边映射
     */
    public static final Map<String, String> TOOL_EXECUTE_MAPPING = Map.of(
            CONTINUE, LLM_TOOL_DISPATCHER_NODE,
            HUMAN, END_NODE,
            TERMINATE, REPORT_NODE
    );

    /**
     * tot原始信息字符串
     */
    private final String totOriginInfo;

    /**
     * agent定义
     */
    private final String agentDefinition;
    
    private final PromptBuilder promptBuilder;
    
    @Setter
    private ChatClient llmToolDispatcherClient;
    
    @Setter
    private String reportSystemPrompt;

    
    @Getter
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    public ToTStateGraph(String agentDefinition, String totOriginInfo, List<ToolCallback> tools) {
        this(agentDefinition, totOriginInfo, tools, KeyStrategyFactoryBuilder.create());
    }

    public ToTStateGraph(String agentDefinition, String totOriginInfo, List<ToolCallback> tools, KeyStrategyFactoryBuilder factoryBuilder) {
        this(agentDefinition, totOriginInfo, tools, factoryBuilder, new XMLPromptBuilder(new SimpleTokenCalculator()), new CustomJacksonStateSerializer());
    }

    public ToTStateGraph(String agentDefinition, String totOriginInfo, List<ToolCallback> tools,
                         KeyStrategyFactoryBuilder factoryBuilder,
                         PromptBuilder promptBuilder,
                         PlainTextStateSerializer stateSerializer) {
        super(name, tools, factoryBuilder
                .putToTDefaults()
                .build(), stateSerializer);
        this.totOriginInfo = totOriginInfo;
        this.agentDefinition = agentDefinition;
        this.promptBuilder = promptBuilder;
        this.llmToolDispatcherClient = ChatClientFactory.createChatModel();
    }

    @Override
    protected void buildWorkflow() throws GraphStateException {

        StartNode.ContextInput contextInput = new StartNode.ContextInput()
                .setAgentDefinition(agentDefinition)
                .setToolCallbackResolver(toolCallbackResolver);

        // 添加内置节点
        addNode(START_NODE, node_async(new StartNode(contextInput)));
        addNode(TOT_GET_NODE, node_async(new ToTGetNode(totOriginInfo)));
        addNode(TOT_PARSE_NODE, node_async(new ToTParserNode()));
        addNode(LLM_TOOL_DISPATCHER_NODE, node_async(new LLMToolDispatcherNode(llmToolDispatcherClient, promptBuilder)));
        addNode(TOOL_EXECUTOR_NODE, node_async(new ToolExecutorNode(toolCallbackResolver, new ToolExecutorNode.ExecuteConfig().setExecutorService(executorService))));
        ReportNode reportNode = new ReportNode(llmToolDispatcherClient, promptBuilder);
        if (CharSequenceUtil.isNotEmpty(reportSystemPrompt)) {
            reportNode.setSystemPrompt(reportSystemPrompt);
        }
        addNode(REPORT_NODE, node_async(reportNode));
        addNode(END_NODE, node_async(new EndNode()));

        // 添加内置边
        addEdge(START, START_NODE);
        addEdge(START_NODE, TOT_GET_NODE);
        addEdge(TOT_GET_NODE, TOT_PARSE_NODE);
        addEdge(TOT_PARSE_NODE, LLM_TOOL_DISPATCHER_NODE);
        addEdge(LLM_TOOL_DISPATCHER_NODE, TOOL_EXECUTOR_NODE);
        addConditionalEdges(TOOL_EXECUTOR_NODE, edge_async(new ToolExecuteConditionEdge()), TOOL_EXECUTE_MAPPING);
        addEdge(REPORT_NODE, END_NODE);
        addEdge(END_NODE, END);
    }
}
