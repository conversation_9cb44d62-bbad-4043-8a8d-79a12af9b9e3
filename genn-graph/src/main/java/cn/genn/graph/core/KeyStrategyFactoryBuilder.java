package cn.genn.graph.core;

import com.alibaba.cloud.ai.graph.KeyStrategy;
import com.alibaba.cloud.ai.graph.KeyStrategyFactory;
import com.alibaba.cloud.ai.graph.state.strategy.AppendStrategy;
import com.alibaba.cloud.ai.graph.state.strategy.ReplaceStrategy;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * KeyStrategyFactory 构建器，用于支持子类扩展键策略。
 * 提供了一种类型安全且灵活的方式来构建自定义的 KeyStrategyFactory。
 * <p>
 * 重要说明：
 * 1. 使用 create() 方法创建的构建器会包含默认策略，且默认策略具有最高优先级
 * 2. 使用 createEmpty() 方法可以创建不包含默认策略的构建器
 * 3. 默认策略键名：agentContext, messages
 * 4. 建议自定义策略使用不同的键名以避免冲突
 * <p>
 * 使用示例：
 * <pre>{@code
 * KeyStrategyFactory factory = ModularStateGraph.KeyStrategyFactoryBuilder
 *     .create()
 *     .put("businessContext", new MergeStrategy())
 *     .put("executionHistory", new AppendStrategy())
 *     .build();
 * }</pre>
 * <AUTHOR>
 */
@Slf4j
public class KeyStrategyFactoryBuilder {

    private final Map<String, KeyStrategy> strategies = new HashMap<>();
    private final Set<String> defaultKeys = Set.of(StateKey.CONTEXT_AGENT.getKey(), "messages");
    private boolean includeDefaults = false;

    /**
     * 创建一个新的构建器实例，包含默认的键策略。
     * 默认策略将在 build() 时添加，并具有最高优先级。
     *
     * @return 新的构建器实例
     */
    public static KeyStrategyFactoryBuilder create() {
        KeyStrategyFactoryBuilder builder = new KeyStrategyFactoryBuilder();
        builder.includeDefaults = true;
        return builder;
    }

    /**
     * 创建一个空的构建器实例，不包含任何默认策略。
     *
     * @return 空的构建器实例
     */
    public static KeyStrategyFactoryBuilder createEmpty() {
        return new KeyStrategyFactoryBuilder();
    }
    
    /**
     * 将所有 StateKey 枚举中的键策略添加到给定的构建器中。
     * 如果构建器已包含某个键，则该键的策略将被覆盖。
     *
     */
    public KeyStrategyFactoryBuilder putToTDefaults() {
        Arrays.stream(StateKey.values()).forEach(stateKey -> {
            this.put(stateKey.getKey(), stateKey.getStrategy());
        });
        return this;
    }

    /**
     * 添加一个键策略。
     * 如果键名与默认策略冲突，会发出警告，但仍会添加（默认策略在构建时会覆盖）。
     *
     * @param key      键名
     * @param strategy 键策略
     * @return 构建器实例，支持链式调用
     */
    public KeyStrategyFactoryBuilder put(String key, KeyStrategy strategy) {
        if (includeDefaults && defaultKeys.contains(key)) {
            log.warn("KeyStrategyFactoryBuilder: Key '{}' conflicts with default strategy. " +
                    "Default strategy will take precedence. Consider using a different key name.", key);
        }
        this.strategies.put(key, strategy);
        return this;
    }

    /**
     * 批量添加键策略。
     *
     * @param strategies 键策略映射
     * @return 构建器实例，支持链式调用
     */
    public KeyStrategyFactoryBuilder putAll(Map<String, KeyStrategy> strategies) {
        this.strategies.putAll(strategies);
        return this;
    }

    /**
     * 移除一个键策略。
     *
     * @param key 键名
     * @return 构建器实例，支持链式调用
     */
    public KeyStrategyFactoryBuilder remove(String key) {
        this.strategies.remove(key);
        return this;
    }

    /**
     * 检查是否包含指定的键策略。
     *
     * @param key 键名
     * @return 如果包含则返回 true，否则返回 false
     */
    public boolean containsKey(String key) {
        return this.strategies.containsKey(key);
    }

    /**
     * 获取当前所有键的集合。
     *
     * @return 键的集合
     */
    public Set<String> keySet() {
        return new HashSet<>(this.strategies.keySet());
    }

    /**
     * 构建 KeyStrategyFactory。
     * 如果包含默认策略，默认策略将最后添加以确保优先级。
     *
     * @return KeyStrategyFactory 实例
     */
    public KeyStrategyFactory build() {
        return () -> {
            // 创建策略映射的副本，避免外部修改
            Map<String, KeyStrategy> strategiesCopy = new HashMap<>(this.strategies);

            // 如果需要包含默认策略，最后添加以确保优先级
            if (includeDefaults) {
                strategiesCopy.put(StateKey.CONTEXT_AGENT.getKey(), new ReplaceStrategy());
                strategiesCopy.put("messages", new AppendStrategy());
            }

            return strategiesCopy;
        };
    }
}
