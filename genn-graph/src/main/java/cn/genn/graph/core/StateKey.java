package cn.genn.graph.core;

import cn.genn.graph.context.AgentContextState;
import cn.genn.graph.context.layer.FocusLayer;
import cn.genn.graph.tot.ToTStructure;
import com.alibaba.cloud.ai.graph.KeyStrategy;
import com.alibaba.cloud.ai.graph.state.strategy.AppendStrategy;
import com.alibaba.cloud.ai.graph.state.strategy.ReplaceStrategy;
import lombok.Getter;

/**
 * 使用枚举统一管理所有的StateKey及其更新策略
 * 提供类型安全的key管理和策略配置
 *
 * <AUTHOR>
 */
@Getter
public enum StateKey {

    // ==================== ToT相关 ====================
    /**
     * ToT原始信息JSON
     */
    TOT_ORIGIN_INFO("tot.origin_info",
            String.class,
            new ReplaceStrategy(),
            "ToT原始信息JSON"),

    /**
     * 解析后的ToT结构
     */
    TOT_STRUCTURE("tot.structure",
            ToTStructure.class,  // ToTStructure.class
            new ReplaceStrategy(),
            "解析后的ToT结构"),

    // ==================== Context相关 ====================
    /**
     * Agent上下文状态
     */
    CONTEXT_AGENT("context.agent_context",
            AgentContextState.class,
            new ReplaceStrategy(),
            "Agent上下文"),

    /**
     * 用户输入
     */
    CONTEXT_USER_INPUT("context.user_input",
            String.class,
            new ReplaceStrategy(),
            "用户输入"),
    
    /**
     * 用户补充信息
     * FocusLayer.UserFillInfo
     */
    CONTEXT_USER_FILL_INFO("context.user_fill_info",
            FocusLayer.UserFillInfo.class,
            new ReplaceStrategy(),
            "用户补充信息"),


    // ==================== Tool相关 ====================

    /**
     * 子图工具执行结果
     */
    TOOL_SUB_GRAPH_RESULT("tool.subgraph.result",
            String.class,
            new ReplaceStrategy(),
            "子图工具执行结果"),

    /**
     * 上游agent传递的输入key
     */
    TOOL_UPSTREAM_INPUT_KEY("tool.upstream_input_key",
            String.class,
            new ReplaceStrategy(),
            "工具输入的StateKey"),

    // ==================== Message相关 ====================
    /**
     * 消息列表
     */
    MESSAGE_LIST("message.messages",
            Object.class,  // List.class
            new AppendStrategy(),
            "消息列表"),

    // ==================== Result相关 ====================
    /**
     * 最终结果
     */
    RESULT_FINAL("result.final",
            String.class,
            new ReplaceStrategy(),
            "最终结果"),

    ;
    // ==================== 枚举属性 ====================
    private final String key;
    private final Class<?> valueType;
    private final KeyStrategy strategy;
    private final String description;

    /**
     * 构造函数
     *
     * @param key         状态键名
     * @param valueType   值类型
     * @param strategy    更新策略
     * @param description 描述信息
     */
    StateKey(String key, Class<?> valueType, KeyStrategy strategy, String description) {
        this.key = key;
        this.valueType = valueType;
        this.strategy = strategy;
        this.description = description;
    }

}