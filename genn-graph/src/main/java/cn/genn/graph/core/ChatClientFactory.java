package cn.genn.graph.core;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.model.SimpleApiKey;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;

/**
 * <AUTHOR>
 */
public class ChatClientFactory {

    public static ChatClient createChatModel() {
        // 创建OpenAiApi实例
        OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl("https://ark.cn-beijing.volces.com/api/v3")
                .completionsPath("/chat/completions")
                .apiKey(new SimpleApiKey("65f1df6f-89dd-4d67-9d13-62a75003a2bf"))
                .build();
        // 创建并返回聊天模型
        OpenAiChatModel chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(OpenAiChatOptions.builder()
                        .model("ep-20250822145144-9lh8c")
                        .internalToolExecutionEnabled(false).build())
                .build();
        return ChatClient.create(chatModel);
    }
}
