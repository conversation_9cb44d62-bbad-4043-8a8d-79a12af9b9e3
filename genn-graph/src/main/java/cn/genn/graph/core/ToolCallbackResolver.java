package cn.genn.graph.core;

import cn.genn.ai.tools.StreamToolCallback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.ToolCallback;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工具回调解析器
 *
 * 与Spring AI的ToolCallback深度集成，支持传统和流式工具调用。
 * 专注于核心功能：工具的注册、查找和获取。
 *
 * 主要功能：
 * 1. 工具注册和管理
 * 2. 工具查找和解析
 * 3. 与Spring AI ToolCallback无缝集成
 * 4. 支持流式工具调用（StreamToolCallback）
 * 5. 提供AsyncGenerator适配能力
 *
 * <AUTHOR>
 */
@Slf4j
public class ToolCallbackResolver {

    /** 工具回调映射表 */
    private final Map<String, ToolCallback> toolCallbacks;

    /** 流式工具回调映射表 */
    private final Map<String, StreamToolCallback> streamToolCallbacks;

    /**
     * 默认构造函数
     */
    public ToolCallbackResolver() {
        this.toolCallbacks = new ConcurrentHashMap<>();
        this.streamToolCallbacks = new ConcurrentHashMap<>();
        log.info("ToolCallbackResolver initialized");
    }

    /**
     * 构造函数
     * @param tools 工具列表
     */
    public ToolCallbackResolver(List<ToolCallback> tools) {
        this();
        if (tools != null) {
            tools.forEach(this::registerTool);
        }
        log.info("ToolCallbackResolver initialized with {} tools", toolCallbacks.size());
    }

    /**
     * 注册工具（支持流式工具）
     * @param tool 工具回调
     */
    public void registerTool(ToolCallback tool) {
        if (tool == null) {
            log.warn("Attempted to register null tool");
            return;
        }

        String toolName = tool.getToolDefinition().name();
        if (toolName.trim().isEmpty()) {
            log.warn("Attempted to register tool with null or empty name");
            return;
        }

        toolCallbacks.put(toolName, tool);

        // 如果是流式工具，同时注册到流式映射表
        if (tool instanceof StreamToolCallback) {
            streamToolCallbacks.put(toolName, (StreamToolCallback) tool);
            log.debug("Registered stream tool: {}", toolName);
        } else {
            log.debug("Registered traditional tool: {}", toolName);
        }
    }

    /**
     * 批量注册工具
     * @param tools 工具列表
     */
    public void registerTools(List<ToolCallback> tools) {
        if (tools != null) {
            tools.forEach(this::registerTool);
        }
    }

    /**
     * 获取工具回调
     * @param toolName 工具名称
     * @return 工具回调，如果不存在则返回null
     */
    public ToolCallback getToolCallback(String toolName) {
        if (toolName == null || toolName.trim().isEmpty()) {
            return null;
        }
        return toolCallbacks.get(toolName);
    }

    /**
     * 检查工具是否存在
     * @param toolName 工具名称
     * @return 是否存在
     */
    public boolean hasToolCallback(String toolName) {
        return toolName != null && toolCallbacks.containsKey(toolName);
    }

    /**
     * 获取所有工具名称
     * @return 工具名称集合
     */
    public Set<String> getAllToolNames() {
        return new HashSet<>(toolCallbacks.keySet());
    }

    /**
     * 获取所有工具回调
     * @return 工具回调集合
     */
    public Collection<ToolCallback> getAllToolCallbacks() {
        return new ArrayList<>(toolCallbacks.values());
    }

    /**
     * 移除工具（同时移除流式映射）
     * @param toolName 工具名称
     */
    public void removeTool(String toolName) {
        if (toolName != null) {
            toolCallbacks.remove(toolName);
            streamToolCallbacks.remove(toolName);
            log.debug("Removed tool from both traditional and stream mappings: {}", toolName);
        }
    }

    /**
     * 清空所有工具（同时清空流式映射）
     */
    public void clear() {
        toolCallbacks.clear();
        streamToolCallbacks.clear();
        log.debug("Cleared all tools from both traditional and stream mappings");
    }

    /**
     * 获取工具数量
     * @return 工具数量
     */
    public int getToolCount() {
        return toolCallbacks.size();
    }

    /**
     * 检查是否为空
     * @return 是否为空
     */
    public boolean isEmpty() {
        return toolCallbacks.isEmpty();
    }

    /**
     * 获取工具回调的Map视图（只读）
     * 用于与Spring AI框架的深度集成
     * @return 工具回调的不可变Map
     */
    public Map<String, ToolCallback> getToolCallbacksMap() {
        return Collections.unmodifiableMap(toolCallbacks);
    }

    /**
     * 根据工具名称列表获取对应的工具回调列表
     * 用于批量获取工具，常用于Agent配置
     * @param toolNames 工具名称列表
     * @return 工具回调列表
     */
    public List<ToolCallback> getToolCallbacks(List<String> toolNames) {
        if (toolNames == null || toolNames.isEmpty()) {
            return Collections.emptyList();
        }

        return toolNames.stream()
            .map(this::getToolCallback)
            .filter(Objects::nonNull)
            .toList();
    }

    /**
     * 创建一个新的ToolCallbackResolver实例，包含指定的工具
     * 用于创建特定场景的工具子集
     * @param toolNames 要包含的工具名称
     * @return 新的ToolCallbackResolver实例
     */
    public ToolCallbackResolver createSubset(Set<String> toolNames) {
        ToolCallbackResolver subset = new ToolCallbackResolver();
        if (toolNames != null) {
            toolNames.forEach(name -> {
                ToolCallback tool = getToolCallback(name);
                if (tool != null) {
                    subset.registerTool(tool);
                }
            });
        }
        return subset;
    }

    // ==================== 流式工具相关方法 ====================

    /**
     * 检查工具是否支持流式调用
     * @param toolName 工具名称
     * @return true表示支持流式调用
     */
    public boolean isStreamingSupported(String toolName) {
        return streamToolCallbacks.containsKey(toolName);
    }

    /**
     * 获取流式工具回调
     * @param toolName 工具名称
     * @return 流式工具回调，如果不存在则返回null
     */
    public StreamToolCallback getStreamToolCallback(String toolName) {
        return streamToolCallbacks.get(toolName);
    }

    /**
     * 获取所有流式工具名称
     * @return 流式工具名称集合
     */
    public Set<String> getAllStreamToolNames() {
        return new HashSet<>(streamToolCallbacks.keySet());
    }

    /**
     * 根据工具名称列表获取对应的流式工具回调列表
     * @param toolNames 工具名称列表
     * @return 流式工具回调列表
     */
    public List<StreamToolCallback> getStreamToolCallbacks(List<String> toolNames) {
        if (toolNames == null || toolNames.isEmpty()) {
            return Collections.emptyList();
        }

        return toolNames.stream()
            .map(this::getStreamToolCallback)
            .filter(Objects::nonNull)
            .toList();
    }

}
