package cn.genn.graph.core;

import cn.genn.graph.config.CustomJacksonStateSerializer;
import cn.genn.graph.tools.internal.InternalToolFactory;
import com.alibaba.cloud.ai.graph.CompileConfig;
import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.KeyStrategyFactory;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.action.AsyncCommandAction;
import com.alibaba.cloud.ai.graph.action.AsyncNodeAction;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.alibaba.cloud.ai.graph.serializer.plain_text.PlainTextStateSerializer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.ToolCallback;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模块化 StateGraph 的抽象基类，提供三层上下文管理、工具池和节点注入能力。
 * <p>
 * 这是框架的基石，为所有上层 Agent 提供统一的能力和扩展点：
 * <p>
 * 设计要点:
 * 1. 继承 StateGraph: 直接获得图构建的所有能力
 * 2. 构造函数: 强制传入工具列表，并初始化 ToolCallbackResolver，作为图的"工具池"
 * 3. 上下文管理: 在构造时，自动配置好处理 AgentContextState 三层结构的 KeyStrategyFactory
 * 4. 节点注入（钩子）: 提供公共方法，如 addNodeBefore() 和 addNodeAfter()，允许子类在不破坏核心流程的情况下，
 * 动态地在两个已知节点之间插入新的节点或子图
 * <p>
 * 核心能力：
 * - 强制使用 AgentContextState 三层上下文模型
 * - 统一的工具注册和解析机制
 * - 提供"钩子"机制，允许子类在预定义的流程点前后注入自定义节点
 * - 完整的节点管理和边管理能力
 * - 图结构动态修改能力
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ModularStateGraph extends StateGraph {

    @Getter
    protected final ToolCallbackResolver toolCallbackResolver;

    @Getter
    protected final String graphName;

    @Getter
    protected volatile CompiledGraph compiledGraph;

    @Getter
    protected volatile boolean isBuilt = false;

    // 记录用户添加的自定义节点
    private final Map<String, Object> customNodes = new ConcurrentHashMap<>();

    // 记录用户添加的自定义边
    private final List<EdgeRecord> customEdges = new ArrayList<>();

    private final Map<String, Object> builtInNodeContents = new ConcurrentHashMap<>();

    // 记录内置边
    private final List<EdgeRecord> builtInEdges = new ArrayList<>();

    // 标记是否在构建内置工作流
    private boolean buildingInternalWorkflow = false;

    /**
     * 边记录
     */
    private static class EdgeRecord {
        final String source;
        final String target;
        final EdgeType type;
        final Object condition; // 用于条件边
        final Map<String, String> mappings; // 用于条件边

        EdgeRecord(String source, String target) {
            this.source = source;
            this.target = target;
            this.type = EdgeType.NORMAL;
            this.condition = null;
            this.mappings = null;
        }

        EdgeRecord(String source, Object condition, Map<String, String> mappings) {
            this.source = source;
            this.target = null;
            this.type = EdgeType.CONDITIONAL;
            this.condition = condition;
            this.mappings = mappings;
        }

        enum EdgeType {
            NORMAL,
            CONDITIONAL
        }
    }

    /**
     * 构造函数
     */
    public ModularStateGraph(String name, List<ToolCallback> tools) {
        this(name, tools, createDefaultKeyStrategyFactory());
    }

    /**
     * 构造函数，支持自定义KeyStrategyFactory
     */
    protected ModularStateGraph(String name, List<ToolCallback> tools, KeyStrategyFactory keyStrategyFactory) {
        this(name, tools, keyStrategyFactory, new CustomJacksonStateSerializer());
    }

    protected ModularStateGraph(String name, List<ToolCallback> tools, KeyStrategyFactory keyStrategyFactory, PlainTextStateSerializer stateSerializer) {
        super(name, keyStrategyFactory, stateSerializer);
        this.graphName = name;
        this.toolCallbackResolver = new ToolCallbackResolver(tools);
        InternalToolFactory.getInstance().getAllToolList().forEach(this.toolCallbackResolver::registerTool);
        log.info("ModularStateGraph '{}' initialized with {} tools and custom KeyStrategyFactory",
                name, tools != null ? tools.size() : 0);
    }

    // ==================== 重写StateGraph的方法 ====================


    /**
     * 重写addNode方法，只缓存不添加
     */
    @Override
    public StateGraph addNode(String nodeId, AsyncNodeAction nodeAction) throws GraphStateException {
        if (buildingInternalWorkflow) {
            // 需要保存节点内容以便后续添加
            builtInNodeContents.put(nodeId, nodeAction);
        } else {
            // 用户自定义节点，记录为自定义节点
            customNodes.put(nodeId, nodeAction);
        }
        log.debug("Node '{}' cached for later processing", nodeId);
        return this;
    }

    /**
     * 重写addNode方法，支持子图
     */
    @Override
    public StateGraph addNode(String nodeId, StateGraph subgraph) throws GraphStateException {
        if (buildingInternalWorkflow) {
            builtInNodeContents.put(nodeId, subgraph);
        } else {
            customNodes.put(nodeId, subgraph);
        }
        log.debug("Subgraph node '{}' cached for later processing", nodeId);
        return this;
    }

    /**
     * 重写addEdge方法，只缓存不添加
     */
    @Override
    public StateGraph addEdge(String sourceId, String targetId) throws GraphStateException {
        if (buildingInternalWorkflow) {
            // 构建内置工作流时，记录为内置边
            builtInEdges.add(new EdgeRecord(sourceId, targetId));
        } else {
            // 用户自定义边
            customEdges.add(new EdgeRecord(sourceId, targetId));
        }
        log.debug("Edge '{}' -> '{}' cached for later processing", sourceId, targetId);
        return this;
    }

    /**
     * 重写addConditionalEdges方法，只缓存不添加
     */
    @Override
    public StateGraph addConditionalEdges(String sourceId, AsyncCommandAction condition,
                                          Map<String, String> mappings) throws GraphStateException {
        if (buildingInternalWorkflow) {
            builtInEdges.add(new EdgeRecord(sourceId, condition, mappings));
        } else {
            customEdges.add(new EdgeRecord(sourceId, condition, mappings));
        }
        log.debug("Conditional edges from '{}' cached for later processing", sourceId);
        return this;
    }

    // ==================== 编译和构建逻辑 ====================

    /**
     * 编译图
     */
    @Override
    public CompiledGraph compile() throws GraphStateException {
        return compile(CompileConfig.builder().build());
    }

    /**
     * 编译图（带配置）
     */
    @Override
    public CompiledGraph compile(CompileConfig config) throws GraphStateException {
        if (compiledGraph == null) {
            synchronized (this) {
                if (compiledGraph == null) {
                    ensureGraphBuilt();
                    log.debug("Compiling graph '{}'", graphName);
                    compiledGraph = super.compile(config);
                    log.debug("Graph '{}' compiled successfully", graphName);
                }
            }
        }
        return compiledGraph;
    }

    /**
     * 确保图已构建
     */
    protected final void ensureGraphBuilt() throws GraphStateException {
        if (!isBuilt) {
            synchronized (this) {
                if (!isBuilt) {
                    log.debug("Building graph '{}'", graphName);

                    // 1. 首先构建内置工作流
                    buildingInternalWorkflow = true;
                    buildWorkflow();
                    buildingInternalWorkflow = false;

                    // 2. 处理用户自定义的节点和边
                    processCustomizations();

                    isBuilt = true;
                    log.debug("Graph '{}' built successfully", graphName);
                }
            }
        }
    }

    /**
     * 处理用户自定义的节点和边
     * 核心原则：用户自定义的覆盖内置的
     */
    private void processCustomizations() throws GraphStateException {
        log.debug("Processing customizations for graph '{}'", graphName);

        // 1. 处理节点：合并内置节点和自定义节点，自定义优先

        // 1.1 先添加所有内置节点内容
        Map<String, Object> finalNodes = new LinkedHashMap<>(builtInNodeContents);
        
        // 1.2 记录哪些内置节点被覆盖了
        Set<String> overriddenNodes = new HashSet<>();

        // 1.3 用自定义节点覆盖同名的内置节点
        for (Map.Entry<String, Object> entry : customNodes.entrySet()) {
            String nodeId = entry.getKey();
            Object nodeContent = entry.getValue();

            if (finalNodes.containsKey(nodeId)) {
                log.warn("Custom node '{}' is overriding built-in node", nodeId);
                overriddenNodes.add(nodeId);
            }

            finalNodes.put(nodeId, nodeContent);
        }

        // 1.4 统一添加所有节点到父类
        for (Map.Entry<String, Object> entry : finalNodes.entrySet()) {
            String nodeId = entry.getKey();
            Object nodeContent = entry.getValue();
            addCustomNode(nodeId, nodeContent);
            log.debug("Added node '{}' to graph", nodeId);
        }

        // 2. 处理边：分步骤处理，确保正确的覆盖逻辑
        // 2.1 收集所有边，按源节点分组
        Map<String, List<EdgeRecord>> allEdgesBySource = new HashMap<>();

        // 先添加内置边
        for (EdgeRecord edge : builtInEdges) {
            allEdgesBySource.computeIfAbsent(edge.source, k -> new ArrayList<>()).add(edge);
        }
        
        // 2.2 对于被覆盖的节点，自动继承其内置边规则（如果用户没有显式定义）
        // 收集用户自定义边的源节点
        Set<String> customEdgeSources = new HashSet<>();
        for (EdgeRecord edge : customEdges) {
            customEdgeSources.add(edge.source);
        }
        
        // 对于每个被覆盖的节点，如果用户没有为其定义边，则自动继承内置边
        for (String overriddenNode : overriddenNodes) {
            if (!customEdgeSources.contains(overriddenNode)) {
                // 用户没有为这个被覆盖的节点定义边，自动继承内置边
                List<EdgeRecord> builtInEdgesForNode = allEdgesBySource.get(overriddenNode);
                if (builtInEdgesForNode != null && !builtInEdgesForNode.isEmpty()) {
                    log.info("Node '{}' was overridden but no custom edges defined. Inheriting {} built-in edge(s).",
                            overriddenNode, builtInEdgesForNode.size());
                    // 将内置边作为自定义边添加，这样它们会被保留
                    customEdges.addAll(builtInEdgesForNode);
                }
            }
        }

        // 再添加自定义边（包括刚刚继承的边）
        for (EdgeRecord edge : customEdges) {
            allEdgesBySource.computeIfAbsent(edge.source, k -> new ArrayList<>()).add(edge);
        }

        // 2.3 对每个源节点，应用覆盖规则
        Map<String, EdgeRecord> finalNormalEdges = new LinkedHashMap<>();
        Map<String, EdgeRecord> finalConditionalEdges = new LinkedHashMap<>();

        for (Map.Entry<String, List<EdgeRecord>> entry : allEdgesBySource.entrySet()) {
            String source = entry.getKey();
            List<EdgeRecord> edges = entry.getValue();

            // 分离普通边和条件边
            List<EdgeRecord> normalEdges = new ArrayList<>();
            List<EdgeRecord> conditionalEdges = new ArrayList<>();

            for (EdgeRecord edge : edges) {
                if (edge.type == EdgeRecord.EdgeType.CONDITIONAL) {
                    conditionalEdges.add(edge);
                } else {
                    normalEdges.add(edge);
                }
            }

            // 对于条件边：后定义的覆盖先定义的
            if (!conditionalEdges.isEmpty()) {
                EdgeRecord finalConditionalEdge = conditionalEdges.getLast();
                finalConditionalEdges.put(source, finalConditionalEdge);

                // 如果有条件边，记录被覆盖的普通边
                if (!normalEdges.isEmpty()) {
                    log.warn("Conditional edges from '{}' are overriding {} normal edge(s)",
                            source, normalEdges.size());
                }
            } else if (!normalEdges.isEmpty()) {
                // 对于普通边：后定义的覆盖先定义的
                EdgeRecord finalNormalEdge = normalEdges.getLast();
                finalNormalEdges.put(source, finalNormalEdge);

                if (normalEdges.size() > 1) {
                    log.warn("Multiple normal edges from '{}', using the last one: '{}' -> '{}'",
                            source, finalNormalEdge.source, finalNormalEdge.target);
                }
            }
        }

        // 3. 统一添加所有边到父类
        // 3.1 添加所有普通边
        for (EdgeRecord edge : finalNormalEdges.values()) {
            super.addEdge(edge.source, edge.target);
            log.debug("Added edge '{}' -> '{}'", edge.source, edge.target);
        }

        // 3.2 添加所有条件边
        for (EdgeRecord edge : finalConditionalEdges.values()) {
            super.addConditionalEdges(edge.source,
                    (AsyncCommandAction) edge.condition,
                    edge.mappings);
            log.debug("Added conditional edges from '{}'", edge.source);
        }

        // 4. 清理临时数据
        customNodes.clear();
        customEdges.clear();
        builtInNodeContents.clear();
        builtInEdges.clear();

        log.debug("Customizations processed successfully for graph '{}'", graphName);
    }

    /**
     * 生成边的唯一键
     * 对于普通边：source->target
     * 对于条件边：source->conditional
     */
    private String generateEdgeKey(EdgeRecord edge) {
        if (edge.type == EdgeRecord.EdgeType.CONDITIONAL) {
            return edge.source + "->conditional";
        } else {
            return edge.source + "->" + edge.target;
        }
    }

    /**
     * 添加自定义节点到父类
     */
    private void addCustomNode(String nodeId, Object nodeContent) throws GraphStateException {
        switch (nodeContent) {
            case AsyncNodeAction asyncNodeAction -> super.addNode(nodeId, asyncNodeAction);
            case NodeAction nodeAction -> super.addNode(nodeId, AsyncNodeAction.node_async(nodeAction));
            case StateGraph stateGraph -> super.addNode(nodeId, stateGraph);
            default -> throw new GraphStateException("Unsupported node content type: " + nodeContent.getClass());
        }
    }


    // ==================== 辅助方法 ====================

    /**
     * 创建默认的KeyStrategyFactory
     */
    private static KeyStrategyFactory createDefaultKeyStrategyFactory() {
        return KeyStrategyFactoryBuilder.create().build();
    }

    /**
     * 强制子类实现自己的工作流编排
     */
    protected abstract void buildWorkflow() throws GraphStateException;

    // ==================== 工具管理方法（保持不变） ====================

    public void registerTool(ToolCallback tool) {
        toolCallbackResolver.registerTool(tool);
    }

    public void registerTools(List<ToolCallback> tools) {
        toolCallbackResolver.registerTools(tools);
    }

    public ToolCallback getToolCallback(String toolName) {
        return toolCallbackResolver.getToolCallback(toolName);
    }

    public boolean hasToolCallback(String toolName) {
        return toolCallbackResolver.hasToolCallback(toolName);
    }

    public Set<String> getAllToolNames() {
        return toolCallbackResolver.getAllToolNames();
    }
}
