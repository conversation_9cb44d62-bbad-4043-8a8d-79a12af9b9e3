## 结构

```
{
    "tot_id": "TOT-MOTOR-TEMP-DIAGNOSIS-001",
    "name": "电机定子温度异常诊断思维树",
    "version": "1.0",
    "nodes": [
        {
            "id": "n1",
            "name": "数据特征分析",
            "type": "START",
            "prompt": "分析温度变化特征",
            "tools": [
                {
                    "name": "定子温度数据特征分析专家",
                    "params": {
                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1",
                        "告警时间": "2025-07-28 10:42:35",
                        "报警值": "95℃"
                    }
                }
            ],
            "next": [
                {
                    "condition": "result == '趋势性升高'",
                    "target": "n2"
                },
                {
                    "condition": "result == '突变式升高'",
                    "target": "n4"
                }
            ]
        },
        {
            "id": "n2",
            "name": "趋势性升高分析",
            "type": "THOUGHT",
            "prompt": "检测到温度持续缓慢上升趋势",
            "tools": [
                {
                    "name": "定子温度趋势性升高诊断专家",
                    "params": {
                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1"
                    }
                }
            ],
            "next": [
                {
                    "condition": "result == '三相温度同步升高'",
                    "target": "n3"
                },
                {
                    "condition": "result == '单相温度异常突出'",
                    "target": "n6"
                }
            ]
        },
        {
            "id": "n3",
            "name": "系统性问题诊断",
            "type": "AGENT_EXECUTION",
            "prompt": "三相温度同步升高，可能存在系统性问题",
            "tools": [
                {
                    "name": "三相温度同步升高分析诊断专家",
                    "params": {
                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1",
                        "需要综合判断的定子组名称": ["TE-81518A","TE-81518B","TE-81518C"]
                    }
                }
            ]
        },
        {
            "id": "n6",
            "name": "局部问题诊断",
            "type": "AGENT_EXECUTION",
            "prompt": "单相温度异常突出，可能存在局部故障",
            "tools": [
                {
                    "name": "单相突出升高分析诊断专家",
                    "params": {
                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1",
                        "需要综合判断的定子组名称": ["TE-81518A","TE-81518B","TE-81518C"]
                    }
                }
            ]
        },
        {
            "id": "n4",
            "name": "突变式升高诊断",
            "type": "AGENT_EXECUTION",
            "prompt": "检测到温度突变式升高，需紧急分析",
            "tools": [
                {
                    "name": "定子温度短时间跃升诊断专家",
                    "params": {
                        "报警的定子名称": "一期1号煤气鼓风机的鼓风机电机定子温度1",
                    }
                }
            ]
        }
    ]
}
```

## 要求

根据ToT定义的结构进行流转和编排

1.  利用AI根据每一步的执行结果，判断下一步应该执行哪个工具
    
2.  对应的上下文设计要融入前面的三层架构中
    
3.  工具调用必须准确无误
    

主要是整体框架的设计和调度

ToT来源： 是一个外部的接口查询，当用户提问时，查找到对应的ToT结构，主要流程如下

1.  用户提问
    
2.  框架调用外部接口，查询ToT结构
    
3.  解析ToT，生成初始化的上下文结构
    
4.  根据ToT定义的当前步骤，执行工具
    
5.  根据工具的返回值，判断流转调度
    
6.  更新上下文
    
7.  循环往复
    
8.  直到问题结束，返回