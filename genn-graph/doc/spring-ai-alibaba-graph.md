# 框架使用提示词

### **`spring-ai-alibaba-graph` 框架核心能力解析**

### 1. 框架概述 (Framework Overview)

`spring-ai-alibaba-graph` 是一个基于 Spring AI 生态的图（Graph）计算框架，专门用于构建和运行复杂的、有状态的 AI 应用，特别是多智能体系统。它将应用逻辑抽象为一个**状态图（State Graph）**，其中**节点（Node）**代表计算单元（如调用大语言模型、执行工具、运行代码等），**边（Edge）**代表不同节点之间的逻辑流转路径。

**核心特点：**

- **状态驱动 (State-Driven)**：整个图的运行都围绕一个中心状态对象 (`OverAllState`) 进行，每个节点都可以读取和更新这个状态。
- **循环与条件逻辑 (Cycles and Conditional Logic)**：支持构建包含循环和条件分支的复杂工作流，这对于实现 Agent 的 ReAct (Reason and Act) 等模式至关重要。
- **持久化与可恢复性 (Persistence & Resumability)**：通过**检查点（Checkpoint）**机制，可以将图的执行状态随时保存下来，并在之后从中断处恢复执行。支持内存、文件、Redis、MongoDB 等多种存储后端。
- **异步与流式处理 (Async & Streaming)**：框架原生支持异步执行和流式输出，非常适合构建实时响应的聊天机器人或Agent应用。
- **模块化与可扩展性 (Modular & Extensible)**：提供了丰富的内置节点类型（如LLM、工具、代码执行器、知识检索等），同时也允许用户轻松自定义节点和逻辑。

### 2. 核心概念 (Core Concepts)

要理解这个框架，首先需要掌握以下几个核心概念：

1. **`StateGraph` (状态图)**
    - **定义**：这是构建工作流的**蓝图**或**定义**。你可以在这里添加节点和边，来描述你的应用逻辑。
    - **功能**：通过 `addNode()` 添加计算单元，通过 `addEdge()` 和 `addConditionalEdges()` 定义节点间的跳转逻辑。
1. **`CompiledGraph` (已编译图)**
    - **定义**：当 `StateGraph` 定义完成后，通过 `compile()` 方法会生成一个 `CompiledGraph`。这是状态图的**可执行实例**。
    - **功能**：提供了 `invoke()` (同步执行返回最终结果) 和 `stream()` (流式返回每一步的结果) 等方法来运行图。
1. **`OverAllState` (全局状态)**
    - **定义**：这是一个贯穿整个图执行过程的**状态载体**，本质上是一个可序列化的 `Map<String, Object>`。
    - **功能**：图中所有节点共享此状态对象。节点执行的输出会更新到这个状态中，后续节点可以读取这些更新后的值。
1. **`KeyStrategy` (键更新策略)**
    - **定义**：在定义 `StateGraph` 时，可以为 `OverAllState` 中的每个键（key）指定一个更新策略。
    - **功能**：决定了当一个节点向一个已存在的键写入新值时，新旧值如何合并。
        - `ReplaceStrategy`：新值替换旧值（默认）。
        - `AppendStrategy`：将新值追加到旧值（通常用于列表）。
        - `MergeStrategy`：合并新旧值（通常用于Map）。
1. **`Node` (节点)**
    - **定义**：图中的基本**计算单元**。每个节点都执行一个具体的任务。
    - **功能**：节点的逻辑由一个 `NodeAction` 或 `AsyncNodeAction` 接口的实现来定义。它接收当前的 `OverAllState` 作为输入，并返回一个 `Map<String, Object>` 来更新状态。
1. **`Edge` (边)**
    - **定义**：连接节点的**路径**，定义了图的控制流。
    - **功能**：
        - **普通边 (`addEdge`)**：从一个节点无条件地指向另一个节点。
        - **条件边 (`addConditionalEdges`)**：根据一个 `EdgeAction` 的执行结果（返回一个字符串key），从多个可能的路径中选择一条进行跳转。这是实现逻辑分支的关键。
1. **`Checkpoint` (检查点)**
    - **定义**：图在某个时间点的**状态快照**。它包含了当前的 `OverAllState`、当前节点、下一个节点等信息。
    - **功能**：通过 `BaseCheckpointSaver` 接口的实现类（如 `RedisSaver`, `MongoSaver`）将检查点持久化，从而实现任务的中断和恢复。

### 3. 核心类与接口详解

- **`StateGraph`**
    - **用途**：定义图的结构。
    - **核心方法**：
        - `addNode(String nodeId, NodeAction action)`: 添加一个执行同步逻辑的节点。
        - `addNode(String nodeId, AsyncNodeAction action)`: 添加一个执行异步逻辑的节点。
        - `addEdge(String sourceNode, String targetNode)`: 添加一条从 `sourceNode` 到 `targetNode` 的无条件边。
        - `addConditionalEdges(String sourceNode, EdgeAction condition, Map<String, String> mappings)`: 添加条件边。`condition` 函数返回一个 key，`mappings` 中该 key 对应的 value 就是下一个节点的名字。
        - `compile(CompileConfig config)`: 编译图，生成可执行的 `CompiledGraph`。
- **`CompiledGraph`**
    - **用途**：执行图。
    - **核心方法**：
        - `invoke(Map<String, Object> inputs, RunnableConfig config)`: 同步执行图，从入口开始直到 `END` 节点，返回最终的 `OverAllState`。
        - `stream(Map<String, Object> inputs, RunnableConfig config)`: 流式执行图，每执行完一个节点，就返回一个 `NodeOutput`（包含当前节点名和状态快照）。
        - `resume(OverAllState.HumanFeedback feedback, RunnableConfig config)`: 从一个中断的检查点恢复执行。`RunnableConfig` 中的 `threadId` 和 `checkPointId` 用于定位检查点。
- **`OverAllState`**
    - **用途**：管理和传递数据。
    - **核心方法**：
        - `value(String key)`: 获取指定 key 的值。
        - `updateState(Map<String, Object> partialState)`: 使用 `KeyStrategy` 更新状态。
- **`action` 包下的接口**
    - `NodeAction`: `Map<String, Object> apply(OverAllState state)`，同步节点逻辑。
    - `EdgeAction`: `String apply(OverAllState state)`，同步条件边逻辑。
    - `AsyncNodeAction`: `CompletableFuture<Map<String, Object>> apply(OverAllState state)`，异步节点逻辑。
    - `AsyncEdgeAction`: `CompletableFuture<String> apply(OverAllState state)`，异步条件边逻辑。
    - `...WithConfig` 变体：额外接收一个 `RunnableConfig` 参数，允许在运行时动态配置。

### 4. 内置节点类型 (Built-in Node Types)

框架在 `com.alibaba.cloud.ai.graph.node` 包下提供了许多开箱即用的节点，极大地简化了开发。

- **`LlmNode`**: 调用大语言模型。可以配置 `ChatClient`、系统/用户提示词模板、输入/输出的 state key 等。
- **`ToolNode`**: 执行工具（函数调用）。它会自动从 `OverAllState` 中解析 `AssistantMessage` 里的工具调用请求，执行对应的 `ToolCallback`，并将结果作为 `ToolResponseMessage` 更新回 state。
- **`HumanNode`**: 人工介入节点。当图执行到此节点时，会抛出中断异常，等待外部（用户）提供反馈，然后通过 `resume()` 方法继续执行。
- **`CodeExecutorNodeAction`**: 代码执行节点。支持在本地命令行或 Docker 容器中执行 Python、Java、JavaScript 等代码片段，并将结果返回。
- **`KnowledgeRetrievalNode`**: 知识检索节点。与 `VectorStore` 集成，根据用户问题从向量数据库中检索相关文档，并将其内容注入到提示词中，实现 RAG (Retrieval-Augmented Generation)。
- **`BranchNode`**: 简单的分支节点，根据输入值决定输出，常用于 `addConditionalEdges` 的条件判断。
- **`AnswerNode`**: 答案生成节点，使用模板和 state 中的变量来渲染最终的答案字符串。
- **`HttpNode`**: 发起 HTTP 请求的节点，支持 GET/POST、请求头、请求体、认证等配置。
- **`IterationNode`**: 迭代节点，可以对一个列表中的每个元素执行一个子图（sub-graph），实现循环处理。

### 5. 预置 Agent 架构

框架在 `com.alibaba.cloud.ai.graph.agent` 包下提供了几种经典的 Agent 实现，可以直接使用或作为参考。

- **`ReactAgent`**: 实现了标准的 ReAct (Reason+Act) 循环。它内部构建了一个 `LlmNode` -> `ToolNode` 的循环图。LLM 负责思考并决定是否调用工具，`ToolNode` 负责执行工具，直到 LLM 认为任务完成并给出最终答案。
- **`ReactAgentWithHuman`**: 在 `ReactAgent` 的基础上增加了 `HumanNode`，允许在 Agent 执行的某个环节（如工具调用前后）暂停，等待用户确认或提供额外信息。
- **`ReflectAgent`**: 实现了反思（Reflection）机制。它包含一个“生成”图和一个“反思”图。生成图负责产出初步结果，反思图负责评估和修正结果，两者交替执行，以提升最终输出的质量。

### 6. 状态管理与持久化 (State Management & Persistence)

这是框架的核心优势之一，通过 `checkpoint` 包实现。

- **`BaseCheckpointSaver`**: 持久化存储的接口。
- **实现类**:
    - `MemorySaver`: 存储在内存中（默认）。
    - `FileSystemSaver`: 存储在本地文件系统。
    - `RedisSaver`: 存储在 Redis 中。
    - `MongoSaver`: 存储在 MongoDB 中。
- **配置**: 在 `compile()` 方法中传入 `CompileConfig` 对象，并配置所需的 `Saver`。
- **使用**: 在 `invoke()` 或 `stream()` 时传入 `RunnableConfig`，并指定一个 `threadId`。框架会自动为该 `threadId` 记录和更新检查点。下次使用相同的 `threadId` 调用时，可以从上次的状态继续。

### 7. 如何使用 (How to Use - A Quick Start Guide)

构建一个基于此框架的应用通常遵循以下步骤：

**第 1 步：定义状态和更新策略**
创建一个 `KeyStrategyFactory` 来定义 `OverAllState` 中各个字段的更新方式。

```java
KeyStrategyFactory keyStrategyFactory = () -> {
    Map<String, KeyStrategy> strategies = new HashMap<>();
    // "input" 键使用替换策略
    strategies.put("input", new ReplaceStrategy());
    // "messages" 键使用追加策略，用于保存对话历史
    strategies.put("messages", new AppendStrategy());
    return strategies;
};
```

**第 2 步：创建 `StateGraph` 实例**

```java
StateGraph graph = new StateGraph("my_agent_graph", keyStrategyFactory);
```

**第 3 步：定义并添加节点**
实例化需要的节点，如 `LlmNode`、`ToolNode` 等，并将它们添加到图中。

```java
// 假设 chatClient 和 myTool 已经定义好
LlmNode llmNode = LlmNode.builder()
        .chatClient(chatClient)
        .messagesKey("messages") // 从 state 的 "messages" 字段读取和写入
        .build();

ToolNode toolNode = ToolNode.builder()
        .toolCallbacks(List.of(myTool))
        .build();

graph.addNode("agent", llmNode);
graph.addNode("action", toolNode);
```

**第 4 步：定义控制流（添加边）**
设置图的入口点，并使用 `addEdge` 和 `addConditionalEdges` 连接节点。

```java
// 设置入口点
graph.addEdge(StateGraph.START, "agent");

// 添加条件边：根据 agent 节点的输出决定下一步
graph.addConditionalEdges(
    "agent",
    // 条件判断逻辑
    (state) -> {
        AssistantMessage lastMessage = ...; // 从 state 中获取最后一条消息
        return lastMessage.hasToolCalls() ? "continue" : "end";
    },
    // 路径映射
    Map.of(
        "continue", "action", // 如果需要调用工具，则跳转到 action 节点
        "end", StateGraph.END    // 否则结束
    )
);

// 从 action 节点返回到 agent 节点，形成循环
graph.addEdge("action", "agent");
```

**第 5 步：编译图**
配置检查点等信息，并编译图。

```java
// 使用内存存储检查点
CompileConfig compileConfig = CompileConfig.builder()
        .saverConfig(new SaverConfig().register("memory", new MemorySaver()))
        .build();
CompiledGraph compiledGraph = graph.compile(compileConfig);
```

**第 6 步：执行图**
使用 `invoke` 或 `stream` 方法运行图。

```java
// 定义一个会话ID
RunnableConfig config = RunnableConfig.builder().threadId("session-123").build();

// 准备输入
List<Message> initialMessages = List.of(new UserMessage("你好，帮我查一下今天北京的天气。"));
Map<String, Object> inputs = Map.of("messages", initialMessages);

// 执行
Optional<OverAllState> finalState = compiledGraph.invoke(inputs, config);

// 从最终状态中获取结果
finalState.ifPresent(state -> {
    List<Message> history = (List<Message>) state.value("messages").get();
    System.out.println("Final Answer: " + history.get(history.size() - 1).getText());
});
```

8. **核心抽象与扩展代码示例**

#### 1. 自定义节点逻辑 (`NodeAction`)

- **用途**: 创建一个自定义的计算单元，用于执行任何你需要的业务逻辑。
- **核心接口定义**:

```java
package com.alibaba.cloud.ai.graph.action;

import com.alibaba.cloud.ai.graph.OverAllState;
import java.util.Map;

@FunctionalInterface
public interface NodeAction {
    Map<String, Object> apply(OverAllState state) throws Exception;
}
```

- **代码实现示例**: 创建一个节点，该节点从 `OverAllState` 中读取一个名为 `username` 的字符串，然后生成一条问候语，并将其存入 `greeting_message` 字段。

```java
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import java.util.Map;

/**
 * 一个自定义节点，用于生成问候语。
 */
public class GreetingNode implements NodeAction {

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        // 1. 从全局状态中读取输入数据
        // .value("username") 返回一个 Optional<Object>
        // .map(Object::toString) 将其转换为 Optional<String>
        // .orElse("Guest") 如果不存在或为null，则提供默认值
        String name = state.value("username")
                           .map(Object::toString)
                           .orElse("Guest");

        // 2. 执行核心逻辑
        String greeting = "Hello, " + name + "! Welcome to the graph.";

        // 3. 返回一个 Map 来更新全局状态
        // 这个 Map 的 key 将成为 OverAllState 中的新 key 或更新已有的 key
        return Map.of("greeting_message", greeting);
    }
}
```

- **如何在 `StateGraph` 中使用**:

```java
StateGraph graph = new StateGraph();
// 将我们自定义的 GreetingNode 实例添加为名为 "greeter" 的节点
graph.addNode("greeter", new GreetingNode());
```

#### 2. 自定义条件边逻辑 (`EdgeAction`)

- **用途**: 实现复杂的路由逻辑，根据当前状态决定工作流的下一个走向。
- **核心接口定义**:

```java
package com.alibaba.cloud.ai.graph.action;

import com.alibaba.cloud.ai.graph.OverAllState;

@FunctionalInterface
public interface EdgeAction {
    String apply(OverAllState state) throws Exception;
}
```

- **代码实现示例**: 创建一个条件边，检查 `OverAllState` 中的 `review_score` 字段。如果分数大于等于 3，则返回 `"positive_feedback"`；否则返回 `"negative_feedback"`。

```java
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.EdgeAction;

/**
 * 一个自定义条件边，根据评分来决定路由。
 */
public class ReviewScoreEdge implements EdgeAction {

    @Override
    public String apply(OverAllState state) throws Exception {
        // 1. 从状态中获取决策所需的数据
        int score = state.value("review_score", Integer.class)
                         .orElse(0); // 如果不存在，默认为0

        // 2. 执行决策逻辑
        if (score >= 3) {
            // 3. 返回一个字符串 key，这个 key 必须在 addConditionalEdges 的 mappings 中存在
            return "positive_feedback";
        } else {
            return "negative_feedback";
        }
    }
}
```

- **如何在 `StateGraph` 中使用**:

```java
graph.addConditionalEdges(
    "check_review", // 源节点
    new ReviewScoreEdge(), // 我们自定义的条件逻辑
    Map.of(
        "positive_feedback", "thank_user_node", // 如果返回 "positive_feedback"，则跳转到 thank_user_node
        "negative_feedback", "escalate_to_support_node" // 否则跳转到 escalate_to_support_node
    )
);
```

#### 3. 自定义状态更新策略 (`KeyStrategy`)

- **用途**: 控制 `OverAllState` 中特定字段的更新行为，超越简单的替换或追加。
- **核心接口定义**:

```java
package com.alibaba.cloud.ai.graph;

import java.util.function.BiFunction;

public interface KeyStrategy extends BiFunction<Object, Object, Object> {
    // 默认实现 apply(Object oldValue, Object newValue)
}
```

- **代码实现示例**: 创建一个用于累加计数的策略。每次有新值写入时，它会将新值（必须是数字）与旧值相加。

```java
import com.alibaba.cloud.ai.graph.KeyStrategy;

/**
 * 一个自定义的 KeyStrategy，用于累加数值。
 */
public class SummationStrategy implements KeyStrategy {

    @Override
    public Object apply(Object oldValue, Object newValue) {
        // 确保新值是 Number 类型
        if (!(newValue instanceof Number)) {
            // 或者可以抛出异常，取决于你的错误处理策略
            return oldValue;
        }

        // 处理旧值不存在或不是 Number 的情况
        double oldNumber = 0.0;
        if (oldValue instanceof Number) {
            oldNumber = ((Number) oldValue).doubleValue();
        }

        double newNumber = ((Number) newValue).doubleValue();

        // 返回累加后的结果
        return oldNumber + newNumber;
    }
}
```

- **如何在 `StateGraph` 中使用**:

```java
KeyStrategyFactory keyStrategyFactory = () -> {
    Map<String, KeyStrategy> strategies = new HashMap<>();
    // 为 "total_cost" 字段指定我们自定义的累加策略
    strategies.put("total_cost", new SummationStrategy());
    strategies.put("messages", new AppendStrategy()); // 其他策略
    return strategies;
};

StateGraph graph = new StateGraph(keyStrategyFactory);
```

#### 4. 自定义持久化 (`BaseCheckpointSaver`)

- **用途**: 将图的执行状态（检查点）保存到你选择的任何存储系统中（如自定义数据库、云存储等）。
- **核心接口定义**:

```java
package com.alibaba.cloud.ai.graph.checkpoint;

public interface BaseCheckpointSaver {
    Collection<Checkpoint> list(RunnableConfig config);
    Optional<Checkpoint> get(RunnableConfig config);
    RunnableConfig put(RunnableConfig config, Checkpoint checkpoint) throws Exception;
    boolean clear(RunnableConfig config);
}
```

- **代码实现示例**: 创建一个简单的、将检查点打印到控制台的 `Saver`，用于调试和理解其工作机制。它继承了 `MemorySaver` 以复用其内存缓存逻辑。

```java
import com.alibaba.cloud.ai.graph.RunnableConfig;
import com.alibaba.cloud.ai.graph.checkpoint.Checkpoint;
import com.alibaba.cloud.ai.graph.checkpoint.savers.MemorySaver;
import java.util.LinkedList;
import java.util.Optional;

/**
 * 一个自定义的 CheckpointSaver，它在执行操作时会打印日志。
 * 继承 MemorySaver 以简化实现。
 */
public class LoggingMemorySaver extends MemorySaver {

    @Override
    public Optional<Checkpoint> get(RunnableConfig config) {
        System.out.println("[SAVER LOG] Getting checkpoint for thread: " + config.threadId().orElse("default"));
        return super.get(config);
    }

    @Override
    public RunnableConfig put(RunnableConfig config, Checkpoint checkpoint) throws Exception {
        System.out.println("[SAVER LOG] Putting checkpoint for thread: " + config.threadId().orElse("default"));
        System.out.println("  - Node ID: " + checkpoint.getNodeId());
        System.out.println("  - Next Node ID: " + checkpoint.getNextNodeId());
        System.out.println("  - State: " + checkpoint.getState());
        return super.put(config, checkpoint);
    }
}
```

- **如何在 `StateGraph` 中使用**:

```java
CompileConfig compileConfig = CompileConfig.builder()
    .saverConfig(new SaverConfig().register("logging_memory", new LoggingMemorySaver()))
    .build();

CompiledGraph compiledGraph = graph.compile(compileConfig);
```

### 附录

#### 包的定义：

- **图定义与执行 (Graph Definition & Execution)**
    - `com.alibaba.cloud.ai.graph.StateGraph`: 图的蓝图。
    - `com.alibaba.cloud.ai.graph.CompiledGraph`: 可执行的图实例。
    - `com.alibaba.cloud.ai.graph.CompileConfig`: 编译时配置（如持久化、中断点）。
    - `com.alibaba.cloud.ai.graph.RunnableConfig`: 运行时配置（如会话ID）。
- **状态管理 (State Management)**
    - `com.alibaba.cloud.ai.graph.OverAllState`: 全局状态对象。
    - `com.alibaba.cloud.ai.graph.KeyStrategy`: 状态更新策略接口。
    - `com.alibaba.cloud.ai.graph.state.strategy.*`: 内置策略实现 (`ReplaceStrategy`, `AppendStrategy`, `MergeStrategy`)。
    - `com.alibaba.cloud.ai.graph.KeyStrategyFactory`: 策略工厂。
- **动作定义 (Action Definitions)**
    - `com.alibaba.cloud.ai.graph.action.NodeAction`: 同步节点逻辑。
    - `com.alibaba.cloud.ai.graph.action.EdgeAction`: 同步条件边逻辑。
    - `com.alibaba.cloud.ai.graph.action.AsyncNodeAction`: 异步节点逻辑。
    - `com.alibaba.cloud.ai.graph.action.AsyncEdgeAction`: 异步条件边逻辑。
    - `com.alibaba.cloud.ai.graph.action.AsyncNodeActionWithConfig`: 带运行时配置的异步节点逻辑。
- **持久化 (Persistence)**
    - `com.alibaba.cloud.ai.graph.checkpoint.BaseCheckpointSaver`: 持久化存储接口。
    - `com.alibaba.cloud.ai.graph.checkpoint.Checkpoint`: 状态快照对象。
    - `com.alibaba.cloud.ai.graph.checkpoint.savers.*`: 内置存储实现 (`MemorySaver`, `RedisSaver`, etc.)。

#### 代码使用说明

````java
## 3. 核心接口和类

### 3.1 NodeAction接口

所有节点必须实现的核心接口：

```java
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.OverAllState;
import java.util.Map;

public class CustomNode implements NodeAction {
    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        // 处理业务逻辑
        Map<String, Object> updates = new HashMap<>();
        updates.put("key", "value");
        return updates;
    }
}
```

### 3.2 异步节点接口

```java
import com.alibaba.cloud.ai.graph.action.AsyncNodeAction;
import java.util.concurrent.CompletableFuture;

public class AsyncCustomNode implements AsyncNodeAction {
    @Override
    public CompletableFuture<Map<String, Object>> apply(OverAllState state) {
        return CompletableFuture.supplyAsync(() -> {
            // 异步处理逻辑
            return Map.of("result", "async_value");
        });
    }
}
```

### 3.3 CommandAction接口

用于条件分支的节点：

```java
import com.alibaba.cloud.ai.graph.action.CommandAction;
import com.alibaba.cloud.ai.graph.action.Command;

public class ConditionalNode implements CommandAction {
    @Override
    public Command apply(OverAllState state, RunnableConfig config) throws Exception {
        // 根据状态决定下一个节点
        String nextNode = determineNextNode(state);
        Map<String, Object> updates = Map.of("decision", "made");
        return new Command(nextNode, updates);
    }
}
```

## 4. 内置节点类型

### 4.1 LlmNode - LLM调用节点

```java
import com.alibaba.cloud.ai.graph.node.LlmNode;
import org.springframework.ai.chat.client.ChatClient;

// 创建LLM节点
LlmNode llmNode = LlmNode.builder()
    .chatClient(chatClient)
    .systemPrompt("You are a helpful assistant")
    .userPromptKey("user_input")  // 从状态中获取用户输入的key
    .outputKey("llm_response")    // 输出结果的key
    .build();
```

### 4.2 ToolNode - 工具调用节点

```java
import com.alibaba.cloud.ai.graph.node.ToolNode;
import org.springframework.ai.tool.ToolCallback;

// 创建工具节点
ToolNode toolNode = ToolNode.builder()
    .llmResponseKey("llm_response")  // LLM响应的key
    .outputKey("tool_result")        // 工具执行结果的key
    .toolCallbackResolver(resolver)   // 工具解析器
    .build();
```

### 4.3 KnowledgeRetrievalNode - 知识检索节点

```java
import com.alibaba.cloud.ai.graph.node.KnowledgeRetrievalNode;
import org.springframework.ai.vectorstore.VectorStore;

KnowledgeRetrievalNode retrievalNode = KnowledgeRetrievalNode.builder()
    .vectorStore(vectorStore)
    .userPromptKey("query")
    .topK(5)
    .similarityThreshold(0.7)
    .enableRanker(true)
    .build();
```

### 4.4 DocumentExtractorNode - 文档提取节点

```java
import com.alibaba.cloud.ai.graph.node.DocumentExtractorNode;

DocumentExtractorNode extractorNode = new DocumentExtractorNode(
    "file_paths",    // 输入文件路径的key
    "documents",     // 输出文档的key
    List.of("txt", "md", "html"), // 支持的文件类型
    true            // 输入是否为数组
);
```

### 4.5 ParameterParsingNode - 参数解析节点

```java
import com.alibaba.cloud.ai.graph.node.ParameterParsingNode;

ParameterParsingNode parsingNode = ParameterParsingNode.builder()
    .chatClient(chatClient)
    .inputTextKey("user_input")
    .outputKey("parsed_params")
    .parameters(List.of(
        new Parameter("name", "string", "用户姓名"),
        new Parameter("age", "number", "用户年龄")
    ))
    .build();
```

## 5. 状态图构建

### 5.1 基本图构建

```java
import com.alibaba.cloud.ai.graph.StateGraph;
import static com.alibaba.cloud.ai.graph.StateGraph.START;
import static com.alibaba.cloud.ai.graph.StateGraph.END;

// 创建状态图
StateGraph graph = new StateGraph();

// 添加节点
graph.addNode("node1", new CustomNode());
graph.addNode("node2", new AsyncCustomNode());

// 添加边
graph.addEdge(START, "node1");
graph.addEdge("node1", "node2");
graph.addEdge("node2", END);

// 编译图
CompiledGraph compiledGraph = graph.compile();
```

### 5.2 条件边

```java
// 添加条件边
graph.addConditionalEdges("decision_node", 
    (state) -> {
        String condition = (String) state.value("condition").orElse("");
        return switch (condition) {
            case "A" -> "nodeA";
            case "B" -> "nodeB";
            default -> END;
        };
    },
    Map.of("A", "nodeA", "B", "nodeB", "default", END)
);
```

### 5.3 子图

```java
// 创建子图
StateGraph subGraph = new StateGraph();
subGraph.addNode("sub_node1", new SubNode1());
subGraph.addNode("sub_node2", new SubNode2());
subGraph.addEdge(START, "sub_node1");
subGraph.addEdge("sub_node1", "sub_node2");
subGraph.addEdge("sub_node2", END);

// 将子图添加到主图
graph.addNode("sub_graph", subGraph);
graph.addEdge(START, "sub_graph");
graph.addEdge("sub_graph", END);
```

## 6. 图执行

### 6.1 同步执行

```java
// 准备初始状态
Map<String, Object> initialState = Map.of(
    "user_input", "Hello, world!",
    "messages", new ArrayList<>()
);

// 执行图
Optional<OverAllState> result = compiledGraph.invoke(initialState);
if (result.isPresent()) {
    OverAllState finalState = result.get();
    // 获取结果
    String output = (String) finalState.value("output").orElse("");
}
```

### 6.2 流式执行

```java
import com.alibaba.cloud.ai.graph.RunnableConfig;

// 创建配置
RunnableConfig config = RunnableConfig.builder()
    .threadId("thread-1")
    .build();

// 流式执行
var stream = compiledGraph.stream(initialState, config);
stream.forEach(chunk -> {
    // 处理每个执行步骤的结果
    System.out.println("Node: " + chunk.node());
    System.out.println("State: " + chunk.state());
});
```

## 7. 图可视化

### 7.1 PlantUML格式

```java
import com.alibaba.cloud.ai.graph.GraphRepresentation;

GraphRepresentation plantuml = compiledGraph.getGraph(GraphRepresentation.Type.PLANTUML);
System.out.println(plantuml.content());
```

### 7.2 Mermaid格式

```java
GraphRepresentation mermaid = compiledGraph.getGraph(GraphRepresentation.Type.MERMAID);
System.out.println(mermaid.content());
```

## 8. 错误处理和最佳实践

### 8.1 异常处理

```java
public class SafeNode implements NodeAction {
    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        try {
            // 业务逻辑
            return processLogic(state);
        } catch (Exception e) {
            // 记录错误并返回错误状态
            return Map.of("error", e.getMessage(), "success", false);
        }
    }
}
```

### 8.2 状态管理

```java
// 正确的状态更新方式
Map<String, Object> updates = new HashMap<>();
updates.put("step_count", currentStep + 1);
updates.put("last_result", result);
// 不要直接修改输入状态
return updates;
```

### 8.3 资源管理

```java
// 对于需要资源清理的节点
public class ResourceNode implements NodeAction {
    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        Resource resource = null;
        try {
            resource = acquireResource();
            return processWithResource(resource, state);
        } finally {
            if (resource != null) {
                resource.close();
            }
        }
    }
}
```

## 9. 扩展开发指南

### 9.1 自定义节点开发

1. 实现对应的Action接口
2. 处理OverAllState输入
3. 返回状态更新Map
4. 处理异常情况

### 9.2 自定义图生成器

```java
import com.alibaba.cloud.ai.graph.DiagramGenerator;

public class CustomDiagramGenerator extends DiagramGenerator {
    @Override
    protected void appendHeader(Context ctx) {
        // 实现自定义头部
    }
    
    @Override
    protected void appendFooter(Context ctx) {
        // 实现自定义尾部
    }
    
    // 实现其他抽象方法
}
```

## 10. 常见问题和解决方案

### 10.1 依赖注入

在Spring环境中使用：

```java
@Component
public class GraphService {
    
    @Autowired
    private ChatClient chatClient;
    
    public CompiledGraph createGraph() {
        StateGraph graph = new StateGraph();
        graph.addNode("llm", LlmNode.builder()
            .chatClient(chatClient)
            .build());
        return graph.compile();
    }
}
```

### 10.2 状态持久化

```java
// 使用检查点保存器
CompileConfig config = CompileConfig.builder()
    .checkpointSaver(checkpointSaver)
    .build();

CompiledGraph graph = stateGraph.compile(config);
```
````