## **Agent设计**

  

**通用能力**

1.  思维树体系集成
    
    *   思维树解析
        
    *   基于思维树的编排调度体系
        
2.  工具能力封装（cerebro调用）
    
    *   强制工具调用，保证工具调用的必然执行
        
3.  上下文管理
    
    *   任务焦点层
        
        *   执行指令
            
            *   系统指令
                
            *   用户指令
                
            *   上游指令
                
        *   执行状态
            
            *   ToT当前状态
                
            *   任务执行记录
                
        *   可用资源
            
            *   动态agent集
                
            *   下游产出物
                
    *   任务全景层
        
        *   任务全局视图
            
            *   ToT全局执行图
                
            *   任务参与者清单
                
        *   任务历史产出物
            
            *   执行过的节点输出摘要
                
            *   关键会话片段
                
        *   任务追踪
            
            *   任务里程碑记录
                
    *   战略记忆层
        
        *   会话全局背景
            
            *   下游agent中间过程
                
            *   会话历史摘要
                
4.  编排体系
    
    *   顺序流程编排
        
    *   调度体系
        
        *   ReAct模式循环调度
            
        *   根据思维树的调度（并行或者依赖）
            

**Agent体系**

基于上述通用能力的定制agent，例如中央协调者

*   特定的提示词
    
*   特定的工具
    
*   特定的调度模式
    

  

## 上下文设计

**任务焦点层**

为当前思维节点的单步推理提供必需的、即时的执行上下文。这是代理“正在思考”的内容。

完整性优先，是agent处理问题最关键的上下文

*   **执行指令 (Execution Directives)**：
    
    *   **系统级指令 (System Prompt)**：定义当前智能体的核心角色、目标和行为约束。
        
    *   **用户指令 (User Prompt)**：源自用户的原始问题及所有必要的澄清和补充细节。
        
    *   **上游节点指令（Upstream Prompt）**： 由上游节点（如Planner/Dispatcher Agent）传递给当前节点的**具体、可执行的子任务指令**。
        
*   **执行状态 (Execution State)**：
    
    *   **ToT 当前节点状态 (Current ToT Node State)**：描述当前思维树节点的具体目标
        
    *   **任务执行记录 (Task Execution Record)**：当前任务的关键进展摘要和状态更新。
        
*   **可用资源 (Available Resources)**：
    
    *   **动态Agent集 (Dynamic Agent Toolkit)**：当前节点引用的智能体（Agent）所能调用的子Agent（Tools）。
        
    *   **下游产出物 (Downstream Artifacts)**：（子Agent）输出的核心数据或摘要，作为任务补充信息
        

**任务全景层**

*   **核心作用**：提供完成当前整个任务（思维树）所需的背景信息和关联知识，帮助LLM理解任务全貌和节点之间的依赖关系。
    
*   **特性**：半动态性、任务周期内持久、相关性优先。在不超过上下文预算的前提下尽量完整，若超出则进行有策略的压缩或摘要。
    
*   **任务全局视图 (Global Task View)**：
    
    *   **ToT 全局执行图 (Global ToT Execution Graph)**：整个思维树的结构化表示，包含所有节点及其依赖关系。
        
    *   **任务参与者清单 (Task Actor Manifest)**：执行图中所有涉及的智能体（Agent）的角色和能力摘要。
        
*   **任务历史产出物 (Historical** Outputs**)**：
    
    *   **所有已执行节点的输出摘要 (Node Output Digest)**：思维树中所有已完成节点的输出结果的高度概括。
        
    *   **关键会话片段 (Key Interaction Logs)**：最近3-5轮与任务直接相关的核心交互记录。
        
*   **任务追踪 (Task Tracking)**：
    
    *   **任务里程碑记录 (Task Milestone)**：记录任务执行过程中的关键节点和已达成的阶段性目标。
        

**战略记忆层**

*   **核心作用**：提供跨任务、跨会话的长期记忆和通用背景知识，为任务规划和高级推理提供战略性指导。
    
*   **特性**：低动态性、持久性、选择性加载。此层信息在默认情况下不加载，仅在需要时通过特定检索机制（如向量搜索）提取最相关的片段注入上下文。
    
*   会话全局背景 (Global Conversation Context)
    
    *   **下游agent的中间过程（Downstream Progress）**：记录下游agent的中间过程数据
        
    *   **完整会话历史摘要 (Summarized Conversation)**：当前整个对话生命周期的会话历史摘要。
        

  

## 技术设计

1.  底层采用spring-ai-alibaba-graph
    
2.  ToT思维链控制agent流程
    
3.  作为一个框架层设计，业务可以实现这个框架暴露出去的接口和抽象，来拓展功能和传参（例如传入提示词，工具等等）  
    

  

### 头脑风暴

1.  重点在于上下文的设计集成，基于ToT的调度集成。框架层集成了这些，业务系统可以开箱即用
    
2.  在业务系统集成上，没想好怎么暴露埋点，哪些需要暴露，哪些作为框架封装的功能，这个需要帮助确认
    
3.  把我框架的自定义的特性，如何能融入到spring-ai-alibaba-graph的设计中，也不用过度融入，能用的就用，不能用的就自己开发