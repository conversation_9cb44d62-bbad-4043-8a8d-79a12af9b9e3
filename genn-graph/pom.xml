<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.genn.nova</groupId>
        <artifactId>genn-parent</artifactId>
        <version>${revision}</version>
        <relativePath>../genn-parent/pom.xml</relativePath>
    </parent>

    <artifactId>genn-graph</artifactId>
    <packaging>jar</packaging>
    <name>genn-graph</name>
    <description>genn graph</description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.genn.nova</groupId>
            <artifactId>genn-core</artifactId>
        </dependency>
        
        <dependency>
            <groupId>cn.genn.nova</groupId>
            <artifactId>genn-ai</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-graph-core</artifactId>
            <version>*******</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>