package cn.genn.security.component;


import cn.genn.security.constant.CodecType;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;

import javax.crypto.Cipher;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.*;
import java.util.HashMap;
import java.util.Map;

/**
 * RSA签名工具类
 *
 * <AUTHOR>
 */
public class RSAEncrypt {

    /**
     * 数字签名   MD5withRSA
     */
    public static final String SIGN_ALGORITHM_MD5WITHRSA = "MD5withRSA";

    /**
     * 数字签名   SHA1WithRSA
     */
    public static final String SIGN_ALGORITHM_SHA1WITHRSA = "SHA1WithRSA";

    /**
     * 数字签名   SHA1WithDSA
     */
    public static final String SIGN_ALGORITHM_SHA1WITHDSA = "SHA1WithDSA";

    public static final String PUBLIC_KEY = "RSAPublicKey";

    public static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * 生产密钥对
     *
     * @return
     */
    public static Map<String, String> genKeyPair() {
        return genKeyPair(CodecType.BASE64);
    }

    /**
     * 生产密钥对
     *
     * @param encodeMode
     * @return
     */
    public static Map<String, String> genKeyPair(CodecType encodeMode) {
        try {
            KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
            keyPairGen.initialize(1024, new SecureRandom());
            KeyPair keyPair = keyPairGen.generateKeyPair();
            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            Map<String, String> keyMap = new HashMap<>();
            switch (encodeMode) {
                case BASE64:
                    keyMap.put(PUBLIC_KEY, Base64.encode(publicKey.getEncoded()));
                    keyMap.put(PRIVATE_KEY, Base64.encode(privateKey.getEncoded()));
                    break;
                case HEX:
                    keyMap.put(PUBLIC_KEY, HexUtil.encodeHexStr(publicKey.getEncoded()));
                    keyMap.put(PRIVATE_KEY, HexUtil.encodeHexStr(privateKey.getEncoded()));
                    break;
                default:
                    keyMap.put(PUBLIC_KEY, new String(publicKey.getEncoded(), StandardCharsets.UTF_8));
                    keyMap.put(PRIVATE_KEY, new String(privateKey.getEncoded(), StandardCharsets.UTF_8));
            }
            return keyMap;
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("无此算法", e);
        }
    }

    /**
     * 从字符串中加载公钥
     *
     * @param publicKeyStr
     * @return
     */
    public static RSAPublicKey loadRSAPublicKey(String publicKeyStr) {
        return loadRSAPublicKey(publicKeyStr, CodecType.BASE64);
    }

    /**
     * 从字符串中加载公钥
     *
     * @param publicKeyStr
     * @param encodeMode
     * @return
     */
    public static RSAPublicKey loadRSAPublicKey(String publicKeyStr, CodecType encodeMode) {
        try {
            byte[] buffer;
            switch (encodeMode) {
                case BASE64:
                    buffer = Base64.decode(publicKeyStr);
                    break;
                case HEX:
                    buffer = HexUtil.decodeHex(publicKeyStr);
                    break;
                default:
                    buffer = publicKeyStr.getBytes(StandardCharsets.UTF_8);
            }
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(buffer);
            return (RSAPublicKey) keyFactory.generatePublic(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("无此算法", e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("非法公钥:" + publicKeyStr, e);
        }
    }

    /**
     * 从字符串中加载私钥
     *
     * @param privateKeyStr
     * @return
     */
    public static RSAPrivateKey loadRSAPrivateKey(String privateKeyStr) {
        return loadRSAPrivateKey(privateKeyStr, CodecType.BASE64);
    }

    /**
     * 从字符串中加载私钥
     *
     * @param privateKeyStr
     * @param encodeMode
     * @return
     */
    public static RSAPrivateKey loadRSAPrivateKey(String privateKeyStr, CodecType encodeMode) {
        try {
            byte[] buffer;
            switch (encodeMode) {
                case BASE64:
                    buffer = Base64.decode(privateKeyStr);
                    break;
                case HEX:
                    buffer = HexUtil.decodeHex(privateKeyStr);
                    break;
                default:
                    buffer = privateKeyStr.getBytes(StandardCharsets.UTF_8);
            }
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(buffer);
            return (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("无此算法", e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("非法私钥:" + privateKeyStr, e);
        }
    }

    /**
     * 私钥签名
     *
     * @param content
     * @param privateKey
     * @param signAlgorithm
     * @param encoding
     * @param codecType
     * @return
     */
    public static String sign(String content, RSAPrivateKey privateKey, String signAlgorithm, String encoding, CodecType codecType) {
        return sign(content, privateKey, signAlgorithm, Charset.forName(encoding), codecType);
    }

    /**
     * 私钥签名
     *
     * @param content
     * @param privateKey
     * @param signAlgorithm
     * @param charset
     * @param codecType
     * @return
     */
    public static String sign(String content, RSAPrivateKey privateKey, String signAlgorithm, Charset charset, CodecType codecType) {
        try {
            //实例化Signature
            Signature signature = Signature.getInstance(signAlgorithm);
            //初始化Signature
            signature.initSign(privateKey);
            //更新
            signature.update(content.getBytes(charset));

            byte[] signatureBytes = signature.sign();
            switch (codecType) {
                case BASE64:
                    return Base64.encode(signatureBytes);
                case HEX:
                    return HexUtil.encodeHexStr(signatureBytes);
                default:
                    return new String(signatureBytes, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            throw new RuntimeException("sign error:", e);
        }
    }

    /**
     * 公钥验证
     *
     * @param content
     * @param sign
     * @param publicKey
     * @param signAlgorithm
     * @param encoding
     * @param codecType
     * @return
     */
    public static boolean verify(String content, String sign, RSAPublicKey publicKey, String signAlgorithm, String encoding, CodecType codecType) {
        return verify(content, sign, publicKey, signAlgorithm, Charset.forName(encoding), codecType);
    }

    /**
     * 公钥验证
     *
     * @param content
     * @param sign
     * @param publicKey
     * @param signAlgorithm
     * @param charset
     * @param codecType
     * @return
     */
    public static boolean verify(String content, String sign, RSAPublicKey publicKey, String signAlgorithm, Charset charset, CodecType codecType) {
        try {
            //实例化Signature
            Signature signature = Signature.getInstance(signAlgorithm);
            //初始化Signature
            signature.initVerify(publicKey);
            //更新
            signature.update(content.getBytes(charset));
            byte[] signatureBytes;
            switch (codecType) {
                case BASE64:
                    signatureBytes = Base64.decode(sign);
                    break;
                case HEX:
                    signatureBytes = HexUtil.decodeHex(sign);
                    break;
                default:
                    signatureBytes = sign.getBytes(StandardCharsets.UTF_8);
            }
            return signature.verify(signatureBytes);
        } catch (Exception e) {
            throw new RuntimeException("verify error:", e);
        }
    }

    /**
     * 公钥解密
     *
     * @param publicKey
     * @param cipherText
     * @return
     */
    public static String decrypt(PublicKey publicKey, String cipherText) {
        return decryptByte(publicKey, Base64.decode(cipherText));
    }

    /**
     * 公钥解密
     *
     * @param publicKeyText
     * @param cipherText
     * @return
     */
    public static String decrypt(String publicKeyText, String cipherText) {
        RSAPublicKey publicKey = loadRSAPublicKey(publicKeyText);
        return decryptByte(publicKey, Base64.decode(cipherText));
    }

    /**
     * 公钥解密
     *
     * @param publicKey
     * @param cipherBytes
     * @return
     */
    public static String decryptByte(PublicKey publicKey, byte[] cipherBytes) {
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            try {
                cipher.init(2, publicKey);
            } catch (InvalidKeyException e) {
                RSAPublicKey rsaPublicKey = (RSAPublicKey)publicKey;
                RSAPrivateKeySpec spec = new RSAPrivateKeySpec(rsaPublicKey.getModulus(), rsaPublicKey.getPublicExponent());
                Key fakePrivateKey = KeyFactory.getInstance("RSA").generatePrivate(spec);
                cipher = Cipher.getInstance("RSA");
                cipher.init(Cipher.DECRYPT_MODE, fakePrivateKey);
            }
            byte[] plainBytes = cipher.doFinal(cipherBytes);
            return new String(plainBytes);
        } catch (Exception e) {
            throw new RuntimeException("decryptByte error:", e);
        }
    }

    /**
     * 私钥加密
     *
     * @param privateKey
     * @param plainText
     * @return
     */
    public static String encrypt(String privateKey, String plainText) {
        byte[] privateKeyBytes = Base64.decode(privateKey);
        return encrypt(privateKeyBytes, plainText);
    }

    /**
     * 私钥加密
     *
     * @param privateKeyBytes
     * @param plainText
     * @return
     */
    public static String encrypt(byte[] privateKeyBytes, String plainText) {
        byte[] bytes = encrypt2Byte(privateKeyBytes, plainText);
        return Base64.encode(bytes);
    }

    /**
     * 私钥加密
     *
     * @param privateKeyBytes
     * @param plainText
     * @return
     */
    public static byte[] encrypt2Byte(byte[] privateKeyBytes, String plainText) {
        try {
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory factory = KeyFactory.getInstance("RSA", "SunRsaSign");
            PrivateKey privateKey = factory.generatePrivate(spec);
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            try {
                cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            } catch (InvalidKeyException e) {
                RSAPrivateKey rsaPrivateKey = (RSAPrivateKey)privateKey;
                RSAPublicKeySpec publicKeySpec = new RSAPublicKeySpec(rsaPrivateKey.getModulus(), rsaPrivateKey.getPrivateExponent());
                Key fakePublicKey = KeyFactory.getInstance("RSA").generatePublic(publicKeySpec);
                cipher = Cipher.getInstance("RSA");
                cipher.init(Cipher.ENCRYPT_MODE, fakePublicKey);
            }
            return cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException("encrypt2Byte error:", e);
        }
    }
}
