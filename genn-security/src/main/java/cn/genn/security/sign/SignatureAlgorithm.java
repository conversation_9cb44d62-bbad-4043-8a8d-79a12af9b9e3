package cn.genn.security.sign;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public interface SignatureAlgorithm {

    /**
     * 生成签名
     * @param sourceGen 生成签名源的方法
     * @param secret 密钥
     * @return
     */
    String makeSign(Supplier<String> sourceGen, String secret);

    /**
     * 验证签名
     * @param sourceGen 生成签名源的方法
     * @param requestSign 请求签名
     * @param secret 密钥
     * @return
     */
    boolean validate(Supplier<String> sourceGen, String requestSign, String secret);

}
