package cn.genn.security.sign;

import cn.genn.security.constant.CodecType;
import cn.genn.security.constant.SignAlgorithm;
import cn.genn.security.util.SecurityUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
public class SignatureAlgorithmImpl implements SignatureAlgorithm {

    private final SignAlgorithm signAlgorithm;

    private final CodecType codecType;

    @Setter
    private Charset charset = StandardCharsets.UTF_8;

    public SignatureAlgorithmImpl(SignAlgorithm signAlgorithm) {
        this.signAlgorithm = signAlgorithm;
        this.codecType = CodecType.HEX;
    }

    public SignatureAlgorithmImpl(SignAlgorithm signAlgorithm, CodecType codecType) {
        this.signAlgorithm = signAlgorithm;
        this.codecType = codecType;
    }

    @Override
    public String makeSign(Supplier<String> sourceGen, String secret) {
        String source = sourceGen.get();
        if (source == null) {
            source = "";
        }
        switch (signAlgorithm) {
            case MD5:
            case SHA_1:
            case SHA_256:
                return SecurityUtil.md(source, signAlgorithm.getAlgorithm(), charset, codecType);
            case HmacMD5:
            case HmacSHA1:
            case HmacSHA256:
                return SecurityUtil.mac(secret, source, signAlgorithm.getAlgorithm(), charset, codecType);
            case MD5withRSA:
            case SHA1withDSA:
            case SHA1withRSA:
            case SHA256withRSA:
                return SecurityUtil.rsaSign(source, secret, signAlgorithm, charset, codecType);
            default:
                throw new UnsupportedOperationException();
        }
    }

    @Override
    public boolean validate(Supplier<String> sourceGen, String requestSign, String secret) {
        if (StringUtils.isEmpty(requestSign)) {
            return false;
        }
        String source = sourceGen.get();
        if (StringUtils.isEmpty(source)) {
            return false;
        }
        String serverSign = null;
        boolean result;
        try {
            switch (signAlgorithm) {
                case MD5:
                case SHA_1:
                case SHA_256:
                    serverSign = SecurityUtil.md(source, signAlgorithm.getAlgorithm(), charset, codecType);
                    result = requestSign.equals(serverSign);
                    break;
                case HmacMD5:
                case HmacSHA1:
                case HmacSHA256:
                    serverSign = SecurityUtil.mac(secret, source, signAlgorithm.getAlgorithm(), charset, codecType);
                    result = requestSign.equals(serverSign);
                    break;
                case MD5withRSA:
                case SHA1withDSA:
                case SHA1withRSA:
                case SHA256withRSA:
                    result = SecurityUtil.rsaVerify(source, requestSign, secret, signAlgorithm, charset, codecType);
                    break;
                default:
                    throw new UnsupportedOperationException();
            }
        } catch (Exception e) {
            log.error("check sign error: {}", e.getMessage());
            result = false;
        }
        if (!result) {
            log.info("check sign error: serverSign ={}, requestSign = {}, source = {}", serverSign, requestSign, source);
        }
        return result;
    }
}
