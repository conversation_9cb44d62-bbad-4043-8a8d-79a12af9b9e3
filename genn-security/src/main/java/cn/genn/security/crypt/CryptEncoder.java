package cn.genn.security.crypt;

import java.nio.charset.Charset;

/**
 * <AUTHOR>
 */
public interface CryptEncoder {

    /**
     * 加密数据
     *
     * @param bytes    待加密数据字节码
     * @return 密文
     */
    default String encrypt(byte[] bytes) {
        return encrypt(bytes, null);
    }

    /**
     * 加密数据
     *
     * @param bytes          待加密数据字节码
     * @param cryptGroupName 指定加密组
     * @return 密文
     */
    String encrypt(byte[] bytes, String cryptGroupName);

    /**
     * 加密数据
     *
     * @param content  明文
     * @return 密文
     */
    default String encrypt(String content) {
        return encrypt(content, null, null);
    }

    /**
     * 加密数据
     *
     * @param content 明文
     * @param charset 指定字符集
     * @return
     */
    default String encrypt(String content, Charset charset) {
        return encrypt(content, null, charset);
    }

    /**
     * 加密数据
     *
     * @param content        明文
     * @param cryptGroupName 指定加密组
     * @return 密文
     */
    default String encrypt(String content, String cryptGroupName) {
        return encrypt(content, cryptGroupName, null);
    }

    /**
     * 加密数据
     *
     * @param content        明文
     * @param cryptGroupName 指定加密组
     * @return
     */
    String encrypt(String content, String cryptGroupName, Charset charset);

    /**
     * 解密数据
     *
     * @param content  密文
     * @return 明文
     */
    default String decrypt(String content) {
        return decrypt(content, null, null);
    }

    /**
     * 解密数据
     *
     * @param content 密文
     * @param charset 指定字符集
     * @return 明文
     */
    default String decrypt(String content, Charset charset) {
        return decrypt(content, null, charset);
    }

    /**
     * 解密数据
     *
     * @param content  密文
     * @param cryptGroupName 指定加密组
     * @return 明文
     */
    default String decrypt(String content, String cryptGroupName) {
        return decrypt(content, cryptGroupName, null);
    }

    /**
     * 解密数据
     *
     * @param content        密文
     * @param cryptGroupName 指定加密组
     * @param charset        指定字符集
     * @return
     */
    String decrypt(String content, String cryptGroupName, Charset charset);
}
