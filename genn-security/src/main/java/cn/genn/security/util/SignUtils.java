package cn.genn.security.util;


import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class SignUtils {

    public static final String DEFAULT_JOINER = "&";
    public static final String DEFAULT_PV_JOINER = "=";

    /**
     * 过滤空值参数和特定参数.
     *
     * @return
     */
    public static Map<String, String> filterParams(Map<String, String> srcParams, boolean filterEmpty, String[] exceptKeys) {
        if (srcParams == null || srcParams.isEmpty()) {
            return null;
        }
        List<String> exceptList = new ArrayList<>();
        if(exceptKeys != null) {
            exceptList = Arrays.asList(exceptKeys);
        }
        Map<String, String> desParams = new HashMap<String, String>();
        for (Map.Entry<String, String> srcEntry : srcParams.entrySet()) {
            if(filterEmpty && StringUtils.isBlank(srcEntry.getValue())) {
                continue;
            }
            if (exceptList.contains(srcEntry.getKey())) {
                continue;
            }
            desParams.put(srcEntry.getKey(), srcEntry.getValue());
        }
        return desParams;
    }

    /**
     * 连接参数.
     *
     * @param srcParams   参数map
     *
     */
    public static String linkParams(Map<String, String> srcParams) {
        return linkParams(srcParams, null);
    }

    /**
     * 连接参数.
     *
     * @param srcParams   参数map
     * @param exceptKeys  排除key
     *
     */
    public static String linkParams(Map<String, String> srcParams, String[] exceptKeys) {
        return linkParams(srcParams, exceptKeys, true, true);
    }

    /**
     * 连接参数
     *
     * @param srcParams   参数map
     * @param exceptKeys  排除key
     * @param sortByKey   是否按key排序
     * @param filterNull  移除空值
     *
     */
    public static String linkParams(Map<String, String> srcParams, String[] exceptKeys, boolean sortByKey, boolean filterNull) {
        return linkParams(srcParams, exceptKeys, sortByKey, filterNull, false, DEFAULT_JOINER, DEFAULT_PV_JOINER);
    }

    /**
     * 连接参数值
     *
     * @param srcParams   参数map
     * @param exceptKeys  排除key
     * @param sortByKey   是否按key排序
     * @param filterNull  移除空值
     * @return
     */
    public static String linkParamsValues(Map<String, String> srcParams, String[] exceptKeys, boolean sortByKey, boolean filterNull) {
        return linkParams(srcParams, exceptKeys, sortByKey, filterNull, true, DEFAULT_JOINER, null);
    }

    /**
     * 连接参数值
     *
     * @param srcParams   参数map
     * @param joinStr  参数连接符
     * @param sortByKey   是否按key排序
     * @param filterNull  移除空值
     * @return
     */
    public static String linkParamsValues(Map<String, String> srcParams, String joinStr, boolean sortByKey, boolean filterNull) {
        return linkParams(srcParams, null, sortByKey, filterNull, true, joinStr, null);
    }

    /**
     * 连接参数值
     *
     * @param srcParams   参数map
     * @param joinStr  参数连接符
     * @return
     */
    public static String linkParamsValues(Map<String, String> srcParams, String joinStr) {
        return linkParamsValues(srcParams, joinStr, true, true);
    }

    /**
     * 连接参数值
     *
     * @param srcParams   参数map
     * @return
     */
    public static String linkParamsValues(Map<String, String> srcParams) {
        return linkParamsValues(srcParams, null);
    }

    /**
     * 连接参数
     *
     * @param srcParams   参数map
     * @param exceptKeys  排除key
     * @param sortByKey   是否按key排序
     * @param filterNull  移除空值
     * @param onlyValue   只拼接值
     * @param joinStr     参数连接符
     * @param pvJoinStr   键值连接符
     * @return
     */
    public static String linkParams(Map<String, String> srcParams,
                                    String[] exceptKeys,
                                    boolean sortByKey,
                                    boolean filterNull,
                                    boolean onlyValue,
                                    String joinStr,
                                    String pvJoinStr) {
        List<String> exceptList = new ArrayList<>();
        if(exceptKeys != null) {
            exceptList = Arrays.asList(exceptKeys);
        }
        // 过滤空值
        srcParams = filterParams(srcParams, filterNull, exceptKeys);
        if (srcParams == null || srcParams.isEmpty()) {
            return null;
        }
        // 排序，连接
        Map<String, String> desParams = sortByKey ? new TreeMap<>(srcParams) : srcParams;
        StringBuilder desSb = new StringBuilder();
        int i = 0;
        for (Map.Entry<String, String> entry : desParams.entrySet()) {
            if (exceptList.contains(entry.getKey())) {
                continue;
            }
            if (i > 0) {
                desSb.append(joinStr);
            }
            if(!onlyValue) {
                desSb.append(entry.getKey()).append(pvJoinStr);
            }
            desSb.append(entry.getValue());
            i++;
        }
        return desSb.toString();
    }

}
