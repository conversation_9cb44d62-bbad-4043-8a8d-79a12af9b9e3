package cn.genn.security.util;

import cn.genn.security.constant.CodecType;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class ByteUtils {

    public static String byte2Str(byte[] content) {
        return byte2Str(content, StandardCharsets.UTF_8);
    }

    public static String byte2Str(byte[] content, String encode) {
        try {
            return new String(content, encode);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String byte2Str(byte[] content, Charset charset) {
        return new String(content, charset);
    }

    public static byte[] str2Byte(String content) {
        return str2Byte(content, StandardCharsets.UTF_8);
    }

    public static byte[] str2Byte(String content, String encode) {
        try {
            return content.getBytes(encode);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static byte[] str2Byte(String content, Charset charset) {
        return content.getBytes(charset);
    }

    public static String bytePrint(long num, int bitNum) {
        StringBuilder ret = new StringBuilder();
        for (int i = bitNum - 1; i >= 0; i--) {
            ret.append((num >> i) & 1);
        }
        return ret.toString();
    }

    public static byte[] str2Byte(String key, CodecType encodeMode) {
        byte[] bytes;
        switch (encodeMode) {
            case HEX:
                bytes = HexUtil.decodeHex(key);
                break;
            case BASE64:
                bytes = Base64.decode(key);
                break;
            default:
                bytes = key.getBytes(StandardCharsets.UTF_8);
        }
        return bytes;
    }

    public static String byte2Str(byte[] bytes, CodecType encodeMode) {
        String data;
        switch (encodeMode) {
            case HEX:
                data = HexUtil.encodeHexStr(bytes);
                break;
            case BASE64:
                data = Base64.encode(bytes);
                break;
            default:
                data = new String(bytes, StandardCharsets.UTF_8);
        }
        return data;
    }
}

