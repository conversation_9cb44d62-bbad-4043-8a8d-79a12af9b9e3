package cn.genn.security.util;

import cn.genn.security.constant.CodecType;
import cn.genn.security.constant.SignAlgorithm;
import cn.genn.security.sign.SignatureAlgorithm;
import cn.genn.security.sign.SignatureAlgorithmImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 网关签名工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class GatewaySignUtils {

    public static SignatureAlgorithm gennSignatureAlgorithm = new SignatureAlgorithmImpl(SignAlgorithm.HmacSHA256, CodecType.HEX);

    /**
     * 生成认证头
     *
     * @param appId       appId
     * @param appSecret   appSecret
     * @param httpMethod  httpMethod
     * @param requestPath requestPath
     * @return 认证头, 格式为GENN-HUB appId:sign,需要放在Authorization头中
     */
    public static String generateAuthHeader(String appId,
                                            String appSecret,
                                            String httpMethod,
                                            String requestPath,
                                            long timestamp,
                                            String nonce) {
        String sign = gennSignatureAlgorithm.makeSign(() -> {
            List<String> sourceList = new ArrayList<>();
            sourceList.add("GENN-HUB");
            sourceList.add(httpMethod.toUpperCase());
            sourceList.add(requestPath);
            sourceList.add(String.valueOf(timestamp));
            sourceList.add(nonce);
            return String.join("\n", sourceList);
        }, appSecret);
        return "GENN-HUB" + " " + appId + ":" + sign;
    }

    public static String appendUrl(String hubGatewayUrl, long timestamp, String nonce) {
        return hubGatewayUrl + "?timestamp=" + timestamp + "&nonce=" + nonce;
    }
}
