package cn.genn.security.util;

import cn.genn.security.component.RSAEncrypt;
import cn.genn.security.constant.CodecType;
import cn.genn.security.constant.SignAlgorithm;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;

/**
 * 安全工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SecurityUtil {

    /**
     * MD5 HEX
     *
     * @param source
     * @return
     */
    public static String md5(String source) {
        return md(source, SignAlgorithm.MD5.getAlgorithm(), StandardCharsets.UTF_8, CodecType.HEX);
    }

    /**
     * MD5 HEX
     *
     * @param bytes
     * @return
     */
    public static String md5(byte[] bytes) {
        byte[] digestBytes = md(bytes, SignAlgorithm.MD5.getAlgorithm());
        return HexUtil.encodeHexStr(digestBytes);
    }

    /**
     * SHA-1 HEX
     *
     * @param source
     * @return
     */
    public static String sha1(String source) {
        return md(source, SignAlgorithm.SHA_1.getAlgorithm(), StandardCharsets.UTF_8, CodecType.HEX);
    }

    /**
     * SHA-1 Base64
     *
     * @param source
     * @return
     */
    public static String sha1Base64(String source) {
        return md(source, SignAlgorithm.SHA_1.getAlgorithm(), StandardCharsets.UTF_8, CodecType.BASE64);
    }


    /**
     * SHA-256 HEX
     *
     * @param source
     * @return
     */
    public static String sha256(String source) {
        return md(source, SignAlgorithm.SHA_256.getAlgorithm(), StandardCharsets.UTF_8, CodecType.HEX);
    }

    /**
     * SHA-256 Base64
     *
     * @param source
     * @return
     */
    public static String sha256Base64(String source) {
        return md(source, SignAlgorithm.SHA_256.getAlgorithm(), StandardCharsets.UTF_8, CodecType.BASE64);
    }

    /**
     * HmacMd5 HEX
     *
     * @param source
     * @return
     */
    public static String HmacMd5(String secret, String source) {
        return mac(secret, source, SignAlgorithm.HmacMD5.getAlgorithm(), StandardCharsets.UTF_8, CodecType.HEX);
    }

    /**
     * HmacMd5 Base64
     *
     * @param source
     * @return
     */
    public static String HmacMd5Base64(String secret, String source) {
        return mac(secret, source, SignAlgorithm.HmacMD5.getAlgorithm(), StandardCharsets.UTF_8, CodecType.BASE64);
    }

    /**
     * HmacSHA1 HEX
     *
     * @param source
     * @return
     */
    public static String HmacSha1(String secret, String source) {
        return mac(secret, source, SignAlgorithm.HmacSHA1.getAlgorithm(), StandardCharsets.UTF_8, CodecType.HEX);
    }

    /**
     * HmacSHA1 Base64
     *
     * @param source
     * @return
     */
    public static String HmacSha1Base64(String secret, String source) {
        return mac(secret, source, SignAlgorithm.HmacSHA1.getAlgorithm(), StandardCharsets.UTF_8, CodecType.BASE64);
    }

    /**
     * hmacSHA256 HEX
     *
     * @param source
     * @return
     */
    public static String hmacSha256(String secret, String source) {
        return mac(secret, source, SignAlgorithm.HmacSHA256.getAlgorithm(), StandardCharsets.UTF_8, CodecType.HEX);
    }

    /**
     * hmacSHA256 Base64
     *
     * @param source
     * @return
     */
    public static String hmacSha256Base64(String secret, String source) {
        return mac(secret, source, SignAlgorithm.HmacSHA256.getAlgorithm(), StandardCharsets.UTF_8, CodecType.BASE64);
    }


    /**
     * 签名
     *
     * @param source
     * @param signAlgorithm
     * @param encoding
     * @return
     */
    @Deprecated
    public static String md(String source, String signAlgorithm, String encoding, CodecType codecType) {
        return md(source, signAlgorithm, Charset.forName(encoding), codecType);
    }

    /**
     * 签名
     *
     * @param source
     * @param signAlgorithm
     * @param charset
     * @return
     */
    public static String md(String source, String signAlgorithm, Charset charset, CodecType codecType) {
        byte[] sourceBytes = ByteUtils.str2Byte(source, charset);
        byte[] digestBytes = md(sourceBytes, signAlgorithm);
        switch (codecType) {
            case BASE64:
                return Base64.encode(digestBytes);
            case HEX:
                return HexUtil.encodeHexStr(digestBytes);
            default:
                throw new UnsupportedOperationException();
        }
    }

    public static byte[] md(byte[] sourceBytes, String signAlgorithm) {
        try {
            MessageDigest md = MessageDigest.getInstance(signAlgorithm);
            md.update(sourceBytes);
            return md.digest();
        } catch (NoSuchAlgorithmException e) {
            log.error("MessageDigest Exception", e);
            throw new RuntimeException(e);
        }
    }

    @Deprecated
    public static String mac(String secret, String source, String signAlgorithm, String encoding, CodecType codecType) {
        return mac(secret, source, signAlgorithm, Charset.forName(encoding), codecType);
    }

    public static String mac(String secret, String source, String signAlgorithm, Charset charset, CodecType codecType) {
        byte[] sourceBytes = ByteUtils.str2Byte(source, charset);
        byte[] secretBytes = ByteUtils.str2Byte(secret, charset);
        byte[] digestBytes = mac(secretBytes, sourceBytes, signAlgorithm);
        switch (codecType) {
            case BASE64:
                return Base64.encode(digestBytes);
            case HEX:
                return HexUtil.encodeHexStr(digestBytes);
            default:
                throw new UnsupportedOperationException();
        }
    }

    public static byte[] mac(byte[] secretBytes, byte[] sourceBytes, String signAlgorithm) {
        try {
            Mac mac = Mac.getInstance(signAlgorithm);
            SecretKeySpec secretKey = new SecretKeySpec(secretBytes, signAlgorithm);
            mac.init(secretKey);
            return mac.doFinal(sourceBytes);
        } catch (InvalidKeyException | NoSuchAlgorithmException e) {
            log.error("Mac Exception", e);
            throw new RuntimeException(e);
        }
    }


    public static String rsaSign(String source, String privateKey) {
        return rsaSign(source, privateKey, SignAlgorithm.SHA1withRSA, StandardCharsets.UTF_8, CodecType.BASE64);
    }

    public static String rsaSign(String source, String privateKey, SignAlgorithm signAlgorithm, Charset charset, CodecType codecType) {
        return rsaSign(source, privateKey, signAlgorithm.getAlgorithm(), charset, codecType);
    }

    public static String rsaSign(String source, String privateKey, String signAlgorithm, Charset charset, CodecType codecType) {
        RSAPrivateKey priKey = RSAEncrypt.loadRSAPrivateKey(privateKey);
        return RSAEncrypt.sign(source, priKey, signAlgorithm, charset, codecType);
    }

    public static boolean rsaVerify(String source, String requestSign, String publicKey) {
        return rsaVerify(source, requestSign, publicKey, SignAlgorithm.SHA1withRSA, StandardCharsets.UTF_8, CodecType.BASE64);
    }

    public static boolean rsaVerify(String source, String requestSign, String publicKey, SignAlgorithm signAlgorithm, Charset charset, CodecType codecType) {
        return rsaVerify(source, requestSign, publicKey, signAlgorithm.getAlgorithm(), charset, codecType);
    }

    public static boolean rsaVerify(String source, String requestSign, String publicKey, String signAlgorithm, Charset charset, CodecType codecType) {
        RSAPublicKey pubKey = RSAEncrypt.loadRSAPublicKey(publicKey);
        return RSAEncrypt.verify(source, requestSign, pubKey, signAlgorithm, charset, codecType);
    }

    public static String[] generateRSAKey() {
        String[] appInfo = new String[2];
        RSA rsa = new RSA();
        appInfo[0] = rsa.getPrivateKeyBase64();
        appInfo[1] = rsa.getPublicKeyBase64();
        return appInfo;
    }
}
