package cn.genn.security.constant;

import lombok.Getter;

import java.util.HashMap;

/**
 * 签名算法
 * <AUTHOR>
 */
public enum SignAlgorithm {

    MD5("MD5"),
    SHA_1("SHA-1"),
    SHA_256("SHA-256"),

    Hmac<PERSON>5("HmacMD5"),
    HmacSHA1("HmacSHA1"),
    HmacS<PERSON>256("HmacSHA256"),

    MD5withRSA("MD5withRSA"),
    SHA1withDSA("SHA1withDSA"),
    SHA1withRSA("SHA1withRSA"),
    SHA256withRSA("SHA256withRSA");

    @Getter
    private String algorithm;

    SignAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    private static HashMap<String, SignAlgorithm> VALUES = new HashMap<>();

    static {
        for (final SignAlgorithm val : SignAlgorithm.values()) {
            VALUES.put(val.getAlgorithm(), val);
        }
    }

    public static SignAlgorithm of(String algorithm) {
        return VALUES.get(algorithm);
    }

}
