package cn.genn.bridge.feign;

import cn.genn.bridge.BridgeUtils;
import cn.genn.bridge.properties.BridgeProperties;
import cn.genn.bridge.tenant.BridgeTenantHolder;
import cn.genn.core.utils.remote.BridgeReplaceServiceInstanceHolder;
import cn.genn.core.utils.remote.BridgeServiceInstance;
import cn.genn.web.feign.component.FeignCustomizer;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import feign.RequestTemplate;
import feign.Target;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;
import java.util.List;

import static cn.genn.web.WebConstants.*;

/**
 * 对于通过公网/跨 cce 的调用，如果使用目标的 feign api 包，可以通过该拦截器进行适配
 *
 * <AUTHOR>
 */
@Slf4j
public class FeignBridgeInterceptor implements FeignCustomizer {

    @Resource
    private BridgeProperties bridgeProperties;

    @Override
    public void customize(HttpServletRequest request, RequestTemplate requestTemplate) {
        BridgeReplaceServiceInstanceHolder.remove();
        Target<?> target = requestTemplate.feignTarget();
        String targetServiceName = target.name();

        requestTemplate.header(BRIDGE_ORIGIN_CLUSTER, bridgeProperties.getClusterId());
        requestTemplate.header(BRIDGE_ORIGIN_SERVICE, SpringUtil.getApplicationName());
        requestTemplate.header(BRIDGE_TARGET_SERVICE, targetServiceName);

        List<String> platformServices = bridgeProperties.getPlatformServices();
        if (platformServices.contains(targetServiceName)) {
            routePlatformService(request, requestTemplate, target, targetServiceName);
            return;
        }
        routeTenantService(request, requestTemplate, target, targetServiceName);
    }

    private void routeTenantService(HttpServletRequest request, RequestTemplate requestTemplate, Target<?> target, String targetServiceName) {
        Long tenantId = BridgeTenantHolder.get();
        if (tenantId == null) {
            return;
        }
        String host = URLUtil.url(target.url()).getHost();
        if (host.equals(targetServiceName)) {
            String targetBaseUrl = BridgeUtils.getTargetTenantUrl(bridgeProperties, tenantId);
            if (targetBaseUrl != null) {
                fillAuthInfo(requestTemplate);
                storeServiceInstance(targetBaseUrl, targetServiceName);
            }
        }
    }

    private void routePlatformService(HttpServletRequest request, RequestTemplate requestTemplate, Target<?> target, String targetServiceName) {
        String host = URLUtil.url(target.url()).getHost();
        if (host.equals(targetServiceName)) {
            String targetBaseUrl = BridgeUtils.getTargetPlatformUrl(bridgeProperties, targetServiceName);
            if (targetBaseUrl != null) {
                fillAuthInfo(requestTemplate);
                storeServiceInstance(targetBaseUrl, targetServiceName);
            }
        }
    }

    private void fillAuthInfo(RequestTemplate requestTemplate) {
        String nonce = IdUtil.fastSimpleUUID();
        requestTemplate.header(BRIDGE_NONCE, nonce);
        requestTemplate.header(BRIDGE_AUTHORIZATION,
                BridgeUtils.generateAuthHeader(bridgeProperties.getPrivateKey(), requestTemplate.method(), nonce));
    }

    private void storeServiceInstance(String targetBaseUrl, String serviceName) {
        URL url = URLUtil.url(targetBaseUrl);
        BridgeServiceInstance serviceInstance = new BridgeServiceInstance(serviceName, url.getHost(), url.getPort());
        BridgeReplaceServiceInstanceHolder.set(serviceInstance);
    }
}
