package cn.genn.bridge.tenant;

/**
 * 如果手动设置，需要及时清理
 * <AUTHOR>
 */
public class BridgeTenantHolder {

    public static final ThreadLocal<Long> TENANT_ID = new ThreadLocal<>();

    public static void set(Long tenantId) {
        TENANT_ID.set(tenantId);
    }

    public static Long get() {
        return TENANT_ID.get();
    }

    public static void clear() {
        TENANT_ID.remove();
    }

}
