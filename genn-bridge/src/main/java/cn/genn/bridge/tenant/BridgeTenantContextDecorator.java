package cn.genn.bridge.tenant;

import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;

/**
 * 租户桥接上下文装饰器
 *
 * <AUTHOR>
 */
public class BridgeTenantContextDecorator extends BaseThreadDecorator {

    public BridgeTenantContextDecorator() {
        super();
    }

    public BridgeTenantContextDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }

    @Override
    protected Object beforeExecOnCurrThread() {
        return BridgeTenantHolder.get();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof Long) {
            BridgeTenantHolder.set((Long) object);
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        BridgeTenantHolder.clear();
    }
}
