package cn.genn.bridge.tenant;

import cn.genn.core.utils.SpelUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.expression.spel.support.StandardEvaluationContext;

/**
 * 租户桥接切面
 * <AUTHOR>
 */
@Aspect
@Slf4j
public class BridgeTenantAspect {

    @Around("@annotation(bridgeTenant)")
    public Object bridgeTenant(ProceedingJoinPoint joinPoint, BridgeTenant bridgeTenant) throws Throwable {
        String tenant = bridgeTenant.tenant();
        StandardEvaluationContext spelContext = SpelUtils.initSpelContext(joinPoint);
        try {
            Long tenantId = SpelUtils.parseSpelExpression(spelContext, tenant, Long.class);
            BridgeTenantHolder.set(tenantId);
            return joinPoint.proceed();
        } finally {
            BridgeTenantHolder.clear();
        }
    }
}

