package cn.genn.bridge.properties;

import cn.genn.bridge.enums.ClusterTypeEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * 微服务调用桥接配置
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "genn.bridge")
public class BridgeProperties {

    /**
     * 集群id
     */
    private String clusterId;

    /**
     * 集群类型
     */
    private ClusterTypeEnum clusterType;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 平台服务
     */
    private List<String> platformServices = new ArrayList<>();

    /**
     * 平台路由配置
     */
    @NestedConfigurationProperty
    private BridgePlatformProperties platform = new BridgePlatformProperties();

    /**
     * 租户配置
     */
    private List<BridgeTenantProperties> tenant = new ArrayList<>();

    /**
     * 集群 地址
     */
    private List<BridgeRoutesProperties> routes = new ArrayList<>();

}
