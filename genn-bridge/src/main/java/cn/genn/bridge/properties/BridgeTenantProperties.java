package cn.genn.bridge.properties;

import cn.genn.bridge.enums.InvokeTypeEnum;
import lombok.Data;

import java.util.Set;

/**
 * 微服务调用桥接集群配置
 * 用于平台调用不同集群租户
 *
 * <AUTHOR>
 */
@Data
public class BridgeTenantProperties {

    /**
     * 集群id
     */
    private String clusterId;

    /**
     * 包含的租户id
     */
    private Set<Long> tenantIds;

    /**
     * 调用类型
     */
    private InvokeTypeEnum invokeType;

    /**
     * 网关地址，内网/公网
     */
    private String gatewayUrl;
}
