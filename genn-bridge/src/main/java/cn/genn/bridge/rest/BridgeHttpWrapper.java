package cn.genn.bridge.rest;

import cn.hutool.core.util.URLUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.support.HttpRequestWrapper;

import java.net.URI;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class BridgeHttpWrapper extends HttpRequestWrapper {


    private final String targetBaseUrl;
    private final Map<String, String> httpHeaders;

    public BridgeHttpWrapper(HttpRequest request, String targetBaseUrl, Map<String, String> httpHeaders) {
        super(request);
        this.targetBaseUrl = targetBaseUrl;
        this.httpHeaders = httpHeaders;
    }

    @Override
    public URI getURI() {
        URI originalUri = super.getURI();
        String path = originalUri.getPath();
        String query = originalUri.getQuery();
        String fragment = originalUri.getFragment();

        StringBuilder newUriString = new StringBuilder(URLUtil.completeUrl(targetBaseUrl, path));

        if (query != null && !query.isEmpty()) {
            newUriString.append("?").append(query);
        }

        if (fragment != null && !fragment.isEmpty()) {
            newUriString.append("#").append(fragment);
        }

        return URI.create(newUriString.toString());
    }

    @Override
    public HttpHeaders getHeaders() {
        HttpHeaders headers = super.getHeaders();
        if (httpHeaders != null) {
            httpHeaders.forEach(headers::add);
        }
        return headers;
    }
}
