package cn.genn.bridge.rest;


import cn.genn.bridge.BridgeUtils;
import cn.genn.bridge.properties.BridgeProperties;
import cn.genn.bridge.tenant.BridgeTenantHolder;
import cn.genn.core.utils.remote.BridgeReplaceServiceInstanceHolder;
import cn.genn.core.utils.remote.BridgeServiceInstance;
import cn.genn.web.spring.component.request.RestTemplateInterceptor;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.support.HttpRequestWrapper;

import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.genn.web.WebConstants.*;

/**
 * RestTemplate桥接拦截器, 需要配合 lbRestTemplate 使用
 * <p>
 * <code>
 *  private RestTemplate lbRestTemplate;
 * </code>
 *
 * <AUTHOR>
 */
public class RestTemplateBridgeInterceptor implements RestTemplateInterceptor.RestTemplateRequestCustomizer {

    @Override
    public HttpRequestWrapper customize(HttpRequest request) {
        BridgeReplaceServiceInstanceHolder.remove();
        URI uri = request.getURI();
        String targetServiceName = URLUtil.url(uri).getHost();
        if (!targetServiceName.startsWith("genn-")) {
            //非应用名请求，直接返回
            return new HttpRequestWrapper(request);
        }
        BridgeProperties bridgeProperties = SpringUtil.getBean(BridgeProperties.class);
        List<String> platformServices = bridgeProperties.getPlatformServices();

        Map<String, String> bridgeHeaders = new HashMap<>();

        bridgeHeaders.put(BRIDGE_ORIGIN_CLUSTER, bridgeProperties.getClusterId());
        bridgeHeaders.put(BRIDGE_ORIGIN_SERVICE, SpringUtil.getApplicationName());
        bridgeHeaders.put(BRIDGE_TARGET_SERVICE, targetServiceName);

        String targetBaseUrl;

        if (platformServices.contains(targetServiceName)) {
            targetBaseUrl = routePlatformService(request, bridgeProperties, targetServiceName);
        } else {
            targetBaseUrl = routeTenantService(request, bridgeProperties, targetServiceName);
        }
        if (targetBaseUrl == null) {
            return new HttpRequestWrapper(request);
        }

        String nonce = IdUtil.fastSimpleUUID();
        bridgeHeaders.put(BRIDGE_NONCE, nonce);
        bridgeHeaders.put(BRIDGE_AUTHORIZATION,
                BridgeUtils.generateAuthHeader(bridgeProperties.getPrivateKey(), Objects.requireNonNull(request.getMethod()).name(), nonce));
        storeServiceInstance(targetBaseUrl, targetServiceName);
        return new BridgeHttpWrapper(request, targetBaseUrl, bridgeHeaders);
    }

    private String routeTenantService(HttpRequest request, BridgeProperties bridgeProperties, String targetServiceName) {
        Long tenantId = BridgeTenantHolder.get();
        if (tenantId == null) {
            return null;
        }
        return BridgeUtils.getTargetTenantUrl(bridgeProperties, tenantId);
    }

    private String routePlatformService(HttpRequest request, BridgeProperties bridgeProperties, String targetServiceName) {
        return BridgeUtils.getTargetPlatformUrl(bridgeProperties, targetServiceName);
    }

    private void storeServiceInstance(String targetBaseUrl, String serviceName) {
        URL url = URLUtil.url(targetBaseUrl);
        BridgeServiceInstance serviceInstance = new BridgeServiceInstance(serviceName, url.getHost(), url.getPort());
        BridgeReplaceServiceInstanceHolder.set(serviceInstance);
    }

}