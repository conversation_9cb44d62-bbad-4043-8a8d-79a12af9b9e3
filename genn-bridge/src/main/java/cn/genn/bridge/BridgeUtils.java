package cn.genn.bridge;

import cn.genn.bridge.enums.InvokeTypeEnum;
import cn.genn.bridge.properties.BridgePlatformProperties;
import cn.genn.bridge.properties.BridgeProperties;
import cn.genn.bridge.properties.BridgeTenantProperties;
import cn.genn.security.constant.CodecType;
import cn.genn.security.constant.SignAlgorithm;
import cn.genn.security.sign.SignatureAlgorithm;
import cn.genn.security.sign.SignatureAlgorithmImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class BridgeUtils {

    public static SignatureAlgorithm SIGNATURE_ALGORITHM = new SignatureAlgorithmImpl(SignAlgorithm.SHA256withRSA, CodecType.HEX);
    public static final String AUTH_MAGIC = "GENN-BRIDGE";


    public static String getTargetPlatformUrl(BridgeProperties bridgeProperties, String targetServiceName) {
        BridgePlatformProperties platformProperties = bridgeProperties.getPlatform();
        if (platformProperties != null && platformProperties.getInvokeType() != InvokeTypeEnum.SERVICE_NAME) {
            return platformProperties.getGatewayUrl();
        }
        return null;
    }

    public static String getTargetTenantUrl(BridgeProperties bridgeProperties, Long tenantId) {
        List<BridgeTenantProperties> tenantProperties = bridgeProperties.getTenant();
        BridgeTenantProperties matchProperties = null;
        for (BridgeTenantProperties tenantProperty : tenantProperties) {
            Set<Long> tenantIds = tenantProperty.getTenantIds();
            if (tenantIds.contains(tenantId)) {
                matchProperties = tenantProperty;
                break;
            }
        }
        if (matchProperties == null) {
            return null;
        }
        if (matchProperties.getInvokeType() != InvokeTypeEnum.SERVICE_NAME) {
            return matchProperties.getGatewayUrl();
        }
        return null;
    }

    /**
     * 生成鉴权密钥
     */
    public static String generateAuthHeader(String privateKey,
                                            String httpMethod,
                                            String nonce) {
        String sign = SIGNATURE_ALGORITHM.makeSign(() -> {
            List<String> sourceList = new ArrayList<>();
            sourceList.add(AUTH_MAGIC);
            sourceList.add(httpMethod.toUpperCase());
            sourceList.add(nonce);
            return String.join("\n", sourceList);
        }, privateKey);
        return AUTH_MAGIC + " " + sign;
    }
}
