package cn.genn.web.feign.component;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;


/**
 * 处理feign调用产生的异常信息,包装为自定义异常
 *
 * <AUTHOR>
 */
@Slf4j
public class FeignClientErrorDecoder implements ErrorDecoder {


    @Override
    public Exception decode(String methodKey, Response response) {
        Exception exception;
        String responseBody = null;
        String errorCode = CommonCode.FEIGN_ERROR.buildCode();
        String errorMessage = CommonCode.FEIGN_ERROR.getDescription();
        try {
            responseBody = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
            log.debug("{}", responseBody);
            ResponseResult<?> responseResult = JsonUtils.parse(responseBody, ResponseResult.class, false);
            if (responseResult != null) {
                errorCode = responseResult.getCode();
                errorMessage = responseResult.getMsg();
            }
            exception = new BaseException(errorCode, errorMessage);
            log.error("invoke feign error, code={}, httpStatus={}, errorMessage={}", errorCode, response.status(), responseBody);
        } catch (Exception e) {
            log.error("responseBody={}, exp={}", responseBody, e.getMessage());
            exception = e;
        } finally {
            FeignStateContext.clear();
        }
        return exception;
    }
}
