package cn.genn.web.feign.component;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * feign请求头定制器
 * <p>
 * 子类继承该类,注入到spring中即可
 *
 * <AUTHOR>
 */
public abstract class FeignHeaderCustomizer implements FeignCustomizer {

    @Override
    public void customize(HttpServletRequest request, RequestTemplate restTemplate) {
        List<HeaderInfo> headerInfos = customizeHeaders(request, restTemplate);
        if (CollUtil.isEmpty(headerInfos)) {
            return;
        }
        headerInfos.forEach(headerInfo -> {
            String oldValue = request == null ? null : request.getHeader(headerInfo.getKey());
            if (CharSequenceUtil.isEmpty(headerInfo.getValue())) {
                modifyHeader(restTemplate, headerInfo.getKey(), oldValue);
                return;
            }
            if (headerInfo.isOverride()) {
                restTemplate.removeHeader(headerInfo.getKey());
                modifyHeader(restTemplate, headerInfo.getKey(), headerInfo.getValue());
            } else {
                if (headerInfo.isMulti()) {
                    modifyHeader(restTemplate, headerInfo.getKey(), headerInfo.getValue());
                }
            }
        });

    }

    /**
     * 自定义请求模板中的头部信息。
     * 并根据每个头部信息的特性（如是否覆盖、是否支持多值等）来修改`RequestTemplate`中的对应头部值。
     *
     * @param request      当前的HTTP请求，用于获取现有的头部信息。可能为空
     * @param restTemplate 要修改的请求模板，将根据提供的头部信息进行调整。
     */
    protected abstract List<HeaderInfo> customizeHeaders(HttpServletRequest request, RequestTemplate restTemplate);

    /**
     * 自定义请求模板中的头信息。此方法通过调用 {@code customizeHeaders} 获取头信息列表，
     * 然后根据每个 {@code HeaderInfo} 对象的属性来决定如何修改 {@code RequestTemplate} 的头信息。
     * 如果头信息列表为空，则此方法不执行任何操作。
     *
     * @param request      当前的 HttpServletRequest 对象，用于获取当前请求的头信息。
     * @param restTemplate 要修改的 RequestTemplate 对象，修改的结果将反映在发出的请求中。
     */
    private void modifyHeader(RequestTemplate restTemplate, String key, String value) {
        if (CharSequenceUtil.isEmpty(value)) {
            return;
        }
        restTemplate.header(key, value);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HeaderInfo {
        /**
         * 请求头key
         */
        private String key;
        /**
         * 请求头value,如果为空,则传递request中的值
         */
        private String value;
        /**
         * 是否覆盖,如果不覆盖,则template中的值优先
         */
        private boolean override = true;
        /**
         * 是否多值,如果不覆盖且允许多值,则会添加多个相同key的请求头
         */
        private boolean multi = true;
    }
}
