package cn.genn.web.feign;

/**
 * <AUTHOR>
 */
public class FeignConstants {

    /**
     * 微服务之间传递的请求头
     */
    public static final String FEIGN_REQUEST_HEADER = "x-genn-feign";

    /**
     * 用户登录态请求头
     */
    public static final String FEIGN_REQUEST_HEADER_TOKEN = "token";

    /**
     * 非微服务,feign请求头,代表目标地址是一个指定的url,而不是从注册中心获取
     */
    public static final String FEIGN_THIRD_REQUEST_HEADER = "x-genn-feign-third";

    /**
     * feign HTTP错误状态码
     */
    public static final int FEIGN_ERROR_STATUS = 500;
}
