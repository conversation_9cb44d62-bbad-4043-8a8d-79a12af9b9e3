package cn.genn.web.feign.component;

import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * magic-token请求头传递
 * 模拟登陆
 *
 * <AUTHOR>
 */
public class FeignHeaderMagicTokenCustomizer extends FeignHeaderCustomizer {

    public static final String MAGIC_TOKEN_HEADER = "magic-token";

    @Override
    protected List<HeaderInfo> customizeHeaders(HttpServletRequest request, RequestTemplate restTemplate) {
        List<HeaderInfo> headerInfos = new ArrayList<>();
        HeaderInfo headerInfo = new HeaderInfo();
        headerInfo.setKey(MAGIC_TOKEN_HEADER);
        headerInfos.add(headerInfo);
        return headerInfos;
    }
}
