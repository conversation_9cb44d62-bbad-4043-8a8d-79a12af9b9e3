package cn.genn.web.feign.component;

import feign.Response;
import feign.codec.Decoder;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Objects;

/**
 * feign解码器,清除上下文
 *
 * <AUTHOR>
 */
public class FeignClearContextDecoder implements Decoder {

    final Decoder delegate;

    public FeignClearContextDecoder(Decoder delegate) {
        Objects.requireNonNull(delegate, "Decoder must not be null. ");
        this.delegate = delegate;
    }

    @Override
    public Object decode(Response response, Type type) throws IOException {
        try {
            return delegate.decode(response, type);
        } finally {
            FeignStateContext.clear();
        }
    }
}
