package cn.genn.web.feign.component;

/**
 * 是否经过了feign请求
 * <AUTHOR>
 */
public class FeignStateContext {

    private static final ThreadLocal<Boolean> STATE = new ThreadLocal<>();

    public static void set() {
        STATE.set(true);
    }

    public static boolean get() {
        return STATE.get() != null && STATE.get();
    }

    public static void clear() {
        STATE.remove();
    }
}
