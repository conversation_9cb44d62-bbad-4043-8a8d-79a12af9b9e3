package cn.genn.web.feign.component;

import cn.genn.core.context.BaseRequestContext;
import cn.genn.web.WebConstants;
import cn.genn.web.spring.utils.ServletUtils;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import feign.RequestTemplate;
import feign.Target;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

import static cn.genn.web.feign.FeignConstants.*;

/**
 * feign请求拦截器,传递客户端 feign标识
 *
 * <AUTHOR>
 */
@Slf4j
public class FeignRequestInterceptor implements feign.RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        FeignStateContext.set();
        String feignRequestHeader = null;
        String feignRequestHeaderToken = null;
        String requestParamGtraceId = null;
        BaseRequestContext requestContext = BaseRequestContext.get();
        if (requestContext != null && MapUtil.isNotEmpty(requestContext.getRequestHeader())) {
            Map<String, String> cacheRequestHeader = requestContext.getRequestHeader();
            feignRequestHeader = cacheRequestHeader.get(FEIGN_REQUEST_HEADER);
            feignRequestHeaderToken = cacheRequestHeader.get(FEIGN_REQUEST_HEADER_TOKEN);
            requestParamGtraceId = requestContext.getGtraceId();
        }else {
            if (attributes == null) {
                feignRequestHeader = IdUtil.fastSimpleUUID();
            }else {
                HttpServletRequest request = attributes.getRequest();
                feignRequestHeader = request.getHeader(FEIGN_REQUEST_HEADER) == null ? IdUtil.fastSimpleUUID() : request.getHeader(FEIGN_REQUEST_HEADER);
                Map<String, String> headers = ServletUtils.getHeaderMap(request);
                if (headers.containsKey(FEIGN_REQUEST_HEADER_TOKEN)) {
                    feignRequestHeaderToken = headers.get(FEIGN_REQUEST_HEADER_TOKEN);
                }
                requestParamGtraceId = request.getParameter(WebConstants.REQUEST_PARAM_GTRACE_ID);
            }
        }
        // 微服务之间传递的唯一标识
        if (CharSequenceUtil.isBlank(feignRequestHeader)) {
            feignRequestHeader = IdUtil.fastSimpleUUID();
        }
        template.header(FEIGN_REQUEST_HEADER, feignRequestHeader);
        // token传递
        if (CharSequenceUtil.isNotBlank(feignRequestHeaderToken)) {
            template.header(FEIGN_REQUEST_HEADER_TOKEN, feignRequestHeaderToken);
        }
        //gtrace链路追踪
        if (CharSequenceUtil.isNotBlank(requestParamGtraceId)) {
            template.query(WebConstants.REQUEST_PARAM_GTRACE_ID, requestParamGtraceId);
        }
        Target<?> target = template.feignTarget();
        String host = URLUtil.url(target.url()).getHost();
        if (!host.equals(target.name())) {
            //如果url是非服务名类型的,需要传递一个标识
            template.header(FEIGN_THIRD_REQUEST_HEADER, IdUtil.fastSimpleUUID());
        }
        // 调用自定义的拦截器
        Map<String, FeignCustomizer> feignCustomizerMap = SpringUtil.getBeansOfType(FeignCustomizer.class);
        if (MapUtil.isEmpty(feignCustomizerMap)) {
            return;
        }
        feignCustomizerMap.forEach((k, v) -> {
            HttpServletRequest request = attributes == null ? null : attributes.getRequest();
            v.customize(request, template);
        });
    }
}
