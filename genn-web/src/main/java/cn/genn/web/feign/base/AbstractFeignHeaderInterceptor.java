package cn.genn.web.feign.base;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.utils.jackson.JsonUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * feign拦截器抽象类
 * 支持请求头的传递
 *
 * <AUTHOR>
 */
public abstract class AbstractFeignHeaderInterceptor implements RequestInterceptor {

    private final Map<String, Object> addHeaders = new HashMap<>();
    @Override
    public void apply(RequestTemplate requestTemplate) {
        addHeaders.putAll(addHeaders());
        addHeaders.forEach((k, v) -> {
            requestTemplate.header(k, encode(v));
        });
    }

    private String encode(Object value) {
        try {
            return URLEncoder.encode(JsonUtils.toJson(value), StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            throw new BaseException(CommonCode.UNSUPPORTED_ENCODING);
        }
    }

    protected abstract Map<String, Object> addHeaders();
}
