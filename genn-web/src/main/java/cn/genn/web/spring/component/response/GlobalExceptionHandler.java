package cn.genn.web.spring.component.response;

import cn.genn.core.exception.*;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.ExceptionUtils;
import cn.genn.core.utils.ResUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.web.feign.FeignConstants;
import cn.genn.web.spring.utils.ServletUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import feign.FeignException;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.unit.DataSize;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.RestClientException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.server.ResponseStatusException;
import org.yaml.snakeyaml.constructor.DuplicateKeyException;

import javax.net.ssl.SSLException;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.List;
import java.util.Optional;

import static cn.genn.core.exception.CommonCode.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Resource
    private HttpServletRequest request;
    private final ExceptionProperties exceptionProperties;
    private final List<ExceptionCustomizer> customizers;


    public GlobalExceptionHandler(List<ExceptionCustomizer> customizers, ExceptionProperties exceptionProperties) {
        this.customizers = customizers;
        this.exceptionProperties = exceptionProperties;
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException exception) {
        printRequest(exception);
        log.error("无效请求({}), message={}", request.getRequestURL(), exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(HTTP_METHOD_NOT_SUPPORTED), HTTP_METHOD_NOT_SUPPORTED.getDescription()), exception);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException exception) {
        printRequest(exception);
        log.error("参数错误({}), message={}", request.getRequestURL(), exception.getMessage(), exception);
        Optional<FieldError> optional = exception.getBindingResult().getFieldErrors().stream().findFirst();
        String code = convertCode(METHOD_ARGUMENT_NOT_VALID);
        return fail(optional
                .map(fieldError ->
                        ResUtils.error(code, fieldError.getDefaultMessage()))
                .orElseGet(() -> ResUtils.error(code, METHOD_ARGUMENT_NOT_VALID.getDescription())), exception);
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<?> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException exception) {
        printRequest(exception);
        log.error("文件上传错误({}), message={}", request.getRequestURL(), exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(MAX_UPLOAD_SIZE_EXCEEDED),
                MessageFormat.format("文件超过最大限制:{0}MB", String.valueOf(DataSize.ofBytes(exception.getMaxUploadSize()).toMegabytes()))), exception);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<?> handleConstraintViolationException(ConstraintViolationException exception) {
        printRequest(exception);
        log.error("参数错误({}), message={}", request.getRequestURL(), exception.getMessage(), exception);
        Optional<ConstraintViolation<?>> optional = exception.getConstraintViolations().stream().findFirst();
        return fail(optional
                .map(constraintViolation ->
                        ResUtils.error(METHOD_ARGUMENT_NOT_VALID.buildCode(), constraintViolation.getMessage()))
                .orElseGet(() -> ResUtils.error(METHOD_ARGUMENT_NOT_VALID)), exception);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<?> handleMissingServletRequestParameterException(MissingServletRequestParameterException exception) {
        printRequest(exception);
        log.error("参数错误({}), param={}，message={}", request.getRequestURL(), exception.getParameterName(),
                exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(METHOD_ARGUMENT_NOT_VALID), String.format("参数错误(%s)", exception.getParameterName())), exception);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException exception) {
        printRequest(exception);
        log.error("参数错误({}), param={}，message={}", request.getRequestURL(), exception.getName(),
                exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(METHOD_ARGUMENT_NOT_VALID), String.format("参数错误(%s)", exception.getName())), exception);
    }

    @ExceptionHandler(BindException.class)
    public ResponseEntity<?> handleBindException(BindException exception) {
        printRequest(exception);
        log.error("参数错误({})，message={}", request.getRequestURL(), exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(METHOD_ARGUMENT_NOT_VALID), METHOD_ARGUMENT_NOT_VALID.getDescription()), exception);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException exception) {
        printRequest(exception);
        log.error("参数错误({}), message={}", request.getRequestURL(), exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(METHOD_ARGUMENT_NOT_VALID), METHOD_ARGUMENT_NOT_VALID.getDescription()), exception);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<?> handleIllegalArgumentException(IllegalArgumentException exception) {
        printRequest(exception);
        log.error("参数错误({}), message={}", request.getRequestURL(), exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(METHOD_ARGUMENT_NOT_VALID), METHOD_ARGUMENT_NOT_VALID.getDescription()), exception);
    }

    @ExceptionHandler({SocketException.class, RestClientException.class, SSLException.class, SocketTimeoutException.class, ConnectException.class, FeignException.class})
    public ResponseEntity<?> handleConnectException(Exception exception) {
        printRequest(exception);
        log.error("远程连接错误({}), message={}", request.getRequestURL(), exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(CONNECT_ERROR), CONNECT_ERROR.getDescription()), exception);
    }

    @ExceptionHandler(ResponseStatusException.class)
    public ResponseEntity<?> handleResponseStatusException(ResponseStatusException exception) {
        printRequest(exception);
        log.error("响应状态错误({}), message={}", request.getRequestURL(), exception.getMessage(), exception);
        int statusCode = exception.getStatusCode().value();
        if (statusCode == 503 || statusCode == 504) {
            return fail(ResUtils.error(convertCode(CONNECT_ERROR), CONNECT_ERROR.getDescription()), exception);
        }
        String code = exceptionProperties.getProductCode()
                + exceptionProperties.getServiceCode()
                + MessageCodeWrap.DEFAULT_BIZ_CODE
                + statusCode;
        return fail(ResUtils.error(code, "响应状态错误"), exception);
    }

    @ExceptionHandler(CheckException.class)
    public ResponseEntity<?> handleCheckException(CheckException exception) {
        printRequest(exception);
        log.info("校验失败({})，code={}, message={}", request.getRequestURL(), exception.getCode(),
                exception.getMessage());
        return fail(ResUtils.error(convertCustomCode(exception.getCode()), convertCustomMsg(exception.getMessage())), exception);
    }


    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<?> handleBusinessException(BusinessException exception) {
        printRequest(exception);
        log.error("业务错误({})，code={}, message={}", request.getRequestURL(), exception.getCode(),
                exception.getMessage(), exception);
        return fail(ResUtils.error(convertCustomCode(exception.getCode()), convertCustomMsg(exception.getMessage())), exception);
    }

    @ExceptionHandler(BaseException.class)
    public ResponseEntity<?> handleBaseException(BaseException exception) {
        printRequest(exception);
        log.error("业务错误({})，code={}, message={}", request.getRequestURL(), exception.getCode(),
                exception.getMessage(), exception);
        return fail(ResUtils.error(convertCustomCode(exception.getCode()), convertCustomMsg(exception.getMessage())), exception);
    }


    @ExceptionHandler({DuplicateKeyException.class})
    public ResponseEntity<?> handleDuplicateKeyException(DuplicateKeyException exception) {
        printRequest(exception);
        log.error("主键冲突, {}", exception.getMessage(), exception);
        return fail(ResUtils.error(convertCode(DUPLICATE_KEY), DUPLICATE_KEY.getDescription()), exception);
    }

    @ExceptionHandler(SQLException.class)
    public ResponseEntity<?> handleSQLException(SQLException exception) {
        printRequest(exception);
        log.error("数据库异常", exception);
        return fail(ResUtils.error(convertCode(FAIL), exceptionProperties.getDefaultErrorMessage()), exception);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<?> handle(Exception exception) {
        printRequest(exception);
        log.error("异常发生({})", request.getRequestURL(), exception);
        return fail(ResUtils.error(convertCode(FAIL), exceptionProperties.getDefaultErrorMessage()), exception);
    }

    private ResponseEntity<?> fail(ResponseResult<?> responseResult, Exception e) {
        int status = StrUtil.isNotEmpty(request.getHeader(FeignConstants.FEIGN_REQUEST_HEADER)) ?
                FeignConstants.FEIGN_ERROR_STATUS : HttpStatus.OK.value();
        if (CollUtil.isNotEmpty(customizers)) {
            customizers.forEach(customizer -> {
                try {
                    customizer.customize(request, responseResult, e);
                }catch (Exception customizerException) {
                    log.error("customizer: {} error", customizer.getClass().getName(), customizerException);
                }
            });
        }
        return ResponseEntity.status(status).body(responseResult);
    }

    private String convertCode(CommonCode commonCode) {
        String productCode = exceptionProperties.getProductCode();
        String serviceCode = exceptionProperties.getServiceCode();
        return productCode + serviceCode + commonCode.getBizCode() + commonCode.getCode();
    }

    private String convertCustomCode(String code) {
        String prefix = exceptionProperties.getProductCode() + exceptionProperties.getServiceCode();
        if (code.length() != 9 && code.length() != 3 && code.length() != 5) {
            return convertCode(FAIL);
        }
        if (code.length() == 3) {
            return prefix + DEFAULT_BIZ_CODE + code;
        }
        if (code.length() == 5) {
            return prefix + code;
        }
        if (code.startsWith("000000")) {
            return prefix + DEFAULT_BIZ_CODE + code.substring(6);
        }
        return code;
    }

    private String convertCustomMsg(String message) {
        if (FAIL.getDescription().equals(message)) {
            return exceptionProperties.getDefaultErrorMessage();
        }
        return message;
    }

    /**
     * 打印请求信息
     *
     * @param request
     */
    protected void printRequest(Throwable t) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest wrappedRequest;
        wrappedRequest = attributes.getRequest();
        log.error("请求出错\n代码位置：{}\n请求客户端IP：{}\n请求地址：{}\n请求方法：{}\n请求头：{}\n请求参数：{}\n请求体：{}\n",
                ExceptionUtils.getErrorPosition(t),
                (JakartaServletUtil.getClientIP(request)),
                request.getRequestURI(),
                request.getMethod(),
                JsonUtils.toJson(JakartaServletUtil.getHeaderMap(request)),
                JsonUtils.toJson(request.getParameterMap()),
                ServletUtils.isJsonRequest(wrappedRequest) ? ServletUtils.getCachedRequestBody(wrappedRequest) : ""
        );
    }

}
