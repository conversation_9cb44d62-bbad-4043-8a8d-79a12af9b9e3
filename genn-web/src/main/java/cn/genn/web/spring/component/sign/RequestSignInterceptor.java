package cn.genn.web.spring.component.sign;

import cn.genn.core.exception.CheckException;
import cn.genn.core.exception.CommonCode;
import cn.genn.security.sign.SignatureAlgorithm;
import cn.genn.security.util.SignUtils;
import cn.genn.web.spring.annotation.RequestSign;
import cn.genn.web.spring.component.base.AbstractAnnotationInterceptor;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import org.springframework.web.method.HandlerMethod;

import java.util.ArrayList;
import java.util.List;

/**
 * 请求签名过滤器
 *
 * <AUTHOR>
 */
public class RequestSignInterceptor extends AbstractAnnotationInterceptor<RequestSign> {

    @Setter
    private RequestSignProperties signProperties;

    public RequestSignInterceptor(RequestSignProperties signProperties) {
        super(RequestSign.class);
        this.signProperties = signProperties;
    }

    @Override
    protected boolean isSupport(RequestSign anno) {
        return anno != null || signProperties.isGlobal();
    }

    @Override
    protected boolean doPreHandle(HttpServletRequest request, HttpServletResponse response, HandlerMethod handler, RequestSign requestSign) throws Exception {
        RequestSignProperties properties = requestSign == null ? signProperties : createProperties(requestSign);
        if (!properties.isEnabled()) {
            return true;
        }
        //无需签名
        if (noSign(properties, request, response)) {
            return true;
        }
        //签名校验
        String source;
        SignatureAlgorithm signatureAlgorithm = SpringUtil.getBean(properties.getSignatureAlgorithm());
        if (signatureAlgorithm == null) {
            throw new IllegalArgumentException("signatureAlgorithm not found");
        }
        SignSecretDefinition signSecretDefinition;
        if (StrUtil.isEmpty(properties.getSignSecretDefinition())) {
            signSecretDefinition = SpringUtil.getBean(SignSecretDefinition.class);
        } else {
            signSecretDefinition = SpringUtil.getBean(properties.getSignSecretDefinition());
        }
        if (signSecretDefinition == null) {
            throw new IllegalArgumentException("signSecretDefinition not found");
        }
        String originSign = getSign(request, properties);
        if (StrUtil.isEmpty(originSign)) {
            return false;
        }
        originSign = originSign.substring(properties.getPrefix().length());
        if (properties.isCheckTimestamp()) {
            //时间戳校验
            if (!checkTimestamp(properties, request)) {
                return false;
            }
        }
        if (StrUtil.isNotEmpty(properties.getSignSourceDefinition())) {
            SignSourceDefinition signSourceDefinition = SpringUtil.getBean(properties.getSignSourceDefinition());
            if (signSourceDefinition != null) {
                //如果设置了签名来源定义,直接使用该签名源字符串
                source = signSourceDefinition.genSignSource(request, response);
            } else {
                throw new IllegalArgumentException("signSourceDefinition not found");
            }
        } else if (SpringUtil.getBean(SignSourceDefinition.class) != null) {
            source = SpringUtil.getBean(SignSourceDefinition.class).genSignSource(request, response);
        } else {
            List<String> sourceList = new ArrayList<>();
            if (properties.isEnableHttpMethod()) {
                sourceList.add(request.getMethod().toUpperCase());
            }
            if (properties.isEnableHttpPath()) {
                sourceList.add(URLEncodeUtil.encode(request.getRequestURI()));
            }
            if (properties.isEnableTimestamp()) {
                Long timestamp = getTimestamp(request, properties);
                if (timestamp == null) {
                    return false;
                }
                sourceList.add(timestamp.toString());
            }
            if (properties.isEnableNonce()) {
                String nonce = getNonce(request, properties);
                if (StrUtil.isEmpty(nonce)) {
                    return false;
                }
                sourceList.add(nonce);
            }
            if (properties.isSignRequestParam()) {
                //签名请求参数
                sourceList.add(SignUtils.linkParams(JakartaServletUtil.getParamMap(request), new String[]{properties.getSignKey()}));
            }
            source = StrUtil.join("\n", sourceList);
        }
        return signatureAlgorithm.validate(() -> source, originSign, signSecretDefinition.getSecret(request, response));
    }

    @Override
    protected void doOnFalse(HttpServletRequest request, HttpServletResponse response, HandlerMethod handler, RequestSign anno) {
        //返回错误信息
        throw new CheckException(CommonCode.SIGN_ERROR);
    }

    private String getSign(HttpServletRequest request, RequestSignProperties properties) {
        String sign = request.getHeader(properties.getSignKey());
        if (StrUtil.isEmpty(sign)) {
            sign = request.getParameter(properties.getSignKey());
        }
        return sign;
    }

    private boolean checkTimestamp(RequestSignProperties properties, HttpServletRequest request) {
        Long timestamp = getTimestamp(request, properties);
        if (timestamp == null) {
            return false;
        }
        long now = System.currentTimeMillis();
        return now - timestamp <= properties.getTimestampExpire() * 1000L;
    }

    private Long getTimestamp(HttpServletRequest request, RequestSignProperties properties) {
        String timestamp = request.getParameter(properties.getTimestampKey());
        if (StrUtil.isEmpty(timestamp)) {
            timestamp = request.getHeader(properties.getTimestampKey());
        }
        if (StrUtil.isEmpty(timestamp)) {
            return null;
        }
        return Long.parseLong(timestamp);
    }

    private String getNonce(HttpServletRequest request, RequestSignProperties properties) {
        String nonce = request.getParameter(properties.getNonceKey());
        if (StrUtil.isEmpty(nonce)) {
            nonce = request.getHeader(properties.getNonceKey());
        }
        return nonce;
    }

    private boolean noSign(RequestSignProperties properties, HttpServletRequest request, HttpServletResponse response) {
        String noSignDefinition = properties.getNoSignDefinition();
        if (StrUtil.isEmpty(noSignDefinition)) {
            //尝试从容器中获取
            NoSignDefinition noSign = SpringUtil.getBean(NoSignDefinition.class);
            if (noSign == null) {
                return false;
            }
        }
        NoSignDefinition noSign = SpringUtil.getBean(noSignDefinition);
        return noSign != null && !noSign.needSign(request, response);
    }

    private RequestSignProperties createProperties(RequestSign requestSign) {
        RequestSignProperties properties = new RequestSignProperties();
        properties.setEnabled(requestSign.enabled());
        properties.setCodecType(signProperties.getCodecType());
        properties.setAlgorithm(signProperties.getAlgorithm());
        properties.setPrefix(requestSign.prefix());
        properties.setEnableHttpMethod(requestSign.enableHttpMethod());
        properties.setEnableHttpPath(requestSign.enableHttpPath());
        properties.setEnableTimestamp(requestSign.enableTimestamp());
        properties.setCheckTimestamp(requestSign.checkTimestamp());
        properties.setTimestampKey(requestSign.timestampKey());
        properties.setTimestampExpire(requestSign.timestampExpire());
        properties.setEnableNonce(requestSign.enableNonce());
        properties.setNonceKey(requestSign.nonceKey());
        properties.setSignKey(requestSign.signKey());
        properties.setSignSourceDefinition(requestSign.signSourceDefinition());
        properties.setSignSecretDefinition(requestSign.signSecretDefinition());
        properties.setSignatureAlgorithm(requestSign.signatureAlgorithm());
        properties.setNoSignDefinition(requestSign.noSignDefinition());
        properties.setSignRequestParam(requestSign.signRequestParam());
        return properties;
    }
}
