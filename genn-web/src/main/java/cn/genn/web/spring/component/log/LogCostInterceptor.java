package cn.genn.web.spring.component.log;

import cn.genn.core.exception.CommonCode;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.LogUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.web.WebConstants;
import cn.genn.web.spring.annotation.LogCost;
import cn.genn.web.spring.component.base.AbstractAnnotationInterceptor;
import cn.genn.web.spring.utils.ServletUtils;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class LogCostInterceptor extends AbstractAnnotationInterceptor<LogCost> {

    public static final String RESPONSE_BODY = "RESPONSE_BODY";
    public static final String RESPONSE_ENCRYPT_BODY = "RESPONSE_ENCRYPT_BODY";

    private ThreadLocal<Long> timeThreadLocal = new ThreadLocal<>();

    private static ConcurrentHashMap<String, MethodStats> methodStats = new ConcurrentHashMap<>();

    @Setter
    private LogCostAdditionalInfo logCostAdditionalInfo;

    @Setter
    private LogCostProperties logCostProperties;

    public LogCostInterceptor() {
        this(null);
    }

    public LogCostInterceptor(LogCostAdditionalInfo logCostAdditionalInfo) {
        this(null, new LogCostProperties());
    }

    public LogCostInterceptor(LogCostAdditionalInfo logCostAdditionalInfo, LogCostProperties logCostProperties) {
        super(LogCost.class);
        this.logCostAdditionalInfo = logCostAdditionalInfo;
        this.logCostProperties = logCostProperties;
    }

    @Override
    protected boolean isSupport(LogCost anno) {
        return null != anno || logCostProperties.isGlobal();
    }

    @Override
    protected boolean doPreHandle(HttpServletRequest request, HttpServletResponse response, HandlerMethod handler, LogCost logCost) throws Exception {
        timeThreadLocal.set(System.currentTimeMillis());
        return true;
    }

    @Override
    protected void doAfterCompletion(HttpServletRequest request, HttpServletResponse response, HandlerMethod handler, LogCost logCost, Exception exception) {
        Long startTime = timeThreadLocal.get();
        if (startTime == null) {
            startTime = 0L;
        }
        timeThreadLocal.remove();
        long elapsedTime = System.currentTimeMillis() - startTime;
        String methodName = getMethodName(handler, logCost);
        Logger logger = getLogger(handler, logCost);
        MethodStats stats = null;
        if (logCostProperties.isStatEnabled()) {
            stats = stats(request.getRequestURI(), methodName, elapsedTime);
        }
        int maxLength;
        long statLogFrequency;
        boolean logEmptyResult;
        boolean logIp;
        boolean logUri;
        boolean logParams;
        boolean logRequestBody;
        boolean logResult;
        String module;
        int requestBodyMaxLength;
        long warningThreshold;
        int maxLengthPerLine;
        String[] ignoreParams = getIgnoreParams(logCost);
        if (logCost == null) {
            maxLength = logCostProperties.getResultMaxLength();
            statLogFrequency = logCostProperties.getStatLogFrequency();
            logEmptyResult = logCostProperties.isLogEmptyResult();
            logIp = logCostProperties.isIp();
            logUri = logCostProperties.isUri();
            logParams = logCostProperties.isParams();
            logRequestBody = logCostProperties.isLogRequestBody();
            logResult = logCostProperties.isResult();
            module = logCostProperties.getModule();
            requestBodyMaxLength = logCostProperties.getRequestBodyMaxLength();
            warningThreshold = logCostProperties.getWarningThreshold();
            maxLengthPerLine = logCostProperties.getMaxLengthPerLine();
        } else {
            maxLength = logCost.resultMaxLength();
            statLogFrequency = logCost.statLogFrequency();
            logEmptyResult = logCost.logEmptyResult();
            logIp = logCost.ip();
            logUri = logCost.uri();
            logParams = logCost.params();
            logRequestBody = logCost.logRequestBody();
            logResult = logCost.result();
            module = logCost.module();
            requestBodyMaxLength = logCost.requestBodyMaxLength();
            warningThreshold = logCost.warningThreshold();
            maxLengthPerLine = logCost.maxLengthPerLine();
        }
        try {
            Object ret = request.getAttribute(RESPONSE_BODY);
            String result = buildResult(ret, maxLength);
            if (!logEmptyResult && isResultEmpty(ret, result)) {
                return;
            }
            String encryptResult = null;
            Object encryptBody = request.getAttribute(RESPONSE_ENCRYPT_BODY);
            if (encryptBody != null) {
                encryptResult = buildResult(encryptBody, maxLength);
            }
            writeLog(request, response, logger,
                    module, logIp, logUri,
                    logParams, ignoreParams,
                    logRequestBody, requestBodyMaxLength,
                    logResult, warningThreshold, maxLengthPerLine,
                    elapsedTime, methodName, result, encryptResult);
        } finally {
            logStat(logger, statLogFrequency, stats);
        }
    }

    private String[] getIgnoreParams(LogCost logCost) {
        List<String> ignoreParams = new ArrayList<>();
        Collections.addAll(ignoreParams, logCostProperties.getIgnoreParams());
        if (logCost != null) {
            Collections.addAll(ignoreParams, logCost.ignoreParams());
        }
        return ignoreParams.toArray(new String[0]);
    }

    private void writeLog(HttpServletRequest request, HttpServletResponse response, Logger logger,
                          String module, boolean logIp, boolean logUri,
                          boolean logParams, String[] ignoreParams,
                          boolean logRequestBody, int requestBodyMaxLength,
                          boolean logResult, long warningThreshold, int maxLengthPerLine,
                          long elapsedTime, String methodName,
                          String result, String encryptResult) {
        StringBuilder sb = new StringBuilder();
        sb.append("[logCost][").append(methodName).append("][").append(elapsedTime).append("][");
        String gtrace = request.getParameter(WebConstants.REQUEST_PARAM_GTRACE_ID);
        if (StringUtils.hasText(gtrace)) {
            sb.append(gtrace).append("][");
        }
        if (logIp) {
            sb.append(JakartaServletUtil.getClientIP(request)).append("][");
        }
        if (StringUtils.hasText(module)) {
            sb.append(module).append("][");
        }
        if (logUri) {
            sb.append(request.getRequestURI()).append("]");
        }
        if (null != logCostAdditionalInfo) {
            sb.append("[").append(logCostAdditionalInfo.logInfo(request, response)).append("]");
        }
        if (logParams) {
            Map<String, String> requestParams = JakartaServletUtil.getParamMap(request);
            sb.append(", args=").append(JsonUtils.toJsonLog(MapUtil.removeAny(requestParams, ignoreParams)));
        }
        if (logRequestBody) {
            String requestBody = getRequestBody(request);
            if (StringUtils.hasText(requestBody)) {
                requestBody = requestBody.replace("\n", "");
                sb.append(", requestBody=").append(requestBodyMaxLength > 0 ? CharSequenceUtil.subSufByLength(requestBody.replace("\n", ""), requestBodyMaxLength): requestBody);
            }
        }
        if (logResult) {
            sb.append(", result=").append(result);
            if (encryptResult != null) {
                sb.append(", encryptResult=").append(encryptResult);
            }
        }
        if (elapsedTime > warningThreshold) {
            LogUtils.warn(logger, sb.toString(), maxLengthPerLine);
        } else {
            LogUtils.info(logger, sb.toString(), maxLengthPerLine);
        }
    }

    private boolean isResultEmpty(Object ret, String result) {
        if (CharSequenceUtil.isEmpty(result)) {
            return true;
        }
        if (ret instanceof ResponseResult) {
            return isResultEmpty((ResponseResult) ret);
        }
        return false;
    }

    private String buildResult(Object ret, int maxLength) {
        String result = null;
        if (ret != null) {
            if (ret instanceof String) {
                result = (String) ret;
            } else {
                result = JsonUtils.toJsonLog(ret);
            }
            if (maxLength > 0) {
                result = CharSequenceUtil.subSufByLength(result, maxLength);
            }
        }
        return result;
    }

    private static Logger getLogger(HandlerMethod handler, LogCost logCost) {
        Logger logger;
        if (logCost != null && StringUtils.hasText(logCost.logger())) {
            logger = LoggerFactory.getLogger(logCost.logger());
        } else {
            logger = LoggerFactory.getLogger(handler.getBeanType().getName());
        }
        return logger;
    }

    private static String getMethodName(HandlerMethod handler, LogCost logCost) {
        String methodName;
        if (logCost != null && StringUtils.hasText(logCost.value())) {
            methodName = logCost.value();
        } else {
            methodName = handler.getMethod().getName();
        }
        return methodName;
    }

    private String getRequestBody(HttpServletRequest request) {
        return ServletUtils.getCachedRequestBody(request);
    }

    private MethodStats stats(String url, String methodName, long elapsedTime) {
        MethodStats stats = methodStats.computeIfAbsent(url, MethodStats::new);

        stats.methodName = methodName;
        stats.count++;
        stats.totalTime += elapsedTime;
        stats.lastTime = elapsedTime;

        if (elapsedTime > stats.maxTime) {
            stats.maxTime = elapsedTime;
        }
        return stats;
    }

    private boolean isResultEmpty(ResponseResult<?> responseResult) {
        if (!CommonCode.SUCCESS.buildCode().equals(responseResult.getCode())) {
            return false;
        }
        Object result = responseResult.getData();
        if (result == null) {
            return true;
        }
        if (result instanceof String) {
            if (((String) result).isEmpty()) {
                return true;
            }
        }
        if (result instanceof Map) {
            if (((Map) result).isEmpty()) {
                return true;
            }
        }
        if (result instanceof Collection) {
            if (((Collection) result).isEmpty()) {
                return true;
            }
        }
        return false;
    }

    private void logStat(Logger logger, long statLogFrequency, MethodStats stats) {
        if (stats == null) {
            return;
        }
        if (statLogFrequency > 0 && stats.count % statLogFrequency == 0) {
            long avgTime = stats.totalTime / stats.count;
            long runningAvg = (stats.totalTime - stats.lastTotalTime) / statLogFrequency;
            logger.info("[logCost][" + stats.methodName + "][" + stats.uri + "], cnt = " + stats.count + ", lastTime = " + stats.lastTime + ", avgTime = " + avgTime + ", runningAvg = " + runningAvg + ", maxTime = " + stats.maxTime);

            //reset the last total time
            stats.lastTotalTime = stats.totalTime;
        }
    }

    static class MethodStats {
        String methodName;
        String uri;
        long count;
        long totalTime;
        long lastTotalTime;
        long maxTime;
        long lastTime;

        MethodStats(String uri) {
            this.uri = uri;
        }
    }
}
