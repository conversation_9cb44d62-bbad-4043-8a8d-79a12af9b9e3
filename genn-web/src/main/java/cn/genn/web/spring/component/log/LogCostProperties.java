package cn.genn.web.spring.component.log;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LogCostProperties {

    /**
     * 是否启用访问日志
     */
    private boolean enabled = true;

    /**
     * 全局启用访问日志
     */
    private boolean global = false;

    /**
     * 拦截地址
     */
    private String[] pathPatterns = new String[]{"/**"};

    /**
     * 拦截排除地址
     */
    private String[] excludePathPatterns = new String[]{"/doc.html", "/swagger-resources/**", "/v?/api-docs"};

    /**
     * 设置日志模块
     */
    private String module = "";

    /**
     * 是否打印IP
     */
    private boolean ip = true;

    /**
     * 是否打印URI
     */
    private boolean uri = true;

    /**
     * 是否打印参数
     * <p>
     * 如果需要打印requestBody内容，需要加载过滤器 {@link cn.genn.web.spring.component.request.BufferedHttpServletRequestFilter}
     */
    private boolean params = true;

    /**
     * 忽略的参数
     */
    private String[] ignoreParams = {};

    /**
     * 是否打印RequestBody
     */
    private boolean logRequestBody = true;

    /**
     * 是否打印结果
     */
    private boolean result = true;

    /**
     * 是否打印空返回
     */
    private boolean logEmptyResult = true;

    /**
     * 结果最大输出长度，0全部输出
     */
    private int resultMaxLength = 0;

    /**
     * requestBody最大输出长度，0全部输出
     */
    private int requestBodyMaxLength = 0;

    /**
     * 每行日志最大长度，超长会换行，0一行输出
     */
    private int maxLengthPerLine = 0;

    /**
     * 大于1000ms警告
     */
    private long warningThreshold = 1000;

    /**
     * 是否启用统计
     */
    private boolean statEnabled = true;

    /**
     * 每100条记录打印一条统计日志， 0不统计
     */
    private long statLogFrequency = 100;
}
