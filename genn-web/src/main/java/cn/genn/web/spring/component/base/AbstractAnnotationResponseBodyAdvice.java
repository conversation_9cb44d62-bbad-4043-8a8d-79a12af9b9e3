package cn.genn.web.spring.component.base;

import cn.genn.core.utils.AnnotationUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.MethodParameter;
import org.springframework.core.ResolvableType;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.annotation.Annotation;

/**
 * <AUTHOR>
 */
public abstract class AbstractAnnotationResponseBodyAdvice<A extends Annotation> implements ResponseBodyAdvice<Object> {

    private Class<A> annotationType;

    public AbstractAnnotationResponseBodyAdvice() {
        this.annotationType = (Class<A>) ResolvableType.forClass(this.getClass()).getSuperType().resolveGeneric(0);
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return AnnotationUtils.hasMethodAnnotation(returnType, this.annotationType);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        A annotation = AnnotationUtils.getMethodAnnotation(returnType, this.annotationType);
        HttpHeaders httpHeaders = request.getHeaders();
        HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();
        HttpServletResponse servletResponse = ((ServletServerHttpResponse) response).getServletResponse();
        return doBeforeBodyWrite(body, annotation, servletRequest, servletResponse, httpHeaders);
    }

    protected abstract Object doBeforeBodyWrite(Object body, A annotation, HttpServletRequest request, HttpServletResponse response, HttpHeaders httpHeaders);

}
