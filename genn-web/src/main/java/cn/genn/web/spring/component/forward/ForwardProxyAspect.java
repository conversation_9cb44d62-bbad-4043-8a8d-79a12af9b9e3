package cn.genn.web.spring.component.forward;

import cn.genn.web.spring.annotation.ForwardProxy;
import cn.genn.web.spring.utils.ServletUtils;
import cn.hutool.core.collection.CollectionUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.List;
import java.util.Map;

/**
 * 转发请求aop
 * <AUTHOR>
 */
@Aspect
@Slf4j
public class ForwardProxyAspect implements ApplicationContextAware {

    @Resource
    private ServiceProxy serviceProxy;
    private ApplicationContext applicationContext;


    @Around(value = "@annotation(forwardProxy)")
    public Object forwardProxyMethod(ProceedingJoinPoint joinPoint, ForwardProxy forwardProxy) throws Throwable {
        String targetService = forwardProxy.targetService();
        String targetUrl = forwardProxy.targetUrl();
        HttpServletRequest request = ServletUtils.getRequest();
        HttpServletResponse response = ServletUtils.getResponse();
        HttpServletRequest newRequest = request;
        Class<? extends ForwardDataGenerator> extendHeaderGenerator = forwardProxy.extendHeaders();
        Class<? extends ForwardDataGenerator> extendParamGenerator = forwardProxy.extendParams();
        if (extendHeaderGenerator != DefaultForwardGenerator.class || extendParamGenerator != DefaultForwardGenerator.class) {
            //获取当前请求参数和请求头
            Map<String, String> requestHeaderMap = ServletUtils.getHeaderMap(request);
            Map<String, Object> requestParamMap = ServletUtils.getParamMap(request);
            if (ServletUtils.isJsonRequest(request)) {
                requestParamMap.putAll(ServletUtils.getJsonBodyMap(request));
            }
            if (extendHeaderGenerator != DefaultForwardGenerator.class) {
                ForwardDataGenerator headerGenerator = applicationContext.getBean(extendHeaderGenerator);
                List<ForwardData> extendHeaders = headerGenerator.generate(requestParamMap, requestHeaderMap);
                if (CollectionUtil.isNotEmpty(extendHeaders)) {
                    newRequest = new ForwardHeaderHttpServletRequestWrapper(newRequest, extendHeaders);
                }
            }
            if (extendParamGenerator != DefaultForwardGenerator.class) {
                ForwardDataGenerator paramGenerator = applicationContext.getBean(extendParamGenerator);
                List<ForwardData> extendParams = paramGenerator.generate(requestParamMap, requestHeaderMap);
                if (CollectionUtil.isNotEmpty(extendParams)) {
                    newRequest = new ForwardParamHttpServletRequestWrapper(newRequest, extendParams);
                }
            }
        }
        newRequest = new ForwardUrlHttpServletRequestWrapper(newRequest, targetService, targetUrl);
        serviceProxy.forward(targetService, newRequest, response);
        return joinPoint.proceed();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}

