package cn.genn.web.spring.component.base;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.web.spring.utils.SpringBootPropertyUtils;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.AnnotationMetadata;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class AbstractImportBeanDefinitionRegistrar implements ImportBeanDefinitionRegistrar, EnvironmentAware, ResourceLoaderAware {

    protected Environment environment;

    protected ResourceLoader resourceLoader;

    protected BeanDefinitionRegistry registry;

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        this.registry = registry;
        try {
            onRegisterBeanDefinitions(importingClassMetadata);
        } catch (Exception e) {
            throw new BaseException(CommonCode.FAIL);
        }
    }

    protected abstract void onRegisterBeanDefinitions(AnnotationMetadata importingClassMetadata) throws Exception;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    /**
     * 注册bean
     */
    protected void registerBean(String name, Class<?> clazz, Object... args) {
        registerBean(name, clazz, null, null, null, args);
    }

    /**
     * 注册bean
     */
    protected void registerBean(String name, Class<?> clazz, Map<String, Object> propertyValueMap, Object... args) {
        registerBean(name, clazz, propertyValueMap, null, null, args);
    }

    /**
     * 注册bean
     */
    protected void registerBean(String name, Class<?> clazz, Map<String, Object> propertyValueMap, Map<String, String> propertyReferenceMap, Object... args) {
        registerBean(name, clazz, propertyValueMap, propertyReferenceMap, null, args);
    }

    /**
     * 注册bean
     */
    protected void registerBean(String name, Class<?> clazz, Map<String, Object> propertyValueMap, Map<String, String> propertyReferenceMap, List<String> dependsOnList, Object... args) {
        BeanDefinitionBuilder beanDefinitionBuilder = BeanDefinitionBuilder.genericBeanDefinition(clazz);
        for (Object arg : args) {
            beanDefinitionBuilder.addConstructorArgValue(arg);
        }
        if (propertyValueMap != null) {
            propertyValueMap.forEach(beanDefinitionBuilder::addPropertyValue);
        }
        if (propertyReferenceMap != null) {
            propertyReferenceMap.forEach(beanDefinitionBuilder::addPropertyReference);
        }
        if(dependsOnList != null) {
            dependsOnList.forEach(beanDefinitionBuilder::addDependsOn);
        }
        BeanDefinition beanDefinition = beanDefinitionBuilder.getRawBeanDefinition();
        registry.registerBeanDefinition(name, beanDefinition);
    }

    protected <T> T getProperties(String prefix, Class<T> clazz) {
        return SpringBootPropertyUtils.handle(environment, prefix, clazz);
    }
}
