package cn.genn.web.spring.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.bind.BindResult;
import org.springframework.core.env.Environment;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SpringBootPropertyUtils {


    @SuppressWarnings("unchecked")
    public static <T> T handle(final Environment environment, final String prefix, final Class<T> targetClass) {
        T result = (T) innerHandler(environment, prefix, targetClass);
        if (result == null && !targetClass.isInterface()) {
            try {
                result = targetClass.newInstance();
            } catch (Exception e) {
                log.error("handle properties failed", e);
            }
        }
        return result;
    }

    @SneakyThrows
    private static Object innerHandler(final Environment environment, final String prefix, final Class<?> targetClass) {
        Class<?> binderClass = Class.forName("org.springframework.boot.context.properties.bind.Binder");
        Method getMethod = binderClass.getDeclaredMethod("get", Environment.class);
        Method bindMethod = binderClass.getDeclaredMethod("bind", String.class, Class.class);
        Object binderObject = getMethod.invoke(null, environment);
        String prefixParam = prefix.endsWith(".") ? prefix.substring(0, prefix.length() - 1) : prefix;
        Object bindResultObject = bindMethod.invoke(binderObject, prefixParam, targetClass);
        if (bindResultObject == null || !((BindResult) bindResultObject).isBound()) {
            return null;
        }
        Method resultGetMethod = bindResultObject.getClass().getDeclaredMethod("get");
        if (resultGetMethod == null) {
            return null;
        }
        return resultGetMethod.invoke(bindResultObject);
    }
}
