package cn.genn.web.spring.component.base;

import cn.genn.web.spring.utils.ServletUtils;
import cn.hutool.core.collection.CollectionUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 */
public abstract class AbstractPatternRequestFilter extends OncePerRequestFilter {

    /**
     * 支持媒体类型
     */
    @Setter
    private MediaType[] supportedMediaTypes = new MediaType[]{MediaType.ALL};

    /**
     * 支持的HttpMethod
     */
    @Setter
    private HttpMethod[] supportedHttpMethods = new HttpMethod[]{HttpMethod.POST, HttpMethod.GET};

    /**
     * 排除的uri
     */
    @Setter
    private Set<String> excludePaths = null;

    /**
     * 包含的uri
     */
    @Setter
    private Set<String> includePaths = null;

    /**
     * 未配置包含uri时，默认为包含
     */
    @Setter
    private boolean defaultInclude = true;


    @Setter
    private PathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (!ServletUtils.supportHttpMethod(request, supportedHttpMethods)) {
            filterChain.doFilter(request, response);
            return;
        }
        if (!ServletUtils.supportMediaType(request, supportedMediaTypes)) {
            filterChain.doFilter(request, response);
            return;
        }
        String requestUri = request.getRequestURI();
        if (isInclude(requestUri) && !isExclude(requestUri)) {
            doFilterInner(request, response, filterChain);
            return;
        }
        filterChain.doFilter(request, response);
    }

    protected abstract void doFilterInner(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException;

    private boolean isExclude(String requestUri) {
        if (CollectionUtil.isEmpty(excludePaths)) {
            return false;
        }
        for (String pattern : excludePaths) {
            if (antPathMatcher.match(pattern, requestUri)) {
                return true;
            }
        }
        return false;
    }

    private boolean isInclude(String requestUri) {
        if (CollectionUtil.isEmpty(includePaths)) {
            return defaultInclude;
        }
        for (String pattern : includePaths) {
            if (antPathMatcher.match(pattern, requestUri)) {
                return true;
            }
        }
        return false;
    }
}
