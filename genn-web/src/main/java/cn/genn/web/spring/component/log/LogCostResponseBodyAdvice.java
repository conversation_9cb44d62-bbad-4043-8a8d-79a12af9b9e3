package cn.genn.web.spring.component.log;

import cn.genn.web.spring.annotation.LogCost;
import cn.genn.web.spring.component.base.AbstractAnnotationResponseBodyAdvice;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;


/**
 * <AUTHOR>
 */
@ControllerAdvice
public class LogCostResponseBodyAdvice extends AbstractAnnotationResponseBodyAdvice<LogCost> {

    @Setter
    private boolean global = false;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return super.supports(returnType, converterType) || global;
    }

    @Override
    protected Object doBeforeBodyWrite(Object body, LogCost annotation, HttpServletRequest request, HttpServletResponse response, HttpHeaders httpHeaders) {
        request.setAttribute(LogCostInterceptor.RESPONSE_BODY, body);
        return body;
    }
}
