package cn.genn.web.spring.component.forward;

import cn.genn.web.spring.utils.ServletUtils;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.http.HttpUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class ServiceProxy {

    private static final int MODE_LOAD_BALANCED = 1;
    private static final int MODE_SINGLE_HOST = 2;
    public static final String FORWARD_HEADER = "x-genn-forward";

    @Resource
    RestTemplate lbRestTemplate;

    @Resource
    RestTemplate restTemplate;

    @SneakyThrows
    public void forward(String serviceName, HttpServletRequest request, HttpServletResponse response) {
        String queryString = request.getQueryString();
        int restMode = choiceRestMode(serviceName);
        String apiUrl = getServiceApiUrl(serviceName, getPublishUri(request), queryString, restMode);
        Map<String, String> requestHeaders = JakartaServletUtil.getHeaderMap(request);
        requestHeaders.put(FORWARD_HEADER, IdUtil.fastSimpleUUID());
        Object params = null;
        String contentType = request.getContentType();
        if (log.isDebugEnabled()) {
            log.debug("contentType={}", contentType);
        }
        if (contentType == null) {
            contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE;
            requestHeaders.put(HttpHeaders.CONTENT_TYPE, contentType);
        }
        params = getRequestBody(contentType, request);

        if (log.isDebugEnabled()) {
            log.debug("apiUrl={}; queryString={}", apiUrl, queryString);
        }

        ResponseEntity<byte[]> resp = invokeCustom(apiUrl, params, requestHeaders, HttpMethod.valueOf(request.getMethod()), restMode);

        HttpHeaders headers = resp.getHeaders();
        if (resp.getStatusCode().value() == 200 && headers.containsKey(HttpHeaders.CONTENT_DISPOSITION)) {
            //对文件下载的处理
            response.setStatus(resp.getStatusCode().value());
            byte[] fileBytes = resp.getBody();
            response.setContentLength(fileBytes.length);
            response.getOutputStream().write(fileBytes);
        } else {
            //其他请求输出为json
            reply(response, new String(Objects.requireNonNull(resp.getBody()), StandardCharsets.UTF_8));
        }
    }

    public ResponseEntity<byte[]> invokeCustom(String apiUrl, Object params, Map<String, String> headers, HttpMethod httpMethod, int restMode) {
        HttpEntity<Object> requestEntity = null; //new HttpEntity<Object>(params, createHeaders(header));
        if (params instanceof Map) {
            requestEntity = createFormRequestEntity((Map) params, headers);
        } else {
            requestEntity = new HttpEntity<>(params, createHeaders(headers));
        }

        ResponseEntity<byte[]> response;

        if (MODE_LOAD_BALANCED == restMode) {
            response = lbRestTemplate.exchange(apiUrl, httpMethod, requestEntity, byte[].class);
        } else {
            response = restTemplate.exchange(apiUrl, httpMethod, requestEntity, byte[].class);
        }

        return response;
    }

    private HttpEntity<MultiValueMap<String, Object>> createFormRequestEntity(Map<String, Object> paramsMap, Map<String, String> headers) {
        MultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
        for (Map.Entry<String, Object> entry : paramsMap.entrySet()) {
            Object val = entry.getValue();
            if (val instanceof String[] arrayValues) {
                if (arrayValues.length > 0) {
                    multiValueMap.add(entry.getKey(), arrayValues[0]);
                } else {
                    multiValueMap.add(entry.getKey(), arrayValues);
                }
            } else if (val instanceof List listValues) {
                if (!listValues.isEmpty()) {
                    multiValueMap.add(entry.getKey(), listValues.getFirst());
                } else {
                    multiValueMap.add(entry.getKey(), listValues);
                }
            }else {
                multiValueMap.add(entry.getKey(), entry.getValue());
            }
        }

        return new HttpEntity<>(multiValueMap, createHeaders(headers));
    }

    private HttpHeaders createHeaders(Map<String, String> headers) {
        HttpHeaders httpHeaders = new HttpHeaders();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            httpHeaders.add(entry.getKey(), entry.getValue());
        }
        return httpHeaders;
    }

    private String getServiceApiUrl(String serviceName, String apiPath, String queryString, int restMode) throws UnsupportedEncodingException {
        String originalQueryString = decodeQueryString(queryString);
        StringBuilder sbApiUrl = new StringBuilder();
        if (MODE_LOAD_BALANCED == restMode) {
            sbApiUrl.append("http://");
        }
        sbApiUrl.append(serviceName).append("/").append(apiPath);
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(sbApiUrl.toString()).query(originalQueryString);
        UriComponents uriComponents = uriBuilder.build();
        return uriComponents.toUriString();
    }

    private Object getRequestBody(String contentType, HttpServletRequest request) throws IOException {
        Object body = null;
        if (contentType != null && contentType.startsWith("application/json")) {
            body = ServletUtils.getCachedRequestBody(request);
        } else if (contentType == null) {
        } else if (contentType.startsWith("application/x-www-form-urlencoded")) {
            String formBody = ServletUtils.getCachedRequestBody(request);
            if (StrUtil.isNotEmpty(formBody)) {
                body = HttpUtil.decodeParams(formBody, StandardCharsets.UTF_8, true);
            }
        } else {
            body = request.getParameterMap();
        }
        return body;
    }

    private String getPublishUri(HttpServletRequest request) {
        String uri = request.getRequestURI();
        // 格式化一下请求的路径
        uri = uri.replaceAll("//", "/");
        String contextPath = request.getContextPath();
        if (StrUtil.hasLetter(contextPath)) {
            uri = uri.substring(contextPath.length());
        }
        return uri;
    }


    private void reply(HttpServletResponse response, Object httpResponse)
            throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        response.getWriter().print(httpResponse);
    }

    private String decodeQueryString(String queryString) throws UnsupportedEncodingException {
        return !StrUtil.isEmpty(queryString) ? URLDecoder.decode(queryString, "utf-8") : queryString;
    }

    private int choiceRestMode(String serviceName) {
        return serviceName.startsWith("http") || serviceName.startsWith("https") ? MODE_SINGLE_HOST : MODE_LOAD_BALANCED;
    }
}
