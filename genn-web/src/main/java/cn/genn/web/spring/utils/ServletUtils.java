package cn.genn.web.spring.utils;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.web.spring.component.request.BufferedHttpServletRequestWrapper;
import cn.hutool.core.map.MapUtil;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ServletUtils {


    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Objects.requireNonNull(attributes).getRequest();
    }

    public static HttpServletResponse getResponse() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Objects.requireNonNull(attributes).getResponse();
    }

    public static Map<String, Object> getParamMap(HttpServletRequest request) {
        Map<String, Object> paramMap = new HashMap<>();
        Enumeration<String> parameterNames = request.getParameterNames();
        String name;
        while (parameterNames.hasMoreElements()) {
            name = parameterNames.nextElement();
            paramMap.put(name, request.getParameterValues(name));
        }
        return paramMap;
    }

    public static Map<String, String> getHeaderMap(HttpServletRequest request) {
        final Map<String, String> headerMap = new HashMap<>();

        final Enumeration<String> names = request.getHeaderNames();
        String name;
        while (names.hasMoreElements()) {
            name = names.nextElement();
            headerMap.put(name, request.getHeader(name));
        }

        return headerMap;
    }

    public static Map<String, Object> getJsonBodyMap(HttpServletRequest request) {
        if (!isJsonRequest(request)) {
            return MapUtil.empty();
        }
        String body = ServletUtils.getCachedRequestBody(request);
        return JsonUtils.parseToMap(body, String.class, Object.class);
    }

    /**
     * 是否支持HttpMethod
     */
    public static boolean supportHttpMethod(HttpServletRequest request, HttpMethod... supportedHttpMethods) {
        String requestMethod = request.getMethod().toUpperCase();
        for (HttpMethod httpMethod : supportedHttpMethods) {
            if (httpMethod.name().equals(requestMethod)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否支持媒体类型
     */
    public static boolean supportMediaType(HttpServletRequest request, MediaType... supportedMediaTypes) {
        for (MediaType mediaType : supportedMediaTypes) {
            if (mediaType.equals(MediaType.ALL)) {
                return true;
            }
        }
        String contentType = request.getContentType();
        if (contentType == null) {
            return false;
        }
        contentType = contentType.toLowerCase();
        for (MediaType mediaType : supportedMediaTypes) {
            if (contentType.startsWith(mediaType.toString())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否POST请求
     *
     * @param request
     * @return
     */
    public static boolean isPost(HttpServletRequest request) {
        return supportHttpMethod(request, HttpMethod.POST);
    }

    /**
     * 是否JSON请求
     */
    public static boolean isJsonRequest(HttpServletRequest request) {
        return isPost(request) && supportMediaType(request, MediaType.APPLICATION_JSON);
    }

    /**
     * 获取请求体,支持重复读取
     * @param request
     * @return
     */
    public static String getCachedRequestBody(HttpServletRequest request) {
        BufferedHttpServletRequestWrapper bufferedHttpServletRequest = ServletUtils.getHttpServletRequestWrapper(request, BufferedHttpServletRequestWrapper.class);
        if (bufferedHttpServletRequest != null) {
            return bufferedHttpServletRequest.getRequestBody();
        }
        return null;
    }

    /**
     * 获取请求指定类型的 RequestWrapper
     */
    public static <T extends HttpServletRequestWrapper> T getHttpServletRequestWrapper(ServletRequest request, Class<T> clazz) {
        if (request == null) {
            return null;
        }
        while (request instanceof HttpServletRequestWrapper) {
            if (request.getClass().isAssignableFrom(clazz)) {
                return (T) request;
            }
            request = ((HttpServletRequestWrapper) request).getRequest();
        }
        return null;
    }

}
