package cn.genn.web.spring.component.base;

import cn.genn.core.exception.BaseException;
import cn.genn.core.exception.CommonCode;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.web.spring.component.forward.ForwardData;
import cn.genn.web.spring.component.request.CustomServletInputStream;
import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.IoUtil;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动给请求注入参数值
 * 1. 支持表单类型
 * 2. 支持json类型
 * 3. 不支持文件上传
 *
 * <AUTHOR>
 */
public abstract class AbstractParamHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private final Map<String, Object> modifiedParameters;
    private final List<ForwardData> additionalParams;
    private final String requestBody;

    public AbstractParamHttpServletRequestWrapper(HttpServletRequest request, List<ForwardData> extendParams) {
        super(request);
        additionalParams = extendParams;
        modifiedParameters = new HashMap<>(request.getParameterMap());
        modifiedParameters.putAll(additionalParams.stream().collect(Collectors.toMap(ForwardData::getKey, ForwardData::getValue)));
        requestBody = getRequestBody(request);
    }

    private String getRequestBody(HttpServletRequest request) {
        try (final InputStream inputStream = request.getInputStream()) {
            FastByteArrayOutputStream outputStream = IoUtil.read(inputStream);
            return outputStream.toString(StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new BaseException(CommonCode.FAIL);
        }
    }

    @Override
    public String getParameter(String name) {
        Object values = modifiedParameters.get(name);
        if (values != null) {
            if (values instanceof String[]) {
                String[] valueArray = (String[]) values;
                if (valueArray.length > 0) {
                    return valueArray[0];
                }
            } else {
                return values.toString();
            }
        }
        return super.getParameter(name);
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> map = new HashMap<>();
        modifiedParameters.forEach((key, value) -> {
            if (value instanceof String[]) {
                map.put(key, (String[]) value);
            } else {
                map.put(key, new String[]{value.toString()});
            }
        });
        return Collections.unmodifiableMap(map);
    }

    @Override
    public Enumeration<String> getParameterNames() {
        return Collections.enumeration(modifiedParameters.keySet());
    }

    @Override
    public String[] getParameterValues(String name) {
        Object values = modifiedParameters.get(name);
        if (values != null) {
            if (values instanceof String[]) {
                return (String[]) values;
            } else {
                return new String[]{values.toString()};
            }
        }
        return super.getParameterValues(name);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        Map<String, Object> bodyMap = JsonUtils.parseToMap(requestBody, String.class, Object.class);
        bodyMap.putAll(additionalParams.stream().collect(Collectors.toMap(ForwardData::getKey, ForwardData::getValue)));
        return new CustomServletInputStream(JsonUtils.toJson(bodyMap).getBytes(StandardCharsets.UTF_8));
    }

}
