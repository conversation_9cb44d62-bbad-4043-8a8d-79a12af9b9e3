package cn.genn.web.spring.component.forward;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.map.MapUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ForwardData {

    private String key;

    private Object value;

    private boolean needSerialize;

    private boolean needBase64;

    private ForwardData() {
    }


    public static ForwardData of(String key, Object value) {
        return of(key, value, true, true);
    }

    public static ForwardData ofNormal(String key, Object value) {
        return of(key, value, false, false);
    }

    public static ForwardData of(String key, Object value, boolean needSerialize, boolean needBase64) {
        ForwardData forwardData = new ForwardData();
        forwardData.setKey(key);
        forwardData.setNeedSerialize(needSerialize);
        forwardData.setNeedBase64(needBase64);
        if (value != null) {
            if (needSerialize) {
                value = JsonUtils.toJson(value);
            }
            if (needBase64) {
                value = Base64.encode(value.toString());
            }
        }
        forwardData.setValue(value);
        return forwardData;
    }

    public static List<ForwardData> of(Map<String, Object> dataMap) {
        if (MapUtil.isEmpty(dataMap)) {
            return null;
        }
        List<ForwardData> dataList = new ArrayList<>();
        dataMap.forEach((key, value) -> {
            ForwardData forwardData = ForwardData.of(key, value);
            dataList.add(forwardData);
        });
        return dataList;
    }
}
