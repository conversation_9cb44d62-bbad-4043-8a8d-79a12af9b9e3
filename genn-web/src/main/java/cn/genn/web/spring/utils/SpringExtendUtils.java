package cn.genn.web.spring.utils;

import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 */
public class SpringExtendUtils {

    /**
     * 追加上下文路径
     * @param paths 路径
     * @return
     */
    public static String[] appendContextPath(String... paths) {
        String contextPath = SpringUtil.getProperty("server.servlet.context-path");
        if (contextPath == null || contextPath.isEmpty()) {
            return paths;
        }
        String[] result = new String[paths.length];
        for (int i = 0; i < paths.length; i++) {
            result[i] = contextPath + paths[i];
        }
        return result;
    }
}
