package cn.genn.web.spring.annotation;

import cn.genn.web.spring.component.sign.SignSourceDefinition;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequestSign {

    /**
     * 是否启用
     */
    boolean enabled() default true;

    /**
     * 签名前缀,会在签完名后拼接在前面
     * @return
     */
    String prefix() default "";

    /**
     * 是否在前面拼接http请求方法
     * @return
     */
    boolean enableHttpMethod() default true;

    /**
     * 是否在前面拼接http请求路径
     * @return
     */
    boolean enableHttpPath() default true;

    /**
     * 时间戳是否参与签名
     */
    boolean enableTimestamp() default true;

    /**
     * 是否校验时间戳时间
     * @return
     */
    boolean checkTimestamp() default true;

    /**
     * 时间戳key
     */
    String timestampKey() default  "timestamp";

    /**
     * 时间戳过期时间(秒)
     */
    int timestampExpire() default 60;

    /**
     * nonce是否参与签名
     */
    boolean enableNonce() default true;

    /**
     * nonce key
     */
    String nonceKey() default "nonce";

    /**
     * 签名key,从header中取
     */
    String signKey() default  "sign";

    /**
     * 签名源获取bean名称,如果设置,直接取该实现返回的签名源作为签名数据
     * @see SignSourceDefinition
     */
    String signSourceDefinition() default "";

    /**
     * 签名秘钥获取实现类,必须设置
     */
    String signSecretDefinition() default "";

    /**
     * 签名实现类,不设置取默认内置实现
     */
    String signatureAlgorithm() default "defaultSignAlgorithm";

    /**
     * 无需签名的实现类
     */
    String noSignDefinition() default "";

    /**
     * 是否签名请求参数,如果时间戳和nonce在请求参数中,会自动签名
     * 建议设置为false,不对参数签名
     */
    boolean signRequestParam() default false;
}
