package cn.genn.web.spring.annotation;

import java.lang.annotation.*;

/**
 * 打印请求日志
 *
 * {@link cn.genn.web.spring.component.log.LogCostInterceptor}
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogCost {

    /**
     * 设置日志名称
     *
     * @return
     */
    String value() default "";

    /**
     * 设置日志模块
     *
     * @return
     */
    String module() default "";

    /**
     * 是否打印IP
     *
     * @return
     */
    boolean ip() default true;

    /**
     * 是否打印URI
     *
     * @return
     */
    boolean uri() default true;

    /**
     * 是否打印参数
     *
     * 如果需要打印requestBody内容，需要加载过滤器 {@link cn.genn.web.spring.component.request.BufferedHttpServletRequestFilter}
     *
     * @return
     */
    boolean params() default true;

    /**
     * 忽略的参数
     *
     * @return
     */
    String[] ignoreParams() default {};

    /**
     * 是否打印RequestBody
     *
     * @return
     */
    boolean logRequestBody() default true;

    /**
     * 是否打印结果
     *
     * @return
     */
    boolean result() default true;

    /**
     * 是否打印空返回
     *
     * @return
     */
    boolean logEmptyResult() default true;

    /**
     * 结果最大输出长度，0全部输出
     *
     * @return
     */
    int resultMaxLength() default 0;

    /**
     * requestBody最大输出长度，0全部输出
     *
     * @return
     */
    int requestBodyMaxLength() default 0;

    /**
     * 每行日志最大长度，超长会换行，0一行输出
     *
     *
     * @return
     */
    int maxLengthPerLine() default 0;

    /**
     * 指定logger，默认当前类
     *
     * @return
     */
    String logger() default "";

    /**
     * 大于1000ms警告
     *
     * @return
     */
    long warningThreshold() default 1000;

    /**
     * 每100条记录打印一条统计日志， 0不统计
     *
     * @return
     */
    long statLogFrequency() default 100;

}
