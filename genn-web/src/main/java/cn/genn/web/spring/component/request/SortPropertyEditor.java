package cn.genn.web.spring.component.request;

import cn.genn.core.model.page.OrderBy;
import cn.genn.core.utils.Assert;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.StrUtil;

import java.beans.PropertyEditorSupport;

/**
 * 非json请求,需要排序字段时,字段解析
 * <AUTHOR>
 */
public class SortPropertyEditor extends PropertyEditorSupport {

    @Override
    public void setAsText(String text) throws IllegalArgumentException {
        if (StrUtil.isBlank(text)) {
            setValue(null);
            return;
        }
        OrderBy order = JsonUtils.parse(text, OrderBy.class);
        Assert.nonNull(order, "排序字段");
        setValue(order);
    }
}
