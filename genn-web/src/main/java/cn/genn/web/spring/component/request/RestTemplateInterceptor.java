package cn.genn.web.spring.component.request;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.support.HttpRequestWrapper;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.zip.GZIPInputStream;

/**
 * RestTemplate 默认拦截器
 *
 * <AUTHOR>
 */
public class RestTemplateInterceptor implements ClientHttpRequestInterceptor {

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        Map<String, RestTemplateRequestCustomizer> requestCustomizerMap = SpringUtil.getBeansOfType(RestTemplateRequestCustomizer.class);
        if (MapUtil.isNotEmpty(requestCustomizerMap)) {
            for (RestTemplateRequestCustomizer customizer : requestCustomizerMap.values()) {
                request = customizer.customize(request);
            }
        }
        ClientHttpResponse response = execution.execute(request, body);
        return new SmartGzipClientHttpResponse(response);
    }

    public interface RestTemplateRequestCustomizer {
        HttpRequestWrapper customize(HttpRequest request);
    }

    private record SmartGzipClientHttpResponse(ClientHttpResponse response) implements ClientHttpResponse {

        @Override
            public InputStream getBody() throws IOException {
                if ("gzip".equalsIgnoreCase(getHeaders().getFirst(HttpHeaders.CONTENT_ENCODING))) {
                    try (GZIPInputStream gzipInputStream = new GZIPInputStream(response.getBody());
                         ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = gzipInputStream.read(buffer)) > 0) {
                            outputStream.write(buffer, 0, len);
                        }
                        return new ByteArrayInputStream(outputStream.toByteArray());
                    }
                }
                return response.getBody();
            }

            // 其他方法直接委托给原始响应
            @Override
            public HttpHeaders getHeaders() {
                return response.getHeaders();
            }

        @Override
        public HttpStatusCode getStatusCode() throws IOException {
            return response.getStatusCode();
        }

        @Override
            public String getStatusText() throws IOException {
                return response.getStatusText();
            }

            @Override
            public void close() {
                response.close();
            }
        }
}
