package cn.genn.web.spring.component.forward;


import cn.genn.web.spring.component.base.AbstractParamHttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ForwardParamHttpServletRequestWrapper extends AbstractParamHttpServletRequestWrapper {

    public ForwardParamHttpServletRequestWrapper(HttpServletRequest request, List<ForwardData> extendParams) {
        super(request, extendParams);
    }
}
