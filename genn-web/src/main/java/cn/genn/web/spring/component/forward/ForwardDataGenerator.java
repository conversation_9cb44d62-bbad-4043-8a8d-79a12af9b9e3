package cn.genn.web.spring.component.forward;

import java.util.List;
import java.util.Map;

/**
 * 转发时数据生成器
 * 支持参数生成
 * 支持请求头生成
 * 注意:子类需要使用@Component注解注入到spring中,否则无法生效.
 * <AUTHOR>
 */
public interface ForwardDataGenerator {

    /**
     * 生成转发时的数据
     * @param requestParamMap 原始的方法参数,不可变
     * @param headers 原始的请求头，不可变
     * @return
     */
    List<ForwardData> generate(Map<String, Object> requestParamMap, Map<String, String> headers);

}
