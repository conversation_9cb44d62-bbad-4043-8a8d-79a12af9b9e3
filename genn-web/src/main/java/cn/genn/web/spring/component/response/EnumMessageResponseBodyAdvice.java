package cn.genn.web.spring.component.response;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.web.spring.annotation.EnumMessageFieldWrapper;
import cn.genn.web.spring.annotation.EnumMessageWrapper;
import cn.hutool.core.util.ReflectUtil;
import lombok.Data;
import org.springframework.core.MethodParameter;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 */
@Order(Ordered.HIGHEST_PRECEDENCE + 101)
@RestControllerAdvice
public class EnumMessageResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    public static final Map<String, Set<String>> enumFiledMap = new ConcurrentHashMap<>();
    public static final Map<String, Map<String, String>> enumDescMap = new ConcurrentHashMap<>();

    public static final Map<Field, EnumMessageFieldObj> annotationCache = new ConcurrentHashMap<>();


    public EnumMessageResponseBodyAdvice() {
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        EnumMessageWrapper annotation = returnType.hasMethodAnnotation(EnumMessageWrapper.class)
                ? returnType.getMethodAnnotation(EnumMessageWrapper.class)
                : returnType.getContainingClass().getAnnotation(EnumMessageWrapper.class);
        return annotation != null;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        EnumMessageWrapper enumMessageWrapper = returnType.hasMethodAnnotation(EnumMessageWrapper.class)
                ? returnType.getMethodAnnotation(EnumMessageWrapper.class)
                : returnType.getContainingClass().getAnnotation(EnumMessageWrapper.class);
        if (body == null || enumMessageWrapper == null) {
            return null;
        }
        return appendObjEnumMessage(body, enumMessageWrapper);
    }

    private Object appendObjEnumMessage(Object result, EnumMessageWrapper enumMessageWrapper) {
        if (result == null) {
            return null;
        }
        //区分result类型
        if (result instanceof Collection<?>) {
            //集合类型
            return appendCollectionEnumMessage((Collection<?>)result, enumMessageWrapper);
        }
        if (result instanceof Object[]) {
            //数组类型
            return appendListEnumMessage(Arrays.asList((Object[]) result), enumMessageWrapper);
        }
        if (result instanceof Map<?,?>) {
            //map类型
            return appendMapEnumMessage((Map<?, ?>) result, enumMessageWrapper);
        }
        if (result instanceof PageResultDTO<?>) {
            //分页对象
            PageResultDTO<Object> pageResponse = (PageResultDTO<Object>) result;
            pageResponse.setList(appendListEnumMessage(pageResponse.getList(), enumMessageWrapper));
            return pageResponse;
        }
        if (result instanceof ResponseResult<?>) {
            //响应对象
            ResponseResult<Object> responseMessage = (ResponseResult<Object>) result;
            responseMessage.setData(appendObjEnumMessage(responseMessage.getData(), enumMessageWrapper));
            return responseMessage;
        }
        return appendSingleEnumMessage(result, enumMessageWrapper);
    }

    private Map<?, ?> appendMapEnumMessage(Map<?, ?> result, EnumMessageWrapper enumMessageWrapper) {
        Map<Object, Object> map;
        if (result instanceof LinkedHashMap<?,?>) {
            map = new LinkedHashMap<>();
        }else {
            map = new HashMap<>();
        }
        result.forEach((k, v) -> {
            map.put(k, appendObjEnumMessage(v, enumMessageWrapper));
        });
        return map;
    }

    private List<Object> appendListEnumMessage(Collection<?> result, EnumMessageWrapper enumMessageWrapper) {
        List<Object> list = new ArrayList<>();
        result.forEach(v -> {
            list.add(appendObjEnumMessage(v, enumMessageWrapper));
        });
        return list;
    }

    private Set<Object> appendSetEnumMessage(Collection<?> result, EnumMessageWrapper enumMessageWrapper) {
        Set<Object> set;
        if (result instanceof LinkedHashSet<?>) {
            set = new LinkedHashSet<>();
        }else if (result instanceof HashSet<?>){
            set = new HashSet<>();
        }else {
            set = new TreeSet<>();
        }
        result.forEach(v -> {
            set.add(appendObjEnumMessage(v, enumMessageWrapper));
        });
        return set;
    }

    private Collection<Object> appendCollectionEnumMessage(Collection<?> result, EnumMessageWrapper enumMessageWrapper) {
        if (result instanceof List<?>) {
            return appendListEnumMessage(result, enumMessageWrapper);
        }
        return appendSetEnumMessage(result, enumMessageWrapper);
    }

    private Object appendSingleEnumMessage(Object v, EnumMessageWrapper enumMessageWrapper) {
        if (v == null) {
            return null;
        }
        if (!hasEnumFiled(v)) {
            return v;
        }
        Class<?> cls = v.getClass();
        String name = cls.getName();
        Set<String> enumFiledList = enumFiledMap.get(name);
        LinkedHashMap<String, Object> map = JsonUtils.parseToObjectLinkedMap(JsonUtils.toJson(v));
        enumFiledList.forEach(enumFiled -> {
            String enumDescFieldName = enumMessageWrapper.enumDescFiledName();
            String enumDescSuffix = enumMessageWrapper.enumDescSuffix();
            Object enumValue = ReflectUtil.getFieldValue(v, enumFiled);
            if (enumValue == null) {
                return;
            }
            Field field = ReflectUtil.getField(cls, enumFiled);
            EnumMessageFieldObj filedInfo = annotationCache.computeIfAbsent(field, k -> {
                EnumMessageFieldWrapper annotation = field.getAnnotation(EnumMessageFieldWrapper.class);
                EnumMessageFieldObj fieldObj = new EnumMessageFieldObj();
                fieldObj.setExist(annotation != null);
                fieldObj.setFieldWrapper(annotation);
                return fieldObj;
            });
            if (filedInfo.exist) {
                enumDescFieldName = filedInfo.getFieldWrapper().enumDescFiledName();
                enumDescSuffix = filedInfo.getFieldWrapper().enumDescSuffix();
            }
            //获取field的枚举类型
            Class<?> enumType = field.getType();
            //获取枚举的description字段
            Field descriptionField = ReflectUtil.getField(enumType, enumDescFieldName);
            if (descriptionField == null) {
                return;
            }
            //获取枚举的description值,该值可以缓存
            enumDescMap.computeIfAbsent(enumType.getName(), k -> {
                //获取枚举中每个name和对应的description
                Map<String, String> currMap = new HashMap<>();
                Object[] enumConstants = enumType.getEnumConstants();
                for (Object enumConstant : enumConstants) {
                    currMap.put(((Enum<?>)enumConstant).name(), (String) ReflectUtil.getFieldValue(enumConstant, descriptionField));
                }
                return currMap;
            });
            //将枚举的description值设置到map中
            map.put(enumFiled + enumDescSuffix, enumDescMap.getOrDefault(enumType.getName(), new HashMap<>()).get(((Enum<?>)enumValue).name()));
        });
        return map;
    }

    private boolean hasEnumFiled(Object result) {
        Class<?> cls = result.getClass();
        String name = cls.getName();
        Set<String> enumFiledList = enumFiledMap.get(name);
        if (enumFiledList == null) {
            Field[] fields = ReflectUtil.getFields(cls);
            enumFiledList = new HashSet<>();
            for (Field field : fields) {
                if (field.getType().isEnum()) {
                    enumFiledList.add(field.getName());
                }
            }
            enumFiledMap.put(name, enumFiledList);
        }
        if (enumFiledList.isEmpty()) {
            return false;
        }
        return true;
    }

    @Data
    public static class EnumMessageFieldObj {
        private boolean exist;
        private EnumMessageFieldWrapper fieldWrapper;
    }

}
