package cn.genn.web.spring.component.base;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.lang.annotation.Annotation;

/**
 * 基于注解的参数解析器
 * <AUTHOR>
 */
public abstract class AbstractAnnotationMethodArgumentResolver<T extends Annotation> implements HandlerMethodArgumentResolver {

    private final Class<T> clazz;

    public AbstractAnnotationMethodArgumentResolver(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(this.clazz);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        T annotation = parameter.getParameterAnnotation(this.clazz);
        HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
        HttpServletResponse response = webRequest.getNativeResponse(HttpServletResponse.class);
        return doResolveArgument(request, response, annotation, parameter);
    }

    protected abstract Object doResolveArgument(HttpServletRequest request, HttpServletResponse response, T annotation, MethodParameter parameter) throws Exception;
}
