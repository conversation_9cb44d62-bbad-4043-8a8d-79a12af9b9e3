package cn.genn.web.spring.component.base;


import cn.genn.web.spring.component.forward.ForwardData;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import java.util.*;
import java.util.stream.Collectors;


/**
 * http请求头添加包装类
 *
 * <AUTHOR>
 */
public abstract class AbstractHeaderHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private final List<ForwardData> addHeaders;

    public AbstractHeaderHttpServletRequestWrapper(HttpServletRequest request, List<ForwardData> headers) {
        super(request);
        if (headers == null) {
            headers = Collections.emptyList();
        }
        addHeaders = headers;
    }

    @Override
    public String getHeader(String name) {
        for (ForwardData header : addHeaders) {
            if (header.getKey().equalsIgnoreCase(name)) {
                return header.getValue() == null ? "" : header.getValue().toString();
            }
        }
        return super.getHeader(name);
    }

    @Override
    public Enumeration<String> getHeaders(String name) {
        Set<String> addHeader = addHeaders.stream().map(ForwardData::getKey).map(String::toLowerCase).collect(Collectors.toSet());
        if (addHeader.contains(name.toLowerCase())) {
            return Collections.enumeration(Collections.singletonList(getHeader(name)));
        }
        return super.getHeaders(name);
    }

    @Override
    public Enumeration<String> getHeaderNames() {
        Set<String> names = new HashSet<>(Collections.list(super.getHeaderNames()));
        names.addAll(addHeaders.stream().map(ForwardData::getKey).collect(Collectors.toSet()));
        return Collections.enumeration(names);
    }
}
