package cn.genn.web.spring.annotation;

import java.lang.annotation.*;

/**
 * 对接口返回的数据中的枚举进行包装
 * <p>
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface EnumMessageWrapper {

    /**
     * 枚举描述的字段名
     *
     * @return
     */
    String enumDescFiledName() default "description";

    /**
     * 枚举字段后缀
     * @return
     */
    String enumDescSuffix() default "Desc";


}
