package cn.genn.web.spring.component.request;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;

import java.io.ByteArrayInputStream;

/**
 * <AUTHOR>
 */
public class CustomServletInputStream extends ServletInputStream {

    private final ByteArrayInputStream input;

    public CustomServletInputStream(byte[] bytes) {
        input = new ByteArrayInputStream(bytes);
    }

    @Override
    public boolean isFinished() {
        return false;
    }

    @Override
    public boolean isReady() {
        return true;
    }

    @Override
    public void setReadListener(ReadListener readListener) {
    }

    @Override
    public int read() {
        return input.read();
    }
}
