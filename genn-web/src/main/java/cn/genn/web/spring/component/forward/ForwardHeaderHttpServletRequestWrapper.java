package cn.genn.web.spring.component.forward;


import cn.genn.web.spring.component.base.AbstractHeaderHttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ForwardHeaderHttpServletRequestWrapper extends AbstractHeaderHttpServletRequestWrapper {

    public ForwardHeaderHttpServletRequestWrapper(HttpServletRequest request, List<ForwardData> extendHeader) {
        super(request, extendHeader);
    }
}
