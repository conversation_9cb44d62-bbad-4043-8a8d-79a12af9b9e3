package cn.genn.web.spring.component.request;

import cn.genn.web.http.HttpClientProperties;
import cn.genn.web.http.HttpClientType;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.ClassUtils;

import java.time.Duration;

/**
 * 根据配置创建ClientHttpRequestFactory
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpClientFactoryCreator {

    private final static boolean httpComponentPresent =
            ClassUtils.isPresent("org.apache.hc.client5.http.classic.HttpClient",
                    HttpClientFactoryCreator.class.getClassLoader());

    public static ClientHttpRequestFactory clientHttpRequestFactory(HttpClientProperties httpClientProperties) {
        if (httpClientProperties.getType() == HttpClientType.HTTP_COMPONENT && httpComponentPresent) {
            int maxConnPerRoute = (httpClientProperties.getMaxConnTotal() + 1) / 2;
            // 创建连接管理器
            PoolingHttpClientConnectionManager connectionManager = PoolingHttpClientConnectionManagerBuilder.create()
                    .setMaxConnPerRoute(maxConnPerRoute)
                    .setMaxConnTotal(httpClientProperties.getMaxConnTotal())
                    .build();
            // 创建 HttpClient 5
            HttpClient httpClient = HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .disableContentCompression()
                    .build();
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
            // 设置超时
            factory.setConnectionRequestTimeout(Duration.ofMillis(httpClientProperties.getConnectionRequestTimeout()));
            factory.setConnectTimeout(Duration.ofMillis(httpClientProperties.getConnectTimeout()));
            factory.setReadTimeout(Duration.ofMillis(httpClientProperties.getReadTimeout()));
            return factory;
        } else {
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
            factory.setConnectTimeout(httpClientProperties.getConnectTimeout());
            factory.setReadTimeout(httpClientProperties.getReadTimeout());
            return factory;
        }
    }

}
