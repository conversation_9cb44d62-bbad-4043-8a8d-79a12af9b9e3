package cn.genn.web.spring.component.request;

import cn.genn.core.context.BaseRequestContext;
import cn.genn.web.WebConstants;
import cn.genn.web.feign.component.FeignStateContext;
import cn.genn.web.spring.utils.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;


/**
 * 注入请求上下文,使用时需要注意拦截器顺序.建议将该拦截器优先级设置为最高,即Integer.MIN_VALUE
 *
 * <AUTHOR>
 */
public class RequestContextInterceptor implements HandlerInterceptor {

    private final Class<? extends BaseRequestContext> contextType;

    public RequestContextInterceptor(Class<? extends BaseRequestContext> contextType) {
        this.contextType = contextType;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        BaseRequestContext.clear();
        FeignStateContext.clear();
        BaseRequestContext context = contextType.newInstance();
        context.beforeSet();
        BaseRequestContext.set(context);
        context.setRequestHeader(ServletUtils.getHeaderMap(request));
        context.setGtraceId(request.getParameter(WebConstants.REQUEST_PARAM_GTRACE_ID));
        context.afterSet();
        return true;
    }
}
