package cn.genn.web.spring.component.forward;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.URLUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import java.net.URI;

/**
 * 转发请求的HttpServletRequestWrapper
 *
 * <AUTHOR>
 */
public class ForwardUrlHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private String targetService;
    private String targetUrl;

    public ForwardUrlHttpServletRequestWrapper(HttpServletRequest request, String targetService, String targetUrl) {
        super(request);
        this.targetService = targetService;
        this.targetUrl = targetUrl;
    }

    /**
     * 返回转发的url,包含host和path
     *
     * @return
     */
    @Override
    public StringBuffer getRequestURL() {
        URI host = URLUtil.getHost(URLUtil.url(super.getRequestURL().toString()));
        return new StringBuffer(host.toString() + getRequestURI());
    }

    /**
     * 返回转发的path
     *
     * @return
     */
    @Override
    public String getRequestURI() {
        return getContextPath() + buildForwardUrl(super.getRequestURI());
    }

    private String buildForwardUrl(String originUrl) {
        if (CharSequenceUtil.isNotBlank(targetUrl)) {
            return targetUrl;
        }
        originUrl = URLUtil.getPath(originUrl);
        return originUrl.substring(getContextPath().length());
    }
}
