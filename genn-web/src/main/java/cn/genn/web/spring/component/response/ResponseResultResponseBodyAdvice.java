package cn.genn.web.spring.component.response;

import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.ResUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.web.feign.FeignConstants;
import cn.genn.web.spring.annotation.ForwardProxy;
import cn.genn.web.spring.annotation.ResponseResultWrapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Setter;
import org.springframework.core.MethodParameter;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;


/**
 * <AUTHOR>
 */
@Order(Ordered.HIGHEST_PRECEDENCE + 100)
@ControllerAdvice
public class ResponseResultResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    @Setter
    private Class<?>[] assignableTypes;

    @Setter
    private Class<?>[] excludeTypes;

    @Setter
    private boolean global = true;

    @Setter
    private String[] pathPatterns = new String[]{"/**"};

    @Setter
    private String[] excludePathPatterns = new String[]{"/doc.html", "/swagger-resources/**", "/v?/api-docs", "/swagger-ui.html/**"};

    @Setter
    private PathMatcher pathMatcher = new AntPathMatcher();


    public ResponseResultResponseBodyAdvice(Class<?>... assignableTypes) {
        this.assignableTypes = assignableTypes;
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        Class<?> containingClass = returnType.getContainingClass();
        ResponseResultWrapper annotation = returnType.hasMethodAnnotation(ResponseResultWrapper.class)
                ? returnType.getMethodAnnotation(ResponseResultWrapper.class)
                : containingClass.getAnnotation(ResponseResultWrapper.class);
        if (annotation != null) {
            return !annotation.ignore();
        }
        if (returnType.hasMethodAnnotation(ForwardProxy.class)) {
            return false;
        }
        if (global) {
            return true;
        }
        return isAssignableType(containingClass) && !isExcludeType(containingClass);
    }

    private boolean isAssignableType(Class<?> clazz) {
        if (assignableTypes == null || assignableTypes.length == 0) {
            return true;
        }
        for (Class<?> assignableType : assignableTypes) {
            if (assignableType.isAssignableFrom(clazz)) {
                return true;
            }
        }
        return false;
    }

    private boolean isExcludeType(Class<?> clazz) {
        if (excludeTypes == null || excludeTypes.length == 0) {
            return false;
        }
        for (Class<?> excludeType : excludeTypes) {
            if (excludeType.isAssignableFrom(clazz)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        boolean isFeign = request.getHeaders().containsKey(FeignConstants.FEIGN_REQUEST_HEADER);
        if (isFeign) {
            return body;
        }
        if (body instanceof ResponseResult) {
            return body;
        }
        if (global) {
            String path = getRequestPath(((ServletServerHttpRequest) request).getServletRequest());
            if (!matches(path)) {
                return body;
            }
        }
        ResponseResult<Object> result = ResUtils.ok(body);
        if (String.class.isAssignableFrom(returnType.getMethod().getReturnType())) {
            return JsonUtils.toJson(result);
        }
        return result;
    }

    private boolean matches(String path) {
        for (String pattern : excludePathPatterns) {
            if (pathMatcher.match(pattern, path)) {
                return false;
            }
        }
        for (String pattern : pathPatterns) {
            if (pathMatcher.match(pattern, path)) {
                return true;
            }
        }
        return false;
    }

    private String getRequestPath(HttpServletRequest request) {
        String context = request.getContextPath();
        String uri = request.getRequestURI();
        if (StringUtils.isEmpty(context) || "/".equals(context)) {
            return uri;
        }
        if (uri.startsWith(context)) {
            return uri.substring(context.length());
        }
        return uri;
    }
}
