package cn.genn.web.spring.component.sign;

import cn.genn.security.constant.CodecType;
import cn.genn.security.constant.SignAlgorithm;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RequestSignProperties {

    /**
     * 是否启用
     */
    private boolean enabled = false;

    /**
     * 是否全局
     */
    private boolean global = false;

    /**
     * 签名前缀,会在签完名后拼接在前面
     */
    private String prefix = "";

    /**
     * 编码类型
     */
    private CodecType codecType = CodecType.HEX;

    /**
     * 签名算法
     */
    private SignAlgorithm algorithm = SignAlgorithm.HmacSHA256;

    /**
     * http请求方法是否参与签名
     */
    private boolean enableHttpMethod = true;

    /**
     * http请求路径是否参与签名
     */
    private boolean enableHttpPath = true;

    /**
     * 时间戳是否参与签名
     */
    private boolean enableTimestamp = true;

    /**
     * 是否校验时间戳时间
     */
    private boolean checkTimestamp = true;

    /**
     * 时间戳key
     */
    private String timestampKey = "timestamp";

    /**
     * 时间戳过期时间(秒)
     */
    private int timestampExpire = 60;

    /**
     * nonce是否参与签名
     */
    private boolean enableNonce = true;

    /**
     * nonce key
     */
    private String nonceKey = "nonce";

    /**
     * 签名key,从header中取
     */
    private String signKey = "sign";

    /**
     * 签名源获取实现类,如果设置,直接取该实现返回的签名源作为签名数据
     * @see SignSourceDefinition
     */
    private String signSourceDefinition = "";

    /**
     * 签名秘钥获取实现类,必须设置.如果未设置会尝试从容器中拿
     * @see SignSecretDefinition
     */
    private String signSecretDefinition = "";

    /**
     * 签名实现类,不设置取默认内置实现
     * @see cn.genn.security.sign.SignatureAlgorithm
     */
    private String signatureAlgorithm = "defaultSignAlgorithm";

    /**
     * 无需签名的实现类
     * @see NoSignDefinition
     */
    private String noSignDefinition = "";

    /**
     * 是否签名请求参数
     */
    private boolean signRequestParam = false;

    /**
     * 拦截地址
     */
    private String[] pathPatterns = new String[]{"/**"};

    /**
     * 拦截排除地址
     */
    private String[] excludePathPatterns = new String[]{"/doc.html", "/swagger-resources/**", "/v?/api-docs"};

}
