package cn.genn.web.spring.component.request;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;
import org.springframework.lang.Nullable;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@SuppressWarnings({"rawtypes", "unchecked"})
@Slf4j
public class EnumConverterFactory implements ConverterFactory<String, Enum> {

    private static final Map<Class, Converter> CONVERTER_MAP = new ConcurrentHashMap<>();

    @Override
    public <T extends Enum> Converter<String, T> getConverter(Class<T> targetType) {
        Converter converter = CONVERTER_MAP.get(targetType);
        if (converter == null) {
            converter = new StringToEnumConverter<>(targetType);
            CONVERTER_MAP.put(targetType, converter);
        }
        return converter;
    }

    static class StringToEnumConverter<T extends Enum> implements Converter<String, T> {

        private final Map<String, T> enumMap = new HashMap<>();
        private final Map<String, T> enumOrignMap = new HashMap<>();

        private StringToEnumConverter(Class<T> enumType) {
            Optional<Field> valueField = Arrays.stream(enumType.getDeclaredFields())
                    .filter(field -> field.isAnnotationPresent(JsonValue.class))
                    .findFirst();
            if (valueField.isPresent()) {
                try {
                    Field field = valueField.get();
                    field.setAccessible(true);
                    T[] enums = enumType.getEnumConstants();
                    for (T e : enums) {
                        enumMap.put(String.valueOf(field.get(e)), e);
                        enumOrignMap.put(e.name(), e);
                    }
                } catch (IllegalAccessException e) {
                    log.error("enum map construct failed:", e);
                }
            } else {
                T[] enums = enumType.getEnumConstants();
                for (T e : enums) {
                    enumMap.put(e.name(), e);
                }
            }
        }

        @Override
        @Nullable
        public T convert(String source) {
            T t = enumMap.get(source);
            if (t == null) {
                t = enumOrignMap.get(source);
            }
            return t;
        }
    }
}



