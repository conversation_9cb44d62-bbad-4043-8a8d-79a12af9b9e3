package cn.genn.web.spring.component.base;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.lang.annotation.Annotation;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractAnnotationInterceptor<T extends Annotation> implements HandlerInterceptor {

    private Class<T> clazz;

    public AbstractAnnotationInterceptor(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        T anno = getAnnotationObj(handler, clazz);
        if (isSupport(anno)) {
            boolean result = doPreHandle(request, response, (HandlerMethod) handler, anno);
            if (!result) {
                doOnFalse(request, response, (HandlerMethod) handler, anno);
            }
            return result;
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        if (!(handler instanceof HandlerMethod)) {
            return;
        }
        T anno = getAnnotationObj(handler, clazz);
        if (isSupport(anno)) {
            doPostHandle(request, response, (HandlerMethod) handler, anno, modelAndView);
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception exception) {
        if (!(handler instanceof HandlerMethod)) {
            return;
        }
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        T anno = getAnnotationObj(handler, clazz);
        if (isSupport(anno)) {
            doAfterCompletion(request, response, (HandlerMethod) handler, anno, exception);
        }
    }

    protected boolean isSupport(T anno) {
        return null != anno;
    }

    protected <A extends Annotation> A getAnnotationObj(Object handler, Class<A> clazz) {
        A annotationObj = null;
        if (handler instanceof HandlerMethod) {
            HandlerMethod method = (HandlerMethod) handler;
            annotationObj = method.getBeanType().getAnnotation(clazz);
            if (annotationObj == null) {
                annotationObj = method.getMethodAnnotation(clazz);
            }
        } else {
            annotationObj = handler.getClass().getAnnotation(clazz);
        }
        return annotationObj;
    }

    protected void doPostHandle(HttpServletRequest request, HttpServletResponse response, HandlerMethod handler, T anno, ModelAndView modelAndView) {

    }

    protected void doAfterCompletion(HttpServletRequest request, HttpServletResponse response, HandlerMethod handler, T anno, Exception exception) {

    }

    protected boolean doPreHandle(HttpServletRequest request, HttpServletResponse response, HandlerMethod handler, T anno) throws Exception {
        return true;
    }

    protected void doOnFalse(HttpServletRequest request, HttpServletResponse response, HandlerMethod handler, T anno) {
        log.error("doOnFalse");
    }
}
