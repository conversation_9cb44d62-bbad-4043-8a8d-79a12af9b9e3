package cn.genn.web.spring.component.request;

import cn.genn.web.spring.component.base.AbstractPatternRequestFilter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.io.IOException;

/**
 *
 * 缓存http请求的过滤器,用于重复获取请求的场景
 * 例如日志等
 *
 * <AUTHOR>
 */
public class BufferedHttpServletRequestFilter extends AbstractPatternRequestFilter {

    public BufferedHttpServletRequestFilter() {
        super();
        setSupportedMediaTypes(new MediaType[]{MediaType.APPLICATION_JSON});
        setSupportedHttpMethods(new HttpMethod[]{HttpMethod.POST});
    }

    @Override
    protected void doFilterInner(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        filterChain.doFilter(new BufferedHttpServletRequestWrapper(request), response);
    }

}
