package cn.genn.web.spring.annotation;


import cn.genn.web.spring.component.forward.DefaultForwardGenerator;
import cn.genn.web.spring.component.forward.ForwardDataGenerator;

import java.lang.annotation.*;

/**
 * 转发代理注解
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface ForwardProxy {

    /**
     * 目标服务名
     * @return
     */
    String targetService();

    /**
     * 指定目标请求路径,默认为原请求路径.
     * @return
     */
    String targetUrl() default "";

    /**
     * 额外的请求头生成
     * @return
     */
    Class<? extends ForwardDataGenerator> extendHeaders() default DefaultForwardGenerator.class;

    /**
     * 额外参数生成
     */
    Class<? extends ForwardDataGenerator> extendParams() default DefaultForwardGenerator.class;

}
