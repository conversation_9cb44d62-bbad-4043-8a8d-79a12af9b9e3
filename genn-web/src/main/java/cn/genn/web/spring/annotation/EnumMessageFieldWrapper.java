package cn.genn.web.spring.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface EnumMessageFieldWrapper {

    /**
     * 枚举描述的字段名
     *
     * @return
     */
    String enumDescFiledName() default "description";

    /**
     * 枚举字段后缀
     * @return
     */
    String enumDescSuffix() default "Desc";


}
