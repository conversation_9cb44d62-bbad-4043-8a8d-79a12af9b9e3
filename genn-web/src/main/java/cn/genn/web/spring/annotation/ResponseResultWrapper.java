package cn.genn.web.spring.annotation;


import cn.genn.web.spring.component.response.ResponseResultResponseBodyAdvice;

import java.lang.annotation.*;

/**
 * 使用 ResponseResultResponseBodyAdvice后，返回结果都会包装为ResponseResult。
 * 使用此注解实现某人特殊的接口，忽略包装
 * <p>
 * {@link ResponseResultResponseBodyAdvice}
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface ResponseResultWrapper {

    /**
     * 是否忽略
     *
     * @return
     */
    boolean ignore() default false;
}
