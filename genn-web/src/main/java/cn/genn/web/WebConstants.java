package cn.genn.web;

/**
 * <AUTHOR>
 */
public class WebConstants {

    /**
     * gtrace链路追踪请求参数
     */
    public static final String REQUEST_PARAM_GTRACE_ID = "gtraceid";

    /**
     * bridge请求头,代表请求的集群id,例如：pf,retail,public,platform
     */
    public static final String BRIDGE_ORIGIN_CLUSTER = "x-genn-bridge-origin-cluster";

    /**
     * bridge请求头,代表请求的服务名称
     */
    public static final String BRIDGE_ORIGIN_SERVICE = "x-genn-bridge-origin-service";

    /**
     * bridge请求头,代表目标服务名称
     */
    public static final String BRIDGE_TARGET_SERVICE = "x-genn-bridge-target-service";

    /**
     * bridge请求头,鉴权信息
     */
    public static final String BRIDGE_AUTHORIZATION = "x-genn-bridge-authorization";

    /**
     * bridge请求头,随机码
     */
    public static final String BRIDGE_NONCE = "x-genn-bridge-nonce";
}
