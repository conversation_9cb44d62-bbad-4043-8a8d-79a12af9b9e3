package cn.genn.web.http;

import lombok.Data;

@Data
public class HttpClientProperties {

    private HttpClientType type = HttpClientType.HTTP_COMPONENT;
    /**
     * 通用配置
     * 连接超时时间
     */
    private int connectTimeout = 10000;
    /**
     * 通用配置
     * 读取超时时间
     */
    private int readTimeout = 10000;

    /**
     * HttpComponent的专用配置
     * 连接池最大连接数
     */
    private int maxConnTotal = 50;
    /**
     * HttpComponent的专用配置
     * 从连接池获取连接超时时间
     */
    private int connectionRequestTimeout = 10000;

    /**
     * HttpComponent的专用配置
     * 是否缓存请求的Body
     */
    private boolean bufferRequestBody = true;

    /**
     * OkHttp3, OkHttp专用配置
     * 写超时时间
     */
    private int writeTimeout = 5000;

    /**
     * Netty4专用配置
     * 响应最大大小
     */
    private int maxResponseSize = 8192;
}