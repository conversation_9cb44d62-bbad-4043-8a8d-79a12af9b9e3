<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.genn.nova</groupId>
    <artifactId>genn-project-nova</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>genn-project</name>
    <description>genn project 全新构建,基于jdk21+springboot3.4.x</description>

    <developers>
        <developer>
            <name>mahaonan</name>
            <email>mahaonan</email>
        </developer>
    </developers>

    <modules>
        <module>genn-parent</module>
        <module>genn-core</module>
        <module>genn-web</module>
        <module>genn-security</module>
        <module>genn-database</module>
        <module>genn-monitor</module>
        <module>genn-cache</module>
        <module>genn-lock</module>
        <module>genn-job</module>
        <module>genn-bridge</module>
        <module>genn-feishu</module>
        <module>genn-graph</module>
        <module>genn-ai</module>
    </modules>


    <properties>
        <java.version>21</java.version>
        <skipTests>true</skipTests>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <maven.wagon.http.ssl.insecure>true</maven.wagon.http.ssl.insecure>
        <maven.wagon.http.ssl.allowall>true</maven.wagon.http.ssl.allowall>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>1.0.0-SNAPSHOT</revision>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>1.2.2</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    </configuration>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>nexus-zj-releases</id>
            <name>Nexus Releases Repository</name>
            <url>http://maven.genn.cn:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-zj-snapshots</id>
            <name>Nexus Snapshots Repository</name>
            <url>http://maven.genn.cn:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>nexus-zj-releases</id>
            <name>Nexus Releases Repository</name>
            <url>http://maven.genn.cn:8081/repository/maven-releases/</url>
            <releases>
                <updatePolicy>always</updatePolicy>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>nexus-zj-snapshots</id>
            <name>Nexus Snapshots Repository</name>
            <url>http://maven.genn.cn:8081/repository/maven-snapshots/</url>
            <releases>
                <updatePolicy>always</updatePolicy>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>



</project>
