package cn.genn.monitor.health;


import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("${genn.monitor.health-check.path:/health}")
public class HealthController {

    private final HealthCheckProperties healthCheckProperties;

    public HealthController(HealthCheckProperties healthCheckProperties) {
        this.healthCheckProperties = healthCheckProperties;
    }

    /**
     * 启动检查
     *
     * @return
     */
    @RequestMapping("/startup")
    public ResponseEntity<SystemHealthStatusEnum> startup() {
        return ResponseEntity.ok(SystemHealthStatusEnum.UP);
    }

    /**
     * 就绪检查
     *
     * @return
     */
    @RequestMapping("/readiness")
    public ResponseEntity<SystemHealthStatusEnum> readiness() {
        if (!healthCheckProperties.isEnableReadiness()) {
            return ResponseEntity.ok(SystemHealthStatusEnum.UP);
        }
        return HealthHolder.getStatus() == SystemHealthStatusEnum.UP ?
                ResponseEntity.ok(SystemHealthStatusEnum.UP) : ResponseEntity.status(503).body(SystemHealthStatusEnum.DOWN);

    }

    /**
     * 存活检查
     *
     * @return
     */
    @RequestMapping("/liveness")
    public ResponseEntity<SystemHealthStatusEnum> liveness() {
        return ResponseEntity.ok(SystemHealthStatusEnum.UP);
    }
}
