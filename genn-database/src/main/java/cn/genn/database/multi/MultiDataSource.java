package cn.genn.database.multi;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Slf4j
public class MultiDataSource extends AbstractRoutingDataSource {

    @Setter
    private Object primaryDataSourceKey;
    @Getter
    private final Map<String, Integer> slaveDataSourceCount = new ConcurrentHashMap<>();
    @Getter
    private final Map<String, AtomicInteger> slaveDataSourceIndex = new HashMap<>();

    @Override
    protected Object determineCurrentLookupKey() {
        String currDataSourceName = MultiDataSourceHolder.getDataSource();
        DataSourceType dataSourceType = SlaveDataSourceHolder.get();
        if (currDataSourceName != null) {
            if (Objects.equals(DataSourceType.SLAVE, dataSourceType)) {
                String salveDataSource = lookupSlaveDataSourceKey(currDataSourceName);
                if (salveDataSource == null) {
                    // 如果没有从库，使用主库
                    log.warn("no slave datasource found, use master datasource");
                    return createDatasourceKey(currDataSourceName, DataSourceType.MASTER, 0);
                }
            }
            return createDatasourceKey(currDataSourceName, DataSourceType.MASTER, 0);
        }
        if (primaryDataSourceKey == null) {
            throw new IllegalArgumentException("primaryDataSourceKey can not be null");
        }
        if (Objects.equals(DataSourceType.SLAVE, dataSourceType)) {
            currDataSourceName = lookupSlaveDataSourceKey(primaryDataSourceKey.toString().split(":")[0]);
            if (currDataSourceName == null) {
                log.warn("no slave datasource found, use master datasource");
                return primaryDataSourceKey;
            }
        }
        return primaryDataSourceKey;
    }


    private String lookupSlaveDataSourceKey(String currDataSourceName) {
        AtomicInteger index = slaveDataSourceIndex.get(currDataSourceName);
        if (index == null) {
            return null;
        }
        return createDatasourceKey(currDataSourceName, DataSourceType.SLAVE, index.getAndIncrement() % slaveDataSourceCount.get(currDataSourceName));
    }

    private String createDatasourceKey(String dataId, DataSourceType dataSourceType, int index) {
        return String.join(":", dataId, dataSourceType.name(), String.valueOf(index));
    }

}

