package cn.genn.database.multi;



/**
 * <AUTHOR>
 */
public class SlaveDataSourceHolder {

    private static final ThreadLocal<DataSourceType> MASTER_SLAVE_THREAD_LOCAL = new ThreadLocal<>();

    public static void set(DataSourceType dataSourceType) {
        MASTER_SLAVE_THREAD_LOCAL.set(dataSourceType);
    }

    public static DataSourceType get() {
        return MASTER_SLAVE_THREAD_LOCAL.get();
    }

    public static void clear() {
        MASTER_SLAVE_THREAD_LOCAL.remove();
    }
}
