package cn.genn.database.multi;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Slf4j
public class MultiDataSourceManager {

    private final MultiDataSource multiDataSource;

    public MultiDataSourceManager(MultiDataSource multiDataSource) {
        this.multiDataSource = multiDataSource;
    }

    /**
     * 添加主数据源
     * @param dataSourceName 数据源名称
     * @param masterProperties 主数据源配置, 参考HikariCP配置
     */
    public synchronized void addDataSource(String dataSourceName, Properties masterProperties) {
        String key = createDatasourceKey(dataSourceName, DataSourceType.MASTER, 0);
        Map<Object, Object> dataSources = new HashMap<>(multiDataSource.getResolvedDataSources());
        if (dataSources.containsKey(key)) {
            log.warn("datasource {} already exists", key);
            return;
        }
        DataSource datasource = createDatasource(masterProperties);
        dataSources.put(key, datasource);
        multiDataSource.setTargetDataSources(dataSources);
        multiDataSource.afterPropertiesSet();
    }

    /**
     * 移除数据源
     * @param dataSourceName
     */
    public synchronized void removeDataSource(String dataSourceName) {
        Map<Object, Object> dataSources = new HashMap<>(multiDataSource.getResolvedDataSources());
        String masterKey = createDatasourceKey(dataSourceName, DataSourceType.MASTER, 0);

        // 移除主库
        DataSource masterDataSource = (DataSource) dataSources.remove(masterKey);
        if (masterDataSource != null) {
            log.info("remove datasource {}", masterKey);
            closeDataSource(masterDataSource);
            multiDataSource.setTargetDataSources(dataSources);
            multiDataSource.afterPropertiesSet();
            return;
        }
        log.warn("datasource {} not found", masterKey);
    }

    private void closeDataSource(DataSource dataSource) {
        try {
            if (dataSource instanceof HikariDataSource) {
                ((HikariDataSource) dataSource).close();
            }
        } catch (Exception e) {
            log.error("Error closing datasource", e);
        }
    }

    private DataSource createDatasource(Properties properties) {
        HikariConfig config = new HikariConfig(properties);
        return new HikariDataSource(config);
    }

    private String createDatasourceKey(String dataId, DataSourceType dataSourceType, int index) {
        return String.join(":", dataId, dataSourceType.name(), String.valueOf(index));
    }

}
