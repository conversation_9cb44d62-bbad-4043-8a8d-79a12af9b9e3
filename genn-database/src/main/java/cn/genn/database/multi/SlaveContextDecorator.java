package cn.genn.database.multi;

import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;

/**
 * 主从数据源上下文装饰器
 *
 * <AUTHOR>
 */
public class SlaveContextDecorator extends BaseThreadDecorator {

    public SlaveContextDecorator() {
        super();
    }

    public SlaveContextDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }

    @Override
    protected Object beforeExecOnCurrThread() {
        return SlaveDataSourceHolder.get();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof DataSourceType) {
            SlaveDataSourceHolder.set((DataSourceType) object);
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        SlaveDataSourceHolder.clear();
    }
}
