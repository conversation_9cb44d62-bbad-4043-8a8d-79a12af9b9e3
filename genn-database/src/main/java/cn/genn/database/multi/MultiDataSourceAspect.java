package cn.genn.database.multi;

import cn.genn.core.utils.AnnotationUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 */
@Aspect
@Order(-1)
public class MultiDataSourceAspect {

    @Pointcut("@within(cn.genn.database.multi.MultiDB) || @annotation(cn.genn.database.multi.MultiDB)")
    public void aopPointCut() {

    }


    @Around(value = "aopPointCut()")
    public Object cacheObject(ProceedingJoinPoint joinPoint) throws Throwable {
        MultiDB multiDB = AnnotationUtils.getAnnotation(joinPoint, MultiDB.class);
        if (multiDB == null) {
            return joinPoint.proceed();
        }
        String newDataSource = multiDB.value();
        String oldDataSource = MultiDataSourceHolder.getDataSource();
        try {
            MultiDataSourceHolder.setDataSource(newDataSource);
            return joinPoint.proceed();
        } finally {
            MultiDataSourceHolder.clearDataSource();
            //这里虽然最终会设置为null,但和remove()不同,remove()会清空ThreadLocalMap中的key,而set(null)只是把value设置为null,但key还在
            MultiDataSourceHolder.setDataSource(oldDataSource);
            if (oldDataSource == null) {
                MultiDataSourceHolder.clearDataSource();
            }
        }
    }
}
