package cn.genn.database.multi;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 */
@Aspect
@Order(-1)
public class SlaveMasterDataSourceAspect {

    @Around("@annotation(slave)")
    public Object salveDb(ProceedingJoinPoint joinPoint, Slave slave) throws Throwable {
        try {
            SlaveDataSourceHolder.set(DataSourceType.SLAVE);
            return joinPoint.proceed();
        }finally {
            SlaveDataSourceHolder.clear();
        }
    }

    @Around("@annotation(slave)")
    public Object masterDb(ProceedingJoinPoint joinPoint, Slave slave) throws Throwable {
        try {
            SlaveDataSourceHolder.set(DataSourceType.MASTER);
            return joinPoint.proceed();
        }finally {
            SlaveDataSourceHolder.clear();
        }
    }


}
