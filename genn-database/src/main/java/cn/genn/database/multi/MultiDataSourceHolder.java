package cn.genn.database.multi;

/**
 * <AUTHOR>
 */
public class MultiDataSourceHolder {

    private static final ThreadLocal<String> DATASOURCE_HOLDER = new ThreadLocal<>();

    public static void setDataSource(String dataSource) {
        DATASOURCE_HOLDER.set(dataSource);
    }

    public static String getDataSource() {
        return DATASOURCE_HOLDER.get();
    }

    public static void clearDataSource() {
        DATASOURCE_HOLDER.remove();
    }
}
