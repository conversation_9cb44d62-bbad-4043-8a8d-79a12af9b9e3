package cn.genn.database.multi;

import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;

/**
 * 多数据源上下文装饰器
 *
 * <AUTHOR>
 */
public class MultiContextDecorator extends BaseThreadDecorator {

    public MultiContextDecorator() {
        super();
    }

    public MultiContextDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }

    @Override
    protected Object beforeExecOnCurrThread() {
        return MultiDataSourceHolder.getDataSource();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof String) {
            MultiDataSourceHolder.setDataSource((String) object);
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        MultiDataSourceHolder.clearDataSource();
    }
}
