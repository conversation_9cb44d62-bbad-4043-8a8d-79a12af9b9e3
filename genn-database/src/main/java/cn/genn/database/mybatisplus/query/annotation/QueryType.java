package cn.genn.database.mybatisplus.query.annotation;


import cn.genn.database.mybatisplus.query.constant.Condition;
import cn.genn.database.mybatisplus.query.constant.NestedCondition;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@Repeatable(NestedQueryType.class)
public @interface QueryType {

    /**
     * 条件
     *
     * @return
     */
    Condition condition() default Condition.EQ;

    /**
     * 字段名
     *
     * @return
     */
    String field() default "";

    /**
     * 是否忽略
     */
    boolean ignore() default false;

    /**
     * 查询的字符串增加前缀
     * @return
     */
    String prefix() default "";

    /**
     * 查询字符串增加后缀
     * @return
     */
    String suffix() default "";

    /**
     * 集合或者数组时的组合关系
     *
     * @return
     */
    NestedCondition nested() default NestedCondition.OR;
}
