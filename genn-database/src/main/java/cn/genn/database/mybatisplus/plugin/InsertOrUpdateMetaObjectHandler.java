package cn.genn.database.mybatisplus.plugin;

import cn.genn.core.context.BaseMethodContext;
import cn.genn.core.context.BaseRequestContext;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Slf4j
public class InsertOrUpdateMetaObjectHandler implements MetaObjectHandler {

    private final String defaultUserName;

    private final Long defaultUserId;

    public InsertOrUpdateMetaObjectHandler(Long defaultUserId, String defaultUserName) {
        this.defaultUserId = defaultUserId == null ? 0L : defaultUserId;
        this.defaultUserName = defaultUserName == null ? "admin" : defaultUserName;
    }


    @Override
    public void insertFill(MetaObject metaObject) {
        Long userId = getUserId();
        String username = getUserName();
        Long tenantId = getTenantId();
        this.strictInsertFill(metaObject, "createUserId", Long.class, userId);
        this.strictInsertFill(metaObject, "createUserName", String.class, username);
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateUserId", Long.class, userId);
        this.strictInsertFill(metaObject, "updateUserName", String.class, username);
        this.strictInsertFill(metaObject, "tenantId", Long.class, tenantId);
    }


    @Override
    public void updateFill(MetaObject metaObject) {
        Long userId = getUserId();
        String username = getUserName();
        this.strictUpdateFill(metaObject, "updateUserId", Long.class, userId);
        this.strictUpdateFill(metaObject, "updateUserName", String.class, username);
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }

    private Long getUserId() {
        BaseMethodContext methodContext = BaseMethodContext.get();
        if (methodContext != null && methodContext.getUserId() != null) {
            return methodContext.getUserId();
        }
        BaseRequestContext requestContext = BaseRequestContext.get();
        if (requestContext != null && requestContext.getUserId() != null) {
            return requestContext.getUserId();
        }
        return defaultUserId;
    }

    private String getUserName() {
        BaseMethodContext methodContext = BaseMethodContext.get();
        if (methodContext != null && CharSequenceUtil.isNotEmpty(methodContext.getUserName())) {
            return methodContext.getUserName();
        }
        BaseRequestContext requestContext = BaseRequestContext.get();
        if (requestContext != null && CharSequenceUtil.isNotEmpty(requestContext.getUserName())) {
            return requestContext.getUserName();
        }
        return defaultUserName;
    }

    private Long getTenantId() {
        BaseMethodContext methodContext = BaseMethodContext.get();
        if (methodContext != null && methodContext.getTenantId() != null) {
            return methodContext.getTenantId();
        }
        BaseRequestContext requestContext = BaseRequestContext.get();
        if (requestContext != null && requestContext.getTenantId() != null) {
            return requestContext.getTenantId();
        }
        return 0L;
    }


}
