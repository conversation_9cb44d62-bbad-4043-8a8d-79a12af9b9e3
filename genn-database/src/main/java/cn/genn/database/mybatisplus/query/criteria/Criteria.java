package cn.genn.database.mybatisplus.query.criteria;

import cn.genn.core.model.page.OrderBy;
import cn.genn.core.model.page.SortOrder;
import cn.genn.database.mybatisplus.query.annotation.QueryType;
import cn.genn.database.mybatisplus.query.constant.Condition;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class Criteria implements Serializable {

    private static final long serialVersionUID = 3248059906852988561L;

    /**
     * 排序字段名
     */
    @QueryType(condition = Condition.SORT)
    private String sort;

    /**
     * 顺序 desc/asc
     */
    @QueryType(condition = Condition.ORDER)
    private String order;

    /**
     *
     */
    @QueryType(condition = Condition.ORDER_BY)
    private OrderBy orderBy;

    public <T extends Criteria> T orderBy(String field, SortOrder direction) {
        if (orderBy == null) {
            orderBy = new OrderBy();
        }
        orderBy.orderBy(field, direction);
        return (T) this;
    }

    public <T extends Criteria> T orderByAsc(String field) {
        if (orderBy == null) {
            orderBy = new OrderBy();
        }
        orderBy.orderByAsc(field);
        return (T) this;
    }

    public <T extends Criteria> T orderByDesc(String field) {
        if (orderBy == null) {
            orderBy = new OrderBy();
        }
        orderBy.orderByDesc(field);
        return (T) this;
    }
}
