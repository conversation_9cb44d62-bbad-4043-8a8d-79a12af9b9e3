package cn.genn.database.mybatisplus.typehandler;

import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.databind.JavaType;

/**
 * 使用List时，继承此类实现泛型List处理
 *
 * <AUTHOR>
 */
public abstract class JacksonNestedListTypeHandler extends JacksonTypeHandler {

    public JacksonNestedListTypeHandler(Class<?> classType) {
        super(JsonUtils.constructNestListType(classType));
    }

    public JacksonNestedListTypeHandler(JavaType javaType) {
        super(JsonUtils.constructNestListType(javaType));
    }
}