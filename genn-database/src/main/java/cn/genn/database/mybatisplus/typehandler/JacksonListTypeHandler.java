package cn.genn.database.mybatisplus.typehandler;

import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.databind.JavaType;

/**
 * 使用List时，继承此类实现泛型List处理
 *
 * <AUTHOR>
 */
public abstract class JacksonListTypeHandler extends JacksonTypeHandler {

    public JacksonListTypeHandler(Class<?> classType) {
        super(JsonUtils.constructListType(classType));
    }

    public JacksonListTypeHandler(JavaType javaType) {
        super(JsonUtils.constructListType(javaType));
    }
}