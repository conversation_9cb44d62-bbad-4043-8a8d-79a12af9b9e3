package cn.genn.database.mybatisplus.plugin.tenant;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.core.utils.thread.BaseThreadDecorator;
import cn.genn.core.utils.thread.ThreadDecorator;

/**
 * 租户上下文装饰器
 *
 * <AUTHOR>
 */
public class TenantContextDecorator extends BaseThreadDecorator {

    public TenantContextDecorator() {
        super();
    }

    public TenantContextDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }

    @Override
    protected Object beforeExecOnCurrThread() {
        return TenantContextHolder.getTenantContext();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof TenantContext) {
            TenantContextHolder.setTenantContext(JsonUtils.clone(object, TenantContext.class));
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        TenantContextHolder.clearTenantContext();
    }
}
