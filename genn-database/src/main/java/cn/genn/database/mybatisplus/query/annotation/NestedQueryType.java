package cn.genn.database.mybatisplus.query.annotation;


import cn.genn.database.mybatisplus.query.constant.NestedCondition;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface NestedQueryType {

    /**
     * 多条件
     * @return
     */
    QueryType[] value() default {};

    /**
     * 条件组合关系
     *
     * @return
     */
    NestedCondition condition() default NestedCondition.OR;
}
