package cn.genn.database.mybatisplus.typehandler;

import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import lombok.extern.slf4j.Slf4j;

/**
 * Json通用TypeHandler
 * 携带类型信息
 *
 * <AUTHOR>
 **/
@Slf4j
public class JacksonEnableTypingHandler extends AbstractJsonTypeHandler {

    protected JavaType javaType;

    public JacksonEnableTypingHandler(Class<?> classType) {
        this.javaType = JsonUtils.getEnableTypingMapper().getTypeFactory().constructType(classType);
    }

    public JacksonEnableTypingHandler(JavaType javaType) {
        this.javaType = javaType;
    }

    public JacksonEnableTypingHandler(TypeReference<?> typeReference) {
        this.javaType = JsonUtils.getEnableTypingMapper().getTypeFactory().constructType(typeReference);
    }

    @Override
    public Object parse(String json) {
        try {
            return JsonUtils.getEnableTypingMapper().readValue(json, javaType);
        } catch (JsonProcessingException e) {
            log.error("Json string to object failed!", e);
        }
        return null;
    }

    @Override
    public String toJson(Object obj) {
        try {
            return JsonUtils.getEnableTypingMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Object to json string failed!", e);
        }
        return "";
    }
}