package cn.genn.database.mybatisplus.plugin.tenant;

import cn.genn.core.context.BaseMethodContext;
import cn.genn.core.context.BaseRequestContext;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.NullValue;

import java.util.List;
import java.util.Map;

/**
 * 多租户管理插件
 *
 * <AUTHOR>
 */
public class GennTenantLineHandler implements TenantLineHandler {

    private final List<String> ignoreTables;

    public GennTenantLineHandler(List<String> ignoreTables) {
        this.ignoreTables = ignoreTables;
    }

    @Override
    public Expression getTenantId() {
        Long tenantId = null;
        BaseMethodContext methodContext = BaseMethodContext.get();
        if (methodContext != null && methodContext.getTenantId() != null) {
            tenantId = methodContext.getTenantId();
        }
        BaseRequestContext requestContext = BaseRequestContext.get();
        if (requestContext != null && requestContext.getTenantId() != null) {
            tenantId = requestContext.getTenantId();
        }
        if (tenantId == null) {
            return new NullValue();
        }
        return new LongValue(tenantId);
    }

    @Override
    public boolean ignoreTable(String tableName) {
        boolean ignored = ignoreTables != null && ignoreTables.contains(tableName);
        Map<String, CustomIgnoreStrategy> ignoreStrategyMap = SpringUtil.getBeansOfType(CustomIgnoreStrategy.class);
        if (MapUtil.isEmpty(ignoreStrategyMap)) {
            return ignored;
        }
        return ignored || ignoreStrategyMap.values().stream().anyMatch(customIgnoreStrategy -> customIgnoreStrategy.ignoreTable(tableName));
    }

    public interface CustomIgnoreStrategy {
        boolean ignoreTable(String tableName);
    }
}
