package cn.genn.database.mybatisplus.typehandler;

import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;

/**
 * Json通用TypeHandler
 * <p>
 * 注意:不支持泛型例如List，Set等，因为类型擦除问题，反序列化会丢失类型，可以使用数组
 *
 * <AUTHOR>
 **/
public class JacksonTypeHandler extends AbstractJsonTypeHandler {

    protected JavaType javaType;

    public JacksonTypeHandler(Class<?> classType) {
        this.javaType = JsonUtils.constructJavaType(classType);
    }

    public JacksonTypeHandler(JavaType javaType) {
        this.javaType = javaType;
    }

    public JacksonTypeHandler(TypeReference<?> typeReference) {
        this.javaType = JsonUtils.constructJavaType(typeReference);
    }

    @Override
    public Object parse(String json) {
        return JsonUtils.parse(json, javaType, true);
    }

    @Override
    public String toJson(Object obj) {
        return JsonUtils.toJsonNotNull(obj);
    }
}