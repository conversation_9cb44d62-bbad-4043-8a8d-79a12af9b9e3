package cn.genn.database.mybatisplus.query.constant;

/**
 * <AUTHOR>
 */
public enum Condition {
    /**
     * 等于 =
     * 例: eq("name", "老王")--->name = '老王'
     */
    EQ,
    /**
     * 不等于 <>
     * 例: ne("name", "老王")--->name <> '老王'
     */
    NE,
    /**
     * 小于等于 <=
     * 例: le("age", 18)--->age <= 18
     */
    LE,
    /**
     * 小于 <
     * 例: lt("age", 18)--->age < 18
     */
    LT,
    /**
     * 大于等于 >=
     * 例: ge("age", 18)--->age >= 18
     */
    GE,
    /**
     * 大于 >
     * 例: gt("age", 18)--->age > 18
     */
    GT,
    /**
     * LIKE '%值%'
     * like("name", "王")--->name like '%王%'
     */
    LIKE,
    /**
     * LIKE '值%'
     * likeRight("name", "王")--->name like '王%'
     */
    LIKE_RIGHT,
    /**
     * LIKE '%值'
     * likeLeft("name", "王")--->name like '%王'
     */
    LIKE_LEFT,
    /**
     * NOT LIKE '%值%'
     * notLike("name", "王")--->name not like '%王%'
     */
    NOT_LIKE,
    /**
     * 字段 IN
     * 例: in("age", 1, 2, 3)--->age in (1,2,3)
     */
    IN,
    /**
     * 字段 NOT IN
     * 例: notIn("age",{1,2,3})--->age not in (1,2,3)
     */
    NOT_IN,
    /**
     * BETWEEN 值1 AND 值2
     * 例: between("age", 18, 30)--->age between 18 and 30
     */
    BETWEEN,
    /**
     * NOT BETWEEN 值1 AND 值2
     * 例: notBetween("age", 18, 30)--->age not between 18 and 30
     */
    NOT_BETWEEN,
    /**
     * 字段 IS NULL
     * 例: isNull("name")--->name is null
     */
    IS_NULL,
    /**
     * 字段 IS NOT NULL
     * 例: isNotNull("name")--->name is not null
     */
    IS_NOT_NULL,
    /**
     * 排序字段
     */
    SORT,
    /**
     * asc,desc
     */
    ORDER,
    /**
     * 排序，支持多级排序，配置此字段后，sort和order无效
     *
     * 例如：column1 asc,column2 desc,column3
     */
    ORDER_BY
    ;
}
