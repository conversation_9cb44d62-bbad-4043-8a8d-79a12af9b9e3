package cn.genn.database.mybatisplus.query;

import cn.genn.core.model.page.OrderBy;
import cn.genn.core.model.page.SortOrder;
import cn.genn.database.mybatisplus.query.annotation.NestedQueryType;
import cn.genn.database.mybatisplus.query.annotation.QueryType;
import cn.genn.database.mybatisplus.query.constant.Condition;
import cn.genn.database.mybatisplus.query.constant.NestedCondition;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.core.conditions.interfaces.Compare;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class QueryWrapperUtil {

    public static final String ORDER_DESC = "desc";

    public static final String SERIAL_VERSION_UID = "serialVersionUID";

    private static Map<Condition, QueryFun> queryFunMap;

    static {
        initQueryFunMap();
    }

    public static <T> QueryWrapper<T> build(Object t) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        exec(t, wrapper);
        return wrapper;
    }

    public static <T> QueryWrapper<T> build(Object t, QueryWrapper<T> wrapper) {
        exec(t, wrapper);
        return wrapper;
    }

    private static <T> void exec(Object t, QueryWrapper<T> wrapper) {
        if (t == null) {
            return;
        }
        Field[] fields = ReflectUtil.getFields(t.getClass());
        try {
            String sort = null;
            String order = null;
            OrderBy orderBy = null;
            for (Field field : fields) {
                field.setAccessible(true);
                Object val = field.get(t);
                if (val == null || val.toString().trim().length() == 0) {
                    continue;
                }
                val = getEnumVal(val);
                String column = field.getName();
                if (SERIAL_VERSION_UID.equals(column)) {
                    continue;
                }
                column = StringUtils.camelToUnderline(column);
                NestedQueryType nestedQueryType = field.getAnnotation(NestedQueryType.class);
                if (nestedQueryType != null) {
                    buildNestedQuery(wrapper, nestedQueryType, column, val);
                } else {
                    QueryType queryType = field.getAnnotation(QueryType.class);
                    if (queryType == null) {
                        continue;
                    }
                    if (queryType.ignore()) {
                        continue;
                    }
                    if (queryType.condition().equals(Condition.SORT)) {
                        sort = val.toString();
                        continue;
                    }
                    if (queryType.condition().equals(Condition.ORDER)) {
                        order = val.toString();
                        continue;
                    }
                    if (queryType.condition().equals(Condition.ORDER_BY)) {
                        orderBy = getOrderBy(val);
                        continue;
                    }
                    buildQuery(wrapper, column, val, queryType);
                }
            }
            buildSortOrder(wrapper, sort, order, orderBy);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    private static OrderBy getOrderBy(Object val) {
        if (val == null) {
            return null;
        }
        if (val instanceof OrderBy) {
            return (OrderBy) val;
        }
        if (val instanceof String) {
            return buildOrderBy((String) val);
        }
        throw new IllegalArgumentException("invalid order by column type");
    }

    private static OrderBy buildOrderBy(String val) {
        if (StrUtil.isEmpty(val)) {
            return null;
        }
        OrderBy orderBy = new OrderBy();
        String[] arr;
        String column;
        for (String str : val.split(",")) {
            str = str.trim();
            if (str.contains(" ")) {
                arr = str.split("\\s+");
                column = StringUtils.camelToUnderline(arr[0].trim());
                if (ORDER_DESC.equalsIgnoreCase(arr[1].trim())) {
                    orderBy.orderByDesc(column);
                } else {
                    orderBy.orderByAsc(column);
                }
            } else {
                column = StringUtils.camelToUnderline(str);
                orderBy.orderByAsc(column);
            }
        }
        return orderBy;
    }

    private static <T> void buildQuery(QueryWrapper<T> wrapper, String column, Object val, QueryType queryType) {
        val = preHandler(val, queryType);
        queryFunMap.get(queryType.condition()).exec(wrapper,
                StrUtil.isEmpty(queryType.field()) ? column : queryType.field(),
                val,
                queryType.nested());
    }

    private static <T> void buildNestedQuery(QueryWrapper<T> wrapper, NestedQueryType nestedQueryType, String column, Object val) {
        wrapper.and(nestWrapper -> {
            for (QueryType queryType : nestedQueryType.value()) {
                if (queryType.ignore()) {
                    continue;
                }
                if (NestedCondition.AND.equals(nestedQueryType.condition())) {
                    nestWrapper.and(w1 -> buildQuery(w1, column, val, queryType));
                } else if (NestedCondition.OR.equals(nestedQueryType.condition())) {
                    nestWrapper.or(w1 -> buildQuery(w1, column, val, queryType));
                }
            }
        });
    }

    private static <T> void buildSortOrder(QueryWrapper<T> wrapper, String sort, String order, OrderBy orderBy) {
        if (orderBy == null && StrUtil.isNotEmpty(sort)) {
            orderBy = new OrderBy().orderBy(sort, ORDER_DESC.equalsIgnoreCase(order) ? SortOrder.DESC : SortOrder.ASC);
        }
        buildSortOrder(wrapper, orderBy);
    }

    private static <T> void buildSortOrder(QueryWrapper<T> wrapper, OrderBy orderBy) {
        if (orderBy != null) {
            String column;
            for (Map.Entry<String, SortOrder> entry : orderBy.getOrderBy().entrySet()) {
                column = StringUtils.camelToUnderline(entry.getKey());
                if (entry.getValue() == SortOrder.DESC) {
                    wrapper.orderByDesc(column);
                } else {
                    wrapper.orderByAsc(column);
                }
            }
        }
    }

    private static Object escape(Object val, Condition condition) {
        if (!isLikeCondition(condition)) {
            return val;
        }
        String value;
        if (val instanceof String) {
            value = (String) val;
        } else {
            value = val.toString();
        }
        return value.trim().replaceAll("_", "\\\\_").replaceAll("%", "\\\\%");
    }

    private static Object preHandler(Object val, QueryType queryType) {
        if (val == null) {
            return null;
        }
        if (val instanceof String) {
            if (StrUtil.isNotEmpty(queryType.prefix())) {
                val = queryType.prefix() + val;
            }
            if (StrUtil.isNotEmpty(queryType.suffix())) {
                val = val + queryType.suffix();
            }
            return val;
        } else if (val instanceof Collection) {
            return ((Collection) val).stream().map(x -> preHandler(x, queryType)).collect(Collectors.toList());
        } else if (val.getClass().isArray()) {
            return Arrays.stream((Object[]) val).map(x -> preHandler(x, queryType)).toArray();
        } else {
            return val;
        }
    }

    private static boolean isLikeCondition(Condition condition) {
        return condition.equals(Condition.LIKE)
                || condition.equals(Condition.LIKE_LEFT)
                || condition.equals(Condition.LIKE_RIGHT)
                || condition.equals(Condition.NOT_LIKE);
    }

    private static Object getEnumVal(Object val) {
        if (val == null) {
            return null;
        }
        if (val instanceof Enum) {
            try {
                Field valueField = getAnnotationField(val.getClass(), EnumValue.class).orElse(null);
                if (valueField != null) {
                    return ReflectUtil.getFieldValue(val, valueField);
                }
            } catch (Exception e) {
                log.error("parse enum value failed", e);
            }
            return val;
        } else if (val instanceof Collection) {
            return ((Collection) val).stream().map(QueryWrapperUtil::getEnumVal).collect(Collectors.toList());
        } else if (val.getClass().isArray()) {
            return Arrays.stream((Object[]) val).map(QueryWrapperUtil::getEnumVal).toArray();
        } else {
            return val;
        }
    }

    private static void initQueryFunMap() {
        queryFunMap = new HashMap<>();
        queryFunMap.put(Condition.EQ, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                wrapper.eq(field, value);
            }
        });
        queryFunMap.put(Condition.NE, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                wrapper.ne(field, value);
            }
        });
        queryFunMap.put(Condition.GT, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                wrapper.gt(field, value);
            }
        });
        queryFunMap.put(Condition.GE, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                wrapper.ge(field, value);
            }
        });
        queryFunMap.put(Condition.LT, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                wrapper.lt(field, value);
            }
        });
        queryFunMap.put(Condition.LE, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                wrapper.le(field, value);
            }
        });
        queryFunMap.put(Condition.IS_NULL, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                wrapper.isNull(field);
            }
        });
        queryFunMap.put(Condition.IS_NOT_NULL, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                wrapper.isNotNull(field);
            }
        });
        queryFunMap.put(Condition.BETWEEN, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                if (value instanceof Collection) {
                    Collection v = (Collection) value;
                    if (v.size() < 2) {
                        throw new IllegalArgumentException("查询数据不足");
                    }
                    List list = new ArrayList(v);
                    wrapper.between(field, list.get(0), list.get(1));
                } else if (value instanceof Object[]) {
                    Object[] v = (Object[]) value;
                    if (v.length < 2) {
                        throw new IllegalArgumentException("查询数据不足");
                    }
                    wrapper.between(field, v[0], v[1]);
                } else {
                    throw new IllegalStateException("区间查询只能标记在集合或者数组类型上");
                }
            }
        });
        queryFunMap.put(Condition.NOT_BETWEEN, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                if (value instanceof Collection) {
                    Collection v = (Collection) value;
                    if (v.size() < 2) {
                        throw new IllegalArgumentException("查询数据不足");
                    }
                    List list = new ArrayList(v);
                    wrapper.between(field, list.get(0), list.get(1));
                } else if (value instanceof Object[]) {
                    Object[] v = (Object[]) value;
                    if (v.length < 2) {
                        throw new IllegalArgumentException("查询数据不足");
                    }
                    wrapper.between(field, v[0], v[1]);
                } else {
                    throw new IllegalStateException("区间查询只能标记在集合或者数组类型上");
                }
            }
        });
        queryFunMap.put(Condition.LIKE, buildNestedQueryFun(Condition.LIKE, Compare::like));
        queryFunMap.put(Condition.LIKE_RIGHT, buildNestedQueryFun(Condition.LIKE_RIGHT, Compare::likeRight));
        queryFunMap.put(Condition.LIKE_LEFT, buildNestedQueryFun(Condition.LIKE_LEFT, Compare::likeLeft));
        queryFunMap.put(Condition.NOT_LIKE, buildNestedQueryFun(Condition.NOT_LIKE, Compare::notLike));
        queryFunMap.put(Condition.IN, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                if (value instanceof Collection) {
                    wrapper.in(field, (Collection<?>) value);
                } else if (value instanceof Object[]) {
                    wrapper.in(field, (Object[]) value);
                } else {
                    wrapper.in(field, value.toString());
                }
            }
        });
        queryFunMap.put(Condition.NOT_IN, new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                if (value instanceof Collection) {
                    wrapper.notIn(field, (Collection<?>) value);
                } else if (value instanceof Object[]) {
                    wrapper.notIn(field, (Object[]) value);
                } else {
                    wrapper.notIn(field, value.toString());
                }
            }
        });
    }

    private static QueryFun buildNestedQueryFun(Condition condition, CompareFun func) {
        return new QueryFun() {
            @Override
            public <T> void exec(QueryWrapper<T> wrapper, String field, Object value, NestedCondition nested) {
                if (value instanceof Collection) {
                    wrapper.and(nestWrapper -> {
                        for (Object v : (Collection) value) {
                            if (v == null) {
                                continue;
                            }
                            if (NestedCondition.AND.equals(nested)) {
                                nestWrapper.and(w1 -> func.exec(w1, field, escape(v, condition)));
                            } else if (NestedCondition.OR.equals(nested)) {
                                nestWrapper.or(w1 -> func.exec(w1, field, escape(v, condition)));
                            }
                        }
                    });
                } else if (value instanceof Object[]) {
                    wrapper.and(nestWrapper -> {
                        for (Object v : (Object[]) value) {
                            if (v == null) {
                                continue;
                            }
                            if (NestedCondition.AND.equals(nested)) {
                                nestWrapper.and(w1 -> func.exec(w1, field, escape(v, condition)));
                            } else if (NestedCondition.OR.equals(nested)) {
                                nestWrapper.or(w1 -> func.exec(w1, field, escape(v, condition)));
                            }
                        }
                    });
                } else {
                    func.exec(wrapper, field, escape(value, condition));
                }
            }
        };
    }

    private static <T extends Annotation> Optional<Field> getAnnotationField(Class<?> clazz, Class<T> annotationType) {
        return Arrays.stream(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(annotationType))
                .findFirst();
    }

}
