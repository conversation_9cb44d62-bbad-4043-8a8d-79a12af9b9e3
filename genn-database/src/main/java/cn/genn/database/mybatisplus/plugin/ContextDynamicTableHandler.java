package cn.genn.database.mybatisplus.plugin;

import cn.genn.core.context.BaseMethodContext;
import cn.genn.core.context.BaseRequestContext;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;

/**
 * 动态表名处理器
 * 从上下文中获取动态表名,注意多线程使用问题
 *
 * <AUTHOR>
 */
public class ContextDynamicTableHandler implements TableNameHandler {

    public static final String DYNAMIC_TABLE_NAME = "dynamicTableName";

    @Override
    public String dynamicTableName(String sql, String tableName) {
        BaseMethodContext methodContext = BaseMethodContext.get();
        if (methodContext != null && methodContext.getAttachment(DYNAMIC_TABLE_NAME) != null) {
            return (String) methodContext.getAttachment(DYNAMIC_TABLE_NAME);
        }
        BaseRequestContext requestContext = BaseRequestContext.get();
        if (requestContext == null) {
            return tableName;
        }
        String dynamicTableName = BaseRequestContext.getAttachment(DYNAMIC_TABLE_NAME, String.class);
        if (dynamicTableName == null) {
            return tableName;
        }
        return dynamicTableName;
    }
}
