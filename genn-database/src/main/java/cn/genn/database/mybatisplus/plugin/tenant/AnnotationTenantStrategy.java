package cn.genn.database.mybatisplus.plugin.tenant;

import cn.genn.core.context.BaseRequestContext;

/**
 * <AUTHOR>
 */
public class AnnotationTenantStrategy implements GennTenantLineHandler.CustomIgnoreStrategy {

    /**
     * 根据当前租户上下文和租户ID确定是否应忽略给定的表。
     */
    @Override
    public boolean ignoreTable(String tableName) {
        TenantContext tenantContext = TenantContextHolder.getTenantContext();
        if (tenantContext == null) {
            return false;
        }
        boolean force = tenantContext.isForce();
        if (force) {
            return true;
        }
        BaseRequestContext requestContext = BaseRequestContext.get();
        if (requestContext == null) {
            return true;
        }
        Long tenantId = requestContext.getTenantId();
        return tenantId == null;
    }
}
