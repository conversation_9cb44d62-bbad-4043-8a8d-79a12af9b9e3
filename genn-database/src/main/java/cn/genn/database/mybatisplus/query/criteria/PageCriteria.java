package cn.genn.database.mybatisplus.query.criteria;

import cn.genn.database.mybatisplus.query.annotation.QueryType;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PageCriteria extends Criteria {
    private static final long serialVersionUID = 892297356344878958L;

    @QueryType(ignore = true)
    private int pageNo = 1;

    @QueryType(ignore = true)
    private int pageSize = 10;

    public <T extends PageCriteria> T page(int pageNo, int pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        return (T) this;
    }
}
