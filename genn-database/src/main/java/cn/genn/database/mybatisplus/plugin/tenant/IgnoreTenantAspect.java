package cn.genn.database.mybatisplus.plugin.tenant;

import cn.genn.core.utils.AnnotationUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * <AUTHOR>
 */
@Aspect
public class IgnoreTenantAspect {

    @Pointcut("@within(cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant) || @annotation(cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant)")
    public void aopPointCut() {

    }

    @Around(value = "aopPointCut()")
    public Object cacheObject(ProceedingJoinPoint joinPoint) throws Throwable {
        IgnoreTenant ignoreTenant = AnnotationUtils.getAnnotation(joinPoint, IgnoreTenant.class);
        if (ignoreTenant == null) {
            return joinPoint.proceed();
        }
        boolean force = ignoreTenant.force();
        TenantContext tenantContext = new TenantContext();
        tenantContext.setForce(force);
        TenantContextHolder.setTenantContext(tenantContext);
        try {
            return joinPoint.proceed();
        } finally {
            TenantContextHolder.clearTenantContext();
        }
    }
}
