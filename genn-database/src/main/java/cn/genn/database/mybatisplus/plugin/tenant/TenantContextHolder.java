package cn.genn.database.mybatisplus.plugin.tenant;

/**
 * <AUTHOR>
 */
public class TenantContextHolder {

    private static final ThreadLocal<TenantContext> TENANT_CONTEXT_THREAD_LOCAL = new ThreadLocal<>();

    public static void setTenantContext(TenantContext tenantContext) {
        TENANT_CONTEXT_THREAD_LOCAL.set(tenantContext);
    }

    public static TenantContext getTenantContext() {
        return TENANT_CONTEXT_THREAD_LOCAL.get();
    }

    public static void clearTenantContext() {
        TENANT_CONTEXT_THREAD_LOCAL.remove();
    }

}
