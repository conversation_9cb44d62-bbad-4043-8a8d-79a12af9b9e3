package cn.genn.job.xxl.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

@ComponentScan("cn.genn.job.xxl")
@MapperScan("cn.genn.job.xxl.repository.mapper")
public class JobConfiguration {

    /**
     * 租户忽略策略
     * @return
     */
    @Bean
    public JobTenantIgnoreStrategy gridTenantIgnoreStrategy() {
        return new JobTenantIgnoreStrategy();
    }
}
