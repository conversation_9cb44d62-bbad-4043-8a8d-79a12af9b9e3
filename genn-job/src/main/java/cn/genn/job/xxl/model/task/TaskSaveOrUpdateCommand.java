package cn.genn.job.xxl.model.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TaskSaveOrUpdateCommand {

    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "任务类型标记")
    private String bizKey;

    @Schema(description = "固定频率")
    private JobTaskPlan plan;

    @Schema(description = "任务内容")
    private JobTaskContext content;

    @Schema(description = "任务是否可以重叠执行,默认false:不可以")
    private Boolean overlap = false;

    @Schema(description = "执行优先级,0-99数字越小,优先级越高")
    private Integer priority;

    @Schema(description = "任务状态,running自动启用,stop暂停")
    private TaskStatusEnum status;

}
