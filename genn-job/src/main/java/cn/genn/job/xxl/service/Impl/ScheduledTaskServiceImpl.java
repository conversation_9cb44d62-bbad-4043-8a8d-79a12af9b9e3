package cn.genn.job.xxl.service.Impl;

import cn.genn.core.exception.CheckException;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.utils.CronUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.exception.JobErrorCode;
import cn.genn.job.xxl.model.XxlJobScheduleTypeEnum;
import cn.genn.job.xxl.model.task.*;
import cn.genn.job.xxl.repository.mapper.JobScheduledTasksLogMapper;
import cn.genn.job.xxl.repository.mapper.JobScheduledTasksMapper;
import cn.genn.job.xxl.repository.po.JobScheduledTasksLogPO;
import cn.genn.job.xxl.repository.po.JobScheduledTasksPO;
import cn.genn.job.xxl.service.ScheduledTaskService;
import cn.genn.job.xxl.service.TaskExecutorService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ScheduledTaskServiceImpl implements ScheduledTaskService {

    @Resource
    private JobScheduledTasksMapper jobScheduledTasksMapper;
    @Resource
    private JobScheduledTasksLogMapper jobScheduledTasksLogMapper;
    @Resource
    private TaskExecutorService taskExecutorService;

    @Override
    public void saveOrUpdate(TaskSaveOrUpdateCommand command) {
        JobScheduledTasksPO oldPO = checkSaveOrUpdate(command);
        JobScheduledTasksPO po = arrangeTaskPO(command, oldPO);
        if (TaskStatusEnum.RUNNING.equals(po.getStatus())) {
            LocalDateTime localDateTime = scheduleFirstNextExecution(po, LocalDateTime.now());
            po.setNextTime(localDateTime);
        } else {
            po.setNextTime(null);
        }
        if(ObjUtil.isNotNull(command.getPlan()) && ObjUtil.isNull(command.getPlan().getUnit())){
            po.setUnit(TimeUnit.SECONDS);
        }
        if (ObjUtil.isNotNull(command.getId())) {
            jobScheduledTasksMapper.updateById(po);
        } else {
            jobScheduledTasksMapper.insert(po);
        }

    }

    @Override
    public void delete(IdCommand command) {
        jobScheduledTasksMapper.deleteById(command.getId());
    }

    @Override
    public void updateStatus(UpdateStatusCommand command) {
        JobScheduledTasksPO jobScheduledTasksPO = jobScheduledTasksMapper.selectById(command.getId());
        if (ObjUtil.isNull(jobScheduledTasksPO)) {
            throw new CheckException(JobErrorCode.SCHEDULED_TASK_NOT_FOUND, command.getId());
        }
        LocalDateTime nextTime = null;
        if (TaskStatusEnum.RUNNING.equals(command.getStatus())) {
            nextTime = scheduleFirstNextExecution(jobScheduledTasksPO, LocalDateTime.now());
        }
        jobScheduledTasksMapper.updateStatusById(command.getId(), command.getStatus(),nextTime);
    }

    @Override
    public List<JobScheduledTasksDTO> query(TaskQuery query) {
        return taskPO2DTO(jobScheduledTasksMapper.query(query));
    }

    public void execute() {
        LocalDateTime now = LocalDateTime.now();
        List<JobScheduledTasksPO> runningTasks = jobScheduledTasksMapper.getRunningTask();
        if (CollUtil.isEmpty(runningTasks)) {
            return;
        }
        for (JobScheduledTasksPO task : runningTasks) {
            if (ObjUtil.isNull(task.getNextTime()) || now.isBefore(task.getNextTime())) {
                continue;
            }
            if (ObjUtil.isNotNull(task.getStopTime()) && now.isAfter(task.getStopTime())) {
                jobScheduledTasksMapper.updateStatusById(task.getId(), TaskStatusEnum.FINISH,null);
                continue;
            }
            if(!task.getOverlap()){
                JobScheduledTasksLogPO lastLogPO = jobScheduledTasksLogMapper.selectLastByTaskId(task.getId());
                if(ObjUtil.isNotNull(lastLogPO) && lastLogPO.getStatus().equals(TaskExecuteStatusEnum.EXECUTING) ){
                    continue;
                }
            }
            if (hasReachedExecutionLimit(task)) {
                jobScheduledTasksMapper.updateStatusById(task.getId(), TaskStatusEnum.FINISH,null);
            } else {
                LocalDateTime localDateTime = scheduleNextExecution(task, now);
                jobScheduledTasksMapper.updateNextTimeById(task.getId(), localDateTime);
            }
            taskExecutorService.executeTask(task);

        }
    }

    private Boolean hasReachedExecutionLimit(JobScheduledTasksPO task) {
        if (ObjUtil.isNull(task.getRepeatCount()) || task.getRepeatCount() == 0L) {
            return false;
        }
        return jobScheduledTasksLogMapper.selectCount(task.getId()) + 1 >= task.getRepeatCount();
    }

    private LocalDateTime scheduleFirstNextExecution(JobScheduledTasksPO task, LocalDateTime now) {
        if (task.getType().equals(XxlJobScheduleTypeEnum.CRON)) {
            return CronUtils.nextExecutionTime(task.getCron(), now);
        } else {
            LocalDateTime nextTime = null;
            if (now.isBefore(task.getStartTime())) {
                nextTime = task.getStartTime();
            } else {
                nextTime = TimeUnit.nextTime(task.getStartTime(), task.getInterval(), task.getUnit());
            }
            return (task.getStopTime() == null || !nextTime.isAfter(task.getStopTime())) ? nextTime : null;
        }
    }

    private LocalDateTime scheduleNextExecution(JobScheduledTasksPO task, LocalDateTime now) {
        if (task.getType().equals(XxlJobScheduleTypeEnum.CRON)) {
            return CronUtils.nextExecutionTime(task.getCron(), now);
        } else {
            LocalDateTime nextTime = TimeUnit.nextTime(task.getNextTime(), task.getInterval(), task.getUnit());
            nextTime = now.isBefore(nextTime) ? nextTime : now;
            return (task.getStopTime() == null || !nextTime.isAfter(task.getStopTime())) ? nextTime : null;
        }
    }

    private JobScheduledTasksPO checkSaveOrUpdate(TaskSaveOrUpdateCommand command) {
        if (ObjUtil.isNotNull(command.getId())) {
            JobScheduledTasksPO jobScheduledTasksPO = jobScheduledTasksMapper.selectById(command.getId());
            if (ObjUtil.isNull(jobScheduledTasksPO)) {
                throw new CheckException(JobErrorCode.SCHEDULED_TASK_NOT_FOUND, command.getId());
            }
            if (TaskStatusEnum.FINISH.equals(jobScheduledTasksPO.getStatus())) {
                throw new CheckException(JobErrorCode.TASK_STOP_UPDATE);
            }
            return jobScheduledTasksPO;
        } else {
            if (StrUtil.isEmpty(command.getName())) {
                throw new CheckException(JobErrorCode.TASK_NAME_NOT_NULL);
            }
            if (ObjUtil.isNull(command.getPlan())) {
                throw new CheckException(JobErrorCode.TASK_PLAN_NOT_NULL);
            }
            JobTaskPlan plan = command.getPlan();
            if (ObjUtil.isNull(plan.getType())) {
                throw new CheckException(JobErrorCode.TASK_TYPE_NOT_NULL);
            }
            if (XxlJobScheduleTypeEnum.FIX_RATE.equals(plan.getType()) && (plan.getInterval() == 0L
                    || plan.getStartTime() == null)) {
                throw new CheckException(JobErrorCode.FIX_RATE_ERROR);
            }
            if (XxlJobScheduleTypeEnum.CRON.equals(command.getPlan().getType())) {
                if (StrUtil.isEmpty(plan.getCron())) {
                    throw new CheckException(JobErrorCode.CRON_NOT_NULL);
                }
                if (!CronUtils.isValidCronExpression(plan.getCron())) {
                    throw new CheckException(JobErrorCode.CRON_FORMAT_ERROR);
                }
            }
            if (ObjUtil.isEmpty(command.getContent())) {
                throw new CheckException(JobErrorCode.TASK_CONTEXT_NOT_EXIST);
            }
            if (!JobTaskContext.checkAdd(command.getContent())) {
                throw new CheckException(JobErrorCode.TASK_CONTEXT_ERROR);
            }
            if (ObjUtil.isNotNull(command.getPriority()) && command.getPriority() < 0 || command.getPriority() > 99) {
                throw new CheckException(JobErrorCode.PRIORITY_RANGE_ERROR);
            }
        }
        if (ObjUtil.isNotNull(command) && ObjUtil.isNotNull(command.getContent()) && (command.getContent().getReqTimeout() < 0 || command.getContent().getReqTimeout() > 60 * 60)) {
            throw new CheckException(JobErrorCode.REQ_TIME_OUT_ERROR);
        }
        return null;
    }

    private JobScheduledTasksPO arrangeTaskPO(TaskSaveOrUpdateCommand command, JobScheduledTasksPO po) {
        if (ObjUtil.isNull(po)) {
            po = new JobScheduledTasksPO();
        }
        po.setId(merge(command.getId(), po.getId(), ObjUtil::isEmpty));
        po.setName(merge(command.getName(), po.getName(), StrUtil::isBlank));
        po.setContent(merge(JsonUtils.toJson(command.getContent()), po.getContent(), ObjUtil::isEmpty));
        po.setPriority(merge(command.getPriority(), po.getPriority(), ObjUtil::isEmpty));
        po.setStatus(merge(command.getStatus(), po.getStatus(), ObjUtil::isEmpty));
        po.setBizKey(merge(command.getBizKey(), po.getBizKey(), StrUtil::isBlank));
        po.setOverlap(merge(command.getOverlap(), po.getOverlap(), ObjUtil::isEmpty));

        if (ObjUtil.isNotNull(command.getPlan())) {
            JobTaskPlan plan = command.getPlan();
            po.setType(merge(plan.getType(), po.getType(), ObjUtil::isEmpty));
            po.setCron(merge(plan.getCron(), po.getCron(), StrUtil::isBlank));
            po.setStartTime(merge(plan.getStartTime(), po.getStartTime(), ObjUtil::isEmpty));
            po.setInterval(merge(plan.getInterval(), po.getInterval(), ObjUtil::isEmpty));
            po.setUnit(merge(plan.getUnit(), po.getUnit(), ObjUtil::isEmpty));
            po.setRepeatCount(merge(plan.getRepeatCount(), po.getRepeatCount(), ObjUtil::isEmpty));
            po.setStopTime(merge(plan.getStopTime(), po.getStopTime(), ObjUtil::isEmpty));
        }
        if (ObjUtil.isNull(po.getUnit())) {
            po.setUnit(TimeUnit.SECONDS);
        }
        return po;
    }

    private List<JobScheduledTasksDTO> taskPO2DTO(List<JobScheduledTasksPO> list) {
        return list.stream().map(po -> {
            JobScheduledTasksDTO dto = new JobScheduledTasksDTO();
            dto.setId(po.getId());
            dto.setName(po.getName());
            dto.setBizKey(po.getBizKey());
            dto.setPlan(new JobTaskPlan(po.getType(), po.getCron(), po.getStartTime(), po.getInterval(), po.getUnit(), po.getRepeatCount(), po.getStopTime()));
            dto.setContent(JsonUtils.parse(po.getContent(), JobTaskContext.class));
            dto.setPriority(po.getPriority());
            dto.setStatus(po.getStatus());
            dto.setNextTime(po.getNextTime());
            dto.setCreateTime(po.getCreateTime());
            dto.setUpdateTime(po.getUpdateTime());
            dto.setCreateUserId(po.getCreateUserId());
            dto.setCreateUserName(po.getCreateUserName());
            dto.setCreateUserAvatar(po.getCreateUserAvatar());
            dto.setUpdateUserId(po.getUpdateUserId());
            dto.setUpdateUserName(po.getUpdateUserName());
            dto.setDeleted(po.getDeleted());
            return dto;
        }).collect(Collectors.toList());
    }

    public static <T> T merge(T target, T source, Predicate<T> isNullOrEmpty) {
        return isNullOrEmpty.test(target) ? source : target;
    }
}
