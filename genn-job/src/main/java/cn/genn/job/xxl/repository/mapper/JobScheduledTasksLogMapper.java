package cn.genn.job.xxl.repository.mapper;


import cn.genn.job.xxl.repository.po.JobScheduledTasksLogPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
 * <AUTHOR>
 */
public interface JobScheduledTasksLogMapper extends BaseMapper<JobScheduledTasksLogPO> {

    default Long selectCount(Long taskId){
        return selectCount(Wrappers.lambdaQuery(JobScheduledTasksLogPO.class).eq(JobScheduledTasksLogPO::getTaskId, taskId));
    }

    default JobScheduledTasksLogPO selectLastByTaskId(Long taskId){
        return selectOne(Wrappers.lambdaQuery(JobScheduledTasksLogPO.class)
                .eq(JobScheduledTasksLogPO::getTaskId, taskId)
                .orderByDesc(JobScheduledTasksLogPO::getId)
                .last("limit 1"));
    }
}
