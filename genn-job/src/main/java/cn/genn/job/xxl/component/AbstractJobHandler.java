package cn.genn.job.xxl.component;

import cn.genn.core.context.BaseRequestContext;
import com.xxl.job.core.handler.IJobHandler;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 */
public abstract class AbstractJobHandler extends IJobHandler {


    @Override
    public void execute() throws Exception {
        try {
            MDC.put("tid", TraceContext.traceId());
            doExecute();
        } finally {
            BaseRequestContext.clear();
            MDC.remove("tid");
        }
    }

    public abstract void doExecute();
}
