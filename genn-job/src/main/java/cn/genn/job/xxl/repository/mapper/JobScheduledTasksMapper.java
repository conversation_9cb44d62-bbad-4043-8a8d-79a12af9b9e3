package cn.genn.job.xxl.repository.mapper;

import cn.genn.job.xxl.model.task.TaskQuery;
import cn.genn.job.xxl.model.task.TaskStatusEnum;
import cn.genn.job.xxl.repository.po.JobScheduledTasksPO;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface JobScheduledTasksMapper extends BaseMapper<JobScheduledTasksPO> {

    default List<JobScheduledTasksPO> query(TaskQuery query){
        return selectList(Wrappers.lambdaQuery(JobScheduledTasksPO.class)
                .eq(ObjUtil.isNotNull(query.getId()),JobScheduledTasksPO::getId, query.getId())
                .eq(StrUtil.isNotBlank(query.getName()),JobScheduledTasksPO::getName, query.getName())
                .eq(StrUtil.isNotBlank(query.getBizKey()),JobScheduledTasksPO::getBizKey, query.getBizKey())
                .eq(ObjUtil.isNotNull(query.getStatus()),JobScheduledTasksPO::getStatus, query.getStatus()));
    }

    default List<JobScheduledTasksPO> getRunningTask(){
        return selectList(Wrappers.lambdaQuery(JobScheduledTasksPO.class)
                .eq(JobScheduledTasksPO::getStatus, TaskStatusEnum.RUNNING)
                .isNotNull(JobScheduledTasksPO::getNextTime)
                .orderByAsc(JobScheduledTasksPO::getPriority));
    }

    default void updateStatusById(Long id, TaskStatusEnum status,LocalDateTime nextTime){
        update(Wrappers.lambdaUpdate(JobScheduledTasksPO.class)
                .set(JobScheduledTasksPO::getStatus, status)
                .set(JobScheduledTasksPO::getNextTime, nextTime)
                .set(status.equals(TaskStatusEnum.FINISH), JobScheduledTasksPO::getNextTime, null)
                .eq(JobScheduledTasksPO::getId, id));
    }

    default void updateNextTimeById(Long id, LocalDateTime nextTime){
        update(Wrappers.lambdaUpdate(JobScheduledTasksPO.class)
                .set(JobScheduledTasksPO::getNextTime, nextTime)
                .eq(JobScheduledTasksPO::getId, id));
    }
}
