package cn.genn.job.xxl.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * xxl-job 过期策略枚举
 */
public enum XxlJobMisfireStrategyEnum {

    DO_NOTHING("DO_NOTHING", "忽略"),

    FIRE_ONCE_NOW("FIRE_ONCE_NOW", "立即执行一次");

    @Getter
    @JsonValue
    private final String strategy;
    @Getter
    private final String desc;

    XxlJobMisfireStrategyEnum(String strategy, String desc) {
        this.strategy = strategy;
        this.desc = desc;
    }

    private static final Map<String, XxlJobMisfireStrategyEnum> VALUES = new HashMap<>();
    static {
        for (final XxlJobMisfireStrategyEnum item : XxlJobMisfireStrategyEnum.values()) {
            VALUES.put(item.getStrategy(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static XxlJobMisfireStrategyEnum of(String strategy) {
        return VALUES.get(strategy);
    }
}
