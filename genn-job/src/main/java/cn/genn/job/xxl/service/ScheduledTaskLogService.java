package cn.genn.job.xxl.service;

import cn.genn.job.xxl.model.task.TaskExecuteStatusEnum;
import cn.genn.job.xxl.repository.po.JobScheduledTasksLogPO;
import cn.genn.job.xxl.repository.po.JobScheduledTasksPO;

import java.time.LocalDateTime;

public interface ScheduledTaskLogService {

    JobScheduledTasksLogPO saveStartLog(JobScheduledTasksPO task, LocalDateTime startTime);

    void updateLog(Long LogId, Throwable ex, String result, Long timeMs, TaskExecuteStatusEnum status);
}
