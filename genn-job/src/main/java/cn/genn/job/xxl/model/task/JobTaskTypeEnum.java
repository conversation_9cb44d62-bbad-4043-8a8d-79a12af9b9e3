package cn.genn.job.xxl.model.task;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum JobTaskTypeEnum {

    HTTP("http", "远程调用"),
    LOCAL("local", "本地调用"),
    ;

    @JsonValue
    private final String code;
    private final String desc;

    private static final Map<String, JobTaskTypeEnum> VALUES = new HashMap<>();
    static {
        for (final JobTaskTypeEnum item : JobTaskTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static JobTaskTypeEnum of(String type) {
        return VALUES.get(type);
    }
}
