package cn.genn.job.xxl.model.task;

import cn.genn.job.xxl.model.XxlJobScheduleTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobTaskPlan {

    @Schema(description = "生效类型")
    private XxlJobScheduleTypeEnum type;

    //1.表达式
    @Schema(description = "cron表达式")
    private String cron;

    //2.固定频率
    @Schema(description = "开始执行时间")
    private LocalDateTime startTime;

    @Schema(description = "执行频率")
    private Long interval;

    @Schema(description = "频率时间单位,默认秒")
    private TimeUnit unit;

    @Schema(description = "执行次数,为0表示不限此时")
    private Long repeatCount = 0L;

    @Schema(description = "截止时间,为空表示一直执行")
    private LocalDateTime stopTime;
}
