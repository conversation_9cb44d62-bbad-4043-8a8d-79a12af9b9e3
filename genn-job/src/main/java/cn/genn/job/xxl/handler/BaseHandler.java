package cn.genn.job.xxl.handler;

import lombok.extern.slf4j.Slf4j;

/**
 * 处理器链路基本逻辑
 * 建议子类继承该类实现一个抽象类
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseHandler {

    protected Class<? extends BaseHandler> next;

    public BaseHandler() {
        this.next = nextHandler();
    }

    public Class<? extends BaseHandler> getNext() {
        return next;
    }

    public boolean dealWrapper() {
        Class<? extends BaseHandler> cls = this.getClass();
        log.info("handler: [{}] begin deal", cls.getSimpleName());
        beforeDeal();
        boolean res = deal();
        afterDeal();
        log.info("handler: [{}] end deal", cls.getSimpleName());
        return res;
    }

    protected void afterDeal() {

    }

    protected void beforeDeal() {

    }

    /**
     * 设置当前执行器的下一个步骤
     */
    public abstract Class<? extends BaseHandler> nextHandler();

    /**
     * 处理逻辑
     * @return true:继续往下执行 false:终止执行
     */
    protected abstract boolean deal();

}
