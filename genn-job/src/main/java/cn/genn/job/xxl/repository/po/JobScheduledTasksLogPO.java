package cn.genn.job.xxl.repository.po;


import cn.genn.job.xxl.model.task.TaskExecuteStatusEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * JobCheduledTasksLogPO对象
 *
 * <AUTHOR>
 * @desc 
 */
@Data
@Accessors(chain = true)
@TableName(value = "job_scheduled_tasks_log", autoResultMap = true)
public class JobScheduledTasksLogPO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 任务id
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 任务快照
     */
    @TableField("task_snapshot")
    private String taskSnapshot;

    /**
     * 执行状态：0-执行中 1-成功 2-失败 3-超时
     */
    @TableField("status")
    private TaskExecuteStatusEnum status;

    /**
     * 执行返回结果
     */
    @TableField("output_result")
    private String outputResult;

    /**
     * 执行耗时（毫秒）
     */
    @TableField("execution_time_ms")
    private Long executionTimeMs;

    /**
     * 执行开始时间
     */
    @TableField("execution_start_time")
    private LocalDateTime executionStartTime;

    /**
     * 其他日志信息
     */
    @TableField("extra_info")
    private String extraInfo;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建人名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 更新人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

