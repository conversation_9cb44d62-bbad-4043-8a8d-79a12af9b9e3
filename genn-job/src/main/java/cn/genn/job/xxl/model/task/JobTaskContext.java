package cn.genn.job.xxl.model.task;

import cn.hutool.core.util.ObjUtil;
import lombok.Data;

import java.util.Map;

@Data
public class JobTaskContext {
    private JobTaskTypeEnum taskType;

    //请求超时时间,单位秒
    private Long reqTimeout = 60L;

    // HTTP 类型字段
    private String url;
    private String method; //get,post
    private Map<String, Object> headers;
    private Map<String, Object> body;

    // 本地方法字段
    private String className;
    private String methodName;
    private Map<String, Object> params;

    public static boolean checkAdd(JobTaskContext context){
        if(ObjUtil.isNull(context.taskType)){
            return false;
        }
        if(context.taskType.equals(JobTaskTypeEnum.HTTP) && (ObjUtil.isNull(context.url) || ObjUtil.isNull(context.method))){
            return false;
        }
        if(context.taskType.equals(JobTaskTypeEnum.LOCAL) && (ObjUtil.isNull(context.className) || ObjUtil.isNull(context.methodName))){
            return false;
        }
        return true;
    }
}
