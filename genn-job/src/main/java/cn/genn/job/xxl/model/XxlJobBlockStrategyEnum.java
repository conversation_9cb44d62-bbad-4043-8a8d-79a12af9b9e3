package cn.genn.job.xxl.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum XxlJobBlockStrategyEnum {

    SERIAL_EXECUTION("SERIAL_EXECUTION", "单机串行"),
    DISCARD_LATER("DISCARD_LATER", "丢弃后续调度"),
    COVER_EARLY("COVER_EARLY", "覆盖之前调度");

    @Getter
    @JsonValue
    private final String strategy;
    @Getter
    private final String desc;

    XxlJobBlockStrategyEnum(String strategy, String desc) {
        this.strategy = strategy;
        this.desc = desc;
    }

    public static Map<String, XxlJobBlockStrategyEnum> VALUES = new HashMap<>();

    static {
        for (final XxlJobBlockStrategyEnum item : XxlJobBlockStrategyEnum.values()) {
            VALUES.put(item.getStrategy(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static XxlJobBlockStrategyEnum of(String strategy) {
        return VALUES.get(strategy);
    }
}
