package cn.genn.job.xxl.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.job.xxl.model.XxlJobScheduleTypeEnum;
import cn.genn.job.xxl.model.task.TaskStatusEnum;
import cn.genn.job.xxl.model.task.TimeUnit;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * JobScheduledTasksPO对象
 *
 * <AUTHOR>
 * @desc 定时任务表
 */
@Data
@Accessors(chain = true)
@TableName(value = "job_scheduled_tasks", autoResultMap = true)
public class JobScheduledTasksPO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 任务名称
     */
    @TableField("name")
    private String name;

    /**
     * 任务类型标记
     */
    @TableField("biz_key")
    private String bizKey;

    /**
     * 任务类型（CRON：表达式；FIX_RATE：固定频率）
     */
    @TableField("type")
    private XxlJobScheduleTypeEnum type;

    /**
     * cron表达式
     */
    @TableField("cron")
    private String cron;

    /**
     * 开始执行时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 执行频率
     */
    @TableField("`interval`")
    private Long interval;

    /**
     * 频率单位（second秒，minute分，hour小时，day天）
     */
    @TableField("`unit`")
    private TimeUnit unit;

    /**
     * 执行次数
     */
    @TableField("repeat_count")
    private Long repeatCount;

    /**
     * 截止时间
     */
    @TableField("stop_time")
    private LocalDateTime stopTime;

    /**
     * 下次执行时间
     */
    @TableField("next_time")
    private LocalDateTime nextTime;

    /**
     * 任务内容
     */
    @TableField("content")
    private String content;

    /**
     * 任务是否可以重叠执行,默认false:不可以
     */
    @TableField("overlap")
    private Boolean overlap = false;

    /**
     * 优先级（0-99，数字越小越优先）
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 任务状态（running,stop,finish）
     */
    @TableField("`status`")
    private TaskStatusEnum status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 头像
     */
    @TableField("create_user_avatar")
    private String createUserAvatar;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

}

