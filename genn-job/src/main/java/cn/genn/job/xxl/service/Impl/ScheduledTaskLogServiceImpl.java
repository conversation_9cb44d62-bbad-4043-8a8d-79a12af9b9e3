package cn.genn.job.xxl.service.Impl;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.model.task.TaskExecuteStatusEnum;
import cn.genn.job.xxl.repository.mapper.JobScheduledTasksLogMapper;
import cn.genn.job.xxl.repository.po.JobScheduledTasksLogPO;
import cn.genn.job.xxl.repository.po.JobScheduledTasksPO;
import cn.genn.job.xxl.service.ScheduledTaskLogService;
import cn.hutool.core.util.ObjUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class ScheduledTaskLogServiceImpl implements ScheduledTaskLogService {

    @Resource
    private JobScheduledTasksLogMapper jobScheduledTasksLogMapper;

    public JobScheduledTasksLogPO saveStartLog(JobScheduledTasksPO task, LocalDateTime startTime) {
        JobScheduledTasksLogPO log = new JobScheduledTasksLogPO();
        log.setTaskId(task.getId());
        log.setTaskSnapshot(JsonUtils.toJson(task));
        log.setStatus(TaskExecuteStatusEnum.EXECUTING);
        log.setExecutionStartTime(startTime);
        jobScheduledTasksLogMapper.insert(log);
        return log;
    }

    public void updateLog(Long LogId, Throwable ex, String result, Long timeMs, TaskExecuteStatusEnum status) {
        JobScheduledTasksLogPO po = new JobScheduledTasksLogPO()
                .setId(LogId)
                .setStatus(status)
                .setOutputResult(ObjUtil.isNull(ex) ? result : ex.getMessage())
                .setExecutionTimeMs(timeMs);
        jobScheduledTasksLogMapper.updateById(po);
    }
}
