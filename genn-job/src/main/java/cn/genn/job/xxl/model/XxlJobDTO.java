package cn.genn.job.xxl.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class XxlJobDTO implements Serializable {
    private static final long serialVersionUID = -3106408145813142728L;

    private Integer id;
    /**
     * 任务描述
     */
    private String jobDesc;

    /**
     * 调用类型
     */
    private XxlJobScheduleTypeEnum scheduleType;

    /**
     * 调度配置,值取决了于调度类型
     */
    private String scheduleConf;
    /**
     * 负责人
     */
    private String author;
    /**
     * 过期策略
     */
    private XxlJobMisfireStrategyEnum misfireStrategy;            // 调度过期策略
    /**
     * 执行路由策略
     */
    private XxlJobRouteStrategyEnum executorRouteStrategy;    // 执行器路由策略
    private String executorHandler;            // 执行器，任务Handler名称
    private String executorParam;            // 执行器，任务参数
    private XxlJobBlockStrategyEnum executorBlockStrategy;    // 阻塞处理策略
    private Integer executorTimeout;            // 任务执行超时时间，单位秒
    private Integer executorFailRetryCount;        // 失败重试次数

    /**
     * 以下参数仅限查询回显使用
     */
    private Integer triggerStatus;        // 调度状态：0-停止，1-运行
    private Long triggerLastTime;    // 上次调度时间
    private Long triggerNextTime;    // 下次调度时间

    public XxlJobInfo buildJonInfo() {
        XxlJobInfo jobInfo = new XxlJobInfo();
        jobInfo.setId(this.id)
                .setJobDesc(this.jobDesc)
                .setAuthor(this.author)
                .setMisfireStrategy(this.misfireStrategy.getStrategy())
                .setExecutorRouteStrategy(this.executorRouteStrategy.getRouteStrategy())
                .setExecutorHandler(this.executorHandler)
                .setExecutorParam(this.executorParam)
                .setExecutorBlockStrategy(this.executorBlockStrategy.getStrategy())
                .setGlueType("BEAN")
                .setScheduleConf(this.scheduleConf);
        if (this.scheduleType == null) {
            jobInfo.setScheduleType(XxlJobScheduleTypeEnum.CRON.getType());
        }
        return jobInfo;
    }

    public static XxlJobDTO buildJobDTO(XxlJobInfo jobInfo) {
        XxlJobDTO jobDTO = new XxlJobDTO();
        jobDTO.setId(jobInfo.getId())
                .setJobDesc(jobInfo.getJobDesc())
                .setAuthor(jobInfo.getAuthor())
                .setMisfireStrategy(XxlJobMisfireStrategyEnum.of(jobInfo.getMisfireStrategy()))
                .setExecutorRouteStrategy(XxlJobRouteStrategyEnum.of(jobInfo.getExecutorRouteStrategy()))
                .setExecutorHandler(jobInfo.getExecutorHandler())
                .setExecutorParam(jobInfo.getExecutorParam())
                .setExecutorBlockStrategy(XxlJobBlockStrategyEnum.of(jobInfo.getExecutorBlockStrategy()))
                .setScheduleType(XxlJobScheduleTypeEnum.of(jobInfo.getScheduleConf()))
                .setScheduleConf(jobInfo.getScheduleConf())
                .setTriggerStatus(jobInfo.getTriggerStatus())
                .setTriggerLastTime(jobInfo.getTriggerLastTime())
                .setTriggerNextTime(jobInfo.getTriggerNextTime());
        return jobDTO;
    }
}
