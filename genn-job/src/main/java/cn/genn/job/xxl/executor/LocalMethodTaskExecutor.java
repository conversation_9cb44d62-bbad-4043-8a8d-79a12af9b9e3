package cn.genn.job.xxl.executor;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.model.task.JobTaskContext;
import cn.genn.job.xxl.model.task.JobTaskTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

@Component
@Slf4j
public class LocalMethodTaskExecutor implements JobTaskExecutor {

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public boolean supports(JobTaskTypeEnum taskType) {
        return JobTaskTypeEnum.LOCAL.equals(taskType);
    }

    @Override
    public String execute(JobTaskContext context) throws RuntimeException {
        String className = context.getClassName();
        String methodName = context.getMethodName();
        Map<String, Object> params = context.getParams();

        Object bean = getBean(className);
        Method method = null;
        try {
            method = findMethod(bean, methodName, params);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        Class<?>[] paramTypes = method.getParameterTypes();
        Object[] args = new Object[paramTypes.length];

        List<Object> values = new ArrayList<>(params.values());

        for (int i = 0; i < paramTypes.length; i++) {
            Object value = i < values.size() ? values.get(i) : null;

            if (value == null && !paramTypes[i].isPrimitive()) {
                args[i] = null;
                continue;
            }

            if (paramTypes[i].isAssignableFrom(value.getClass())) {
                args[i] = value;
            } else if (value instanceof Map) {
                args[i] = JsonUtils.parse(JsonUtils.toJson(value), paramTypes[i]);
            } else {
                throw new IllegalArgumentException("无法转换参数到类型 " + paramTypes[i]);
            }
        }

        try {
            Object result = method.invoke(bean, args);
            return JsonUtils.toJson(result);
        } catch (IllegalAccessException | IllegalArgumentException e) {
            throw new RuntimeException("反射调用失败: " + e.getMessage(), e);
        } catch (InvocationTargetException e) {
            Throwable cause = e.getCause();
            throw new RuntimeException("方法执行失败，原始异常：" + cause.getClass().getName() + ": " + cause.getMessage(), cause);
        }
    }

    private Object getBean(String className) {
        try {
            return applicationContext.getBean(Class.forName(className));
        } catch (ClassNotFoundException e) {
            log.error("未获取到类: {}", className);
            throw new RuntimeException(e);
        }
    }

    private Method findMethod(Object bean, String methodName, Map<String, Object> params) throws Exception {
        List<Method> candidates = new ArrayList<>();

        for (Method method : bean.getClass().getDeclaredMethods()) {
            if (!method.getName().equals(methodName)) continue;

            Class<?>[] paramTypes = method.getParameterTypes();
            if (paramTypes.length != params.size()) continue;

            boolean match = true;
            List<Object> values = new ArrayList<>(params.values());

            for (int i = 0; i < paramTypes.length; i++) {
                Object value = i < values.size() ? values.get(i) : null;

                if (value == null && !paramTypes[i].isPrimitive()) {
                    continue;
                }

                if (paramTypes[i].isAssignableFrom(value.getClass())) {
                    continue;
                } else if (value instanceof Map) {
                    try {
                        JsonUtils.parse(JsonUtils.toJson(value), paramTypes[i]);
                    } catch (Exception e) {
                        match = false;
                        break;
                    }
                } else {
                    match = false;
                    break;
                }
            }

            if (match) {
                candidates.add(method);
            }
        }

        if (candidates.isEmpty()) {
            throw new NoSuchMethodException("找不到匹配的方法: " + methodName);
        }

        if (candidates.size() > 1) {
            throw new IllegalStateException("找到多个匹配方法，请明确指定参数类型");
        }

        return candidates.getFirst();
    }
}
