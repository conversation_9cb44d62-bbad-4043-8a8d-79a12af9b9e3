package cn.genn.job.xxl.model.task;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TimeUnit {

    SECONDS("second", "秒"),
    MINUTE("minute", "分"),
    HOUR("hour", "小时"),
    DAY("day", "天"),
    WEEK("week", "周"),
    MONTH("month", "月"),
    ;

    @JsonValue
    private final String type;
    private final String desc;

    private static final Map<String, TimeUnit> VALUES = new HashMap<>();
    static {
        for (final TimeUnit item : TimeUnit.values()) {
            VALUES.put(item.getType(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TimeUnit of(String type) {
        return VALUES.get(type);
    }

    public static LocalDateTime nextTime(LocalDateTime base, Long amount, TimeUnit unit) {
        switch (unit) {
            case SECONDS:
                return base.plusSeconds(amount);
            case MINUTE:
                return base.plusMinutes(amount);
            case HOUR:
                return base.plusHours(amount);
            case DAY:
                return base.plusDays(amount);
            case WEEK:
                return base.plusWeeks(amount);
            case MONTH:
                return base.plusMonths(amount);
            default:
                throw new IllegalArgumentException("Unsupported time unit: " + unit);
        }
    }
}
