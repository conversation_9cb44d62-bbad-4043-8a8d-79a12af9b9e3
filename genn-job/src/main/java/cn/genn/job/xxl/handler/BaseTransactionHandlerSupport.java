package cn.genn.job.xxl.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * 支持事务的处理器
 *
 * @see BaseJobContext
 * @see BaseHandler
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseTransactionHandlerSupport extends BaseHandlerSupport {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void execute() throws Exception {
        super.execute();
    }
}
