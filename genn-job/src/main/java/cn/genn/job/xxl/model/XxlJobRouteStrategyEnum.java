package cn.genn.job.xxl.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum XxlJobRouteStrategyEnum {

    FIRST("FIRST", "第一个"),
    LAST("LAST", "最后一个"),
    ROUND("ROUND", "轮询"),
    RANDOM("RANDOM", "随机"),
    CONSISTENT_HASH("CONSISTENT_HASH", "一致性HASH"),
    LEAST_FREQUENTLY_USED("LEAST_FREQUENTLY_USED", "最不经常使用"),
    LEAST_RECENTLY_USED("LEAST_RECENTLY_USED", "最近最久未使用"),
    FAILOVER("FAILOVER", "故障转移"),
    BUSYOVER("BUSYOVER", "忙碌转移"),
    SHARDING_BROADCAST("SHARDING_BROADCAST", "分片广播");

    @Getter
    @JsonValue
    private final String routeStrategy;
    @Getter
    private final String desc;

    XxlJobRouteStrategyEnum(String routeStrategy, String desc) {
        this.routeStrategy = routeStrategy;
        this.desc = desc;
    }

    public static Map<String, XxlJobRouteStrategyEnum> VALUES = new HashMap<>();
    static {
        for (final XxlJobRouteStrategyEnum item : XxlJobRouteStrategyEnum.values()) {
            VALUES.put(item.getRouteStrategy(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static XxlJobRouteStrategyEnum of(String routeStrategy) {
        return VALUES.get(routeStrategy);
    }
}
