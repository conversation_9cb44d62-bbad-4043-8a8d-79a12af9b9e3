package cn.genn.job.xxl.service;

import cn.genn.core.model.ddd.IdCommand;
import cn.genn.job.xxl.model.task.JobScheduledTasksDTO;
import cn.genn.job.xxl.model.task.TaskQuery;
import cn.genn.job.xxl.model.task.TaskSaveOrUpdateCommand;
import cn.genn.job.xxl.model.task.UpdateStatusCommand;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface ScheduledTaskService {

    void saveOrUpdate(TaskSaveOrUpdateCommand command);

    void delete(IdCommand command);

    void updateStatus(UpdateStatusCommand command);

    List<JobScheduledTasksDTO> query(TaskQuery query);

    void execute();
}
