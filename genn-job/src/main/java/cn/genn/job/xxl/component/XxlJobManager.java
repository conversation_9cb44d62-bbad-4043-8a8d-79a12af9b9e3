package cn.genn.job.xxl.component;


import cn.genn.core.utils.CronUtils;
import cn.genn.job.xxl.model.XxlJobDTO;
import cn.genn.job.xxl.model.XxlJobInfo;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class XxlJobManager {

    private final XxlJobAdminService xxlJobAdminService;

    private final String executorName;

    public XxlJobManager(XxlJobAdminService xxlJobAdminService, String executorName) {
        this.xxlJobAdminService = xxlJobAdminService;
        this.executorName = executorName;
    }

    /**
     * 添加一个每分钟执行的定时任务
     * @param jobDesc   任务描述
     * @param handlerName   任务handler
     * @param param     任务参数
     */
    public Integer addEveryMinuteJob(String jobDesc, String handlerName, String param) {
        return this.addJob(jobDesc, () -> CronUtils.genMinutesCron(1), handlerName, param);
    }

    /**
     * 添加一个指定cron表达式的定时任务
     * <p>
     * 默认路由策略为 FIRST，即选择第一个可用的执行器
     * <p>
     * 阻塞处理策略为 SERIAL_EXECUTION, 即单机串行执行
     * <p>
     * 过期策略为 DO_NOTHING, 即忽略
     *
     * @param jobDesc  任务描述
     * @param cron    cron表达式生成器,请使用CronUtils生成
     * @param handlerName 任务handler
     * @param param   任务参数
     */
    public Integer addJob(String jobDesc, Supplier<String> cron, String handlerName, String param) {
        return xxlJobAdminService.createJob(jobDesc, cron.get(), handlerName, param);
    }

    /**
     * 添加一个自定义参数的定时任务
     */
    public Integer addJob(XxlJobDTO jobDTO) {
        XxlJobInfo xxlJobInfo = jobDTO.buildJonInfo();
        xxlJobInfo.setJobGroup(xxlJobAdminService.getJobGroup(executorName).getId());
        return xxlJobAdminService.createJob(xxlJobInfo);
    }

    /**
     * 更新任务
     * @param jobId 任务唯一id
     * @param cron cron表达式生成器,请使用CronUtils生成
     * @param param 任务参数
     */
    public void updateJob(int jobId, Supplier<String> cron, String param) {
        xxlJobAdminService.updateJob(jobId, cron.get(), param);
    }

    /**
     * 更新任务,详细信息
     * @param jobDTO 任务信息
     */
    public void updateJob(XxlJobDTO jobDTO) {
        XxlJobInfo xxlJobInfo = jobDTO.buildJonInfo();
        xxlJobInfo.setJobGroup(xxlJobAdminService.getJobGroup(executorName).getId());
        xxlJobAdminService.updateJob(xxlJobInfo);
    }

    /**
     * 删除任务
     * @param jobId 任务唯一id
     */
    public void removeJob(int jobId) {
        xxlJobAdminService.removeJob(jobId);
    }

    /**
     * 启动任务
     * @param jobId 任务唯一id
     */
    public void startJob(int jobId) {
        xxlJobAdminService.startJob(jobId);
    }

    /**
     * 停止任务
     * @param jobId 任务唯一id
     */
    public void stopJob(int jobId) {
        xxlJobAdminService.stopJob(jobId);
    }

    /**
     * 触发一次任务
     * @param jobId 任务唯一id
     * @param executorParam 任务执行参数
     */
    public void triggerJob(int jobId, String executorParam) {
        xxlJobAdminService.triggerJob(jobId, executorParam);
    }

    /**
     * 获取任务信息
     * @param jobId 任务唯一id
     */
    public XxlJobDTO getJobInfo(int jobId) {
        return XxlJobDTO.buildJobDTO(xxlJobAdminService.getJob(jobId));
    }
}
