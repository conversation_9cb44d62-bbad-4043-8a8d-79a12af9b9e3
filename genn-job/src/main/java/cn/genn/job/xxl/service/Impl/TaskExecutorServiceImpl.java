package cn.genn.job.xxl.service.Impl;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.executor.JobTaskExecutor;
import cn.genn.job.xxl.model.task.JobTaskContext;
import cn.genn.job.xxl.model.task.TaskExecuteStatusEnum;
import cn.genn.job.xxl.repository.po.JobScheduledTasksLogPO;
import cn.genn.job.xxl.repository.po.JobScheduledTasksPO;
import cn.genn.job.xxl.service.ScheduledTaskLogService;
import cn.genn.job.xxl.service.TaskExecutorService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.*;

@Slf4j
@Service
public class TaskExecutorServiceImpl implements TaskExecutorService {

    @Resource
    private List<JobTaskExecutor> executors;
    @Resource
    private ScheduledTaskLogService scheduledTaskLogService;

    //虚拟线程
    private static final ExecutorService EXECUTOR = Executors.newVirtualThreadPerTaskExecutor();

    @Override
    public void executeTask(JobScheduledTasksPO task) {
        JobTaskContext context = JsonUtils.parse(task.getContent(), JobTaskContext.class);
        JobTaskExecutor executor = executors.stream()
                .filter(e -> e.supports(context.getTaskType()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("不支持的任务类型: " + context.getTaskType()));
        LocalDateTime startTime = LocalDateTime.now();
        JobScheduledTasksLogPO logPO = scheduledTaskLogService.saveStartLog(task, startTime);
        CompletableFuture<String> future = CompletableFuture
                .supplyAsync(() -> executor.execute(context), EXECUTOR)
                .exceptionally(ex -> {
                    log.error("[{}]任务执行失败!!taskLogId:{}", task.getName(), logPO.getId(), ex);
                    scheduledTaskLogService.updateLog(logPO.getId(), ex, null, Duration.between(startTime, LocalDateTime.now()).toMillis(), TaskExecuteStatusEnum.FAILED);
                    return null;
                });
        try {
            String result = future.get(context.getReqTimeout(), TimeUnit.SECONDS);
            scheduledTaskLogService.updateLog(logPO.getId(), null, result, Duration.between(startTime, LocalDateTime.now()).toMillis(), TaskExecuteStatusEnum.SUCCESS);
        } catch (TimeoutException e) {
            future.cancel(true);
            log.warn("[{}}]任务执行超时，taskLogId: {}", task.getName(), logPO.getId());
            scheduledTaskLogService.updateLog(logPO.getId(), e, null, Duration.between(startTime, LocalDateTime.now()).toMillis(), TaskExecuteStatusEnum.TIMEOUT);
        } catch (Exception e) {
            log.error("[{}}]任务执行失败!!!taskLogId:{}", task.getName(), logPO.getId(), e);
            scheduledTaskLogService.updateLog(logPO.getId(), e, null, Duration.between(startTime, LocalDateTime.now()).toMillis(), TaskExecuteStatusEnum.FAILED);
        }
    }

}
