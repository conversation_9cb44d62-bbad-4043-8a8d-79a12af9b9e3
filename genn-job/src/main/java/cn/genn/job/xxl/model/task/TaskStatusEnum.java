package cn.genn.job.xxl.model.task;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    RUNNING("running", "启动中"),
    STOP("stop", "已暂停"),
    FINISH("finish", "已完成"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    private static final Map<String, TaskStatusEnum> VALUES = new HashMap<>();
    static {
        for (final TaskStatusEnum item : TaskStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TaskStatusEnum of(String type) {
        return VALUES.get(type);
    }
}
