package cn.genn.job.xxl.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum XxlJobScheduleTypeEnum {

    CRON("CRON", "CRON表达式"),

    FIX_RATE("FIX_RATE", "固定频率");

    @Getter
    @JsonValue
    private final String type;
    @Getter
    private final String desc;

    XxlJobScheduleTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private static final Map<String, XxlJobScheduleTypeEnum> VALUES = new HashMap<>();
    static {
        for (final XxlJobScheduleTypeEnum item : XxlJobScheduleTypeEnum.values()) {
            VALUES.put(item.getType(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static XxlJobScheduleTypeEnum of(String type) {
        return VALUES.get(type);
    }
}
