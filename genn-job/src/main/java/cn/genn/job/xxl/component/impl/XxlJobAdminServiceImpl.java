package cn.genn.job.xxl.component.impl;

import cn.genn.core.utils.http.OkHttpUtil;
import cn.genn.core.utils.http.model.HttpResponse;
import cn.genn.core.utils.http.okhttp.HttpClient;
import cn.genn.core.utils.http.okhttp.LocalCookieJar;
import cn.genn.core.utils.http.okhttp.OkHttpClientConfig;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.component.XxlJobAdminService;
import cn.genn.job.xxl.model.XxlJobGroup;
import cn.genn.job.xxl.model.XxlJobInfo;
import cn.genn.job.xxl.properties.XxlJobAdminProperties;
import cn.genn.job.xxl.properties.XxlJobExecutorProperties;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JavaType;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class XxlJobAdminServiceImpl implements XxlJobAdminService {

    private final OkHttpUtil okHttpUtil;

    private final XxlJobAdminProperties adminProperties;
    private final XxlJobExecutorProperties executorProperties;
    @Setter
    private String author;

    private String adminAddress;

    public XxlJobAdminServiceImpl(XxlJobAdminProperties adminProperties, XxlJobExecutorProperties executorProperties) {
        this.adminProperties = adminProperties;
        this.executorProperties = executorProperties;
        this.adminAddress = adminProperties.getAddresses()[0];
        if (!this.adminAddress.endsWith("/")) {
            this.adminAddress = this.adminAddress + "/";
        }
        this.okHttpUtil = new OkHttpUtil(new HttpClient(new OkHttpClientConfig()
                .setFollowRedirects(false)
                .setFollowSslRedirects(false)
                .setCookieJar(new LocalCookieJar())));
    }

    @Override
    public int createJob(String jobDesc, String cron, String handler, String param) {
        return createJob(executorProperties.getAppName(), jobDesc, cron, handler, param);
    }

    @Override
    public int createJob(String appName, String jobDesc, String cron, String handler, String param) {
        Assert.hasText(appName, "请输入执行器appName");
        XxlJobGroup jobGroup = getJobGroup(appName);
        if (jobGroup == null) {
            if (executorProperties.isAutoCreateJobGroup()) {
                createJobGroup(executorProperties.getAppName(), executorProperties.getAppDesc());
                jobGroup = getJobGroup(appName);
            } else {
                throw new RuntimeException("xxlJob服务操作失败：找不到执行器");
            }
        }
        int jobGroupId = jobGroup.getId();
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        xxlJobInfo.setJobGroup(jobGroupId)
                .setJobDesc(jobDesc)
                .setAuthor(StrUtil.isEmpty(author) ? "xxlAdmin": author)
                .setScheduleType("CRON")
                .setScheduleConf(cron)
                .setGlueType("BEAN")
                .setExecutorHandler(handler)
                .setExecutorParam(param)
                .setExecutorRouteStrategy("FIRST")
                .setMisfireStrategy("DO_NOTHING")
                .setExecutorBlockStrategy("SERIAL_EXECUTION");
        return createJob(xxlJobInfo);
    }

    @Override
    public int createJob(XxlJobInfo jobInfo) {
        return postForm("jobinfo/add", JsonUtils.parseToMap(JsonUtils.toJson(jobInfo)), Integer.class);
    }

    @Override
    public void updateJob(int jobId, String cron, String param) {
        XxlJobInfo jobInfo = getJob(jobId);
        jobInfo.setScheduleConf(cron)
                .setExecutorParam(param);
        updateJob(jobInfo);
    }

    @Override
    public void updateJob(XxlJobInfo jobInfo) {
        jobInfo.setAddTime(null)
                .setGlueUpdatetime(null)
                .setUpdateTime(null);
        postForm("jobinfo/update", JsonUtils.parseToMap(JsonUtils.toJson(jobInfo)), Void.class);
    }

    @Override
    public void removeJob(int jobId) {
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(jobId));
        postForm("jobinfo/remove", params, Void.class);
    }

    @Override
    public void startJob(int jobId) {
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(jobId));
        postForm("jobinfo/start", params, Void.class);
    }

    @Override
    public void stopJob(int jobId) {
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(jobId));
        postForm("jobinfo/stop", params, Void.class);
    }

    @Override
    public void triggerJob(int jobId, String executorParam) {
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(jobId));
        params.put("executorParam", executorParam);
        postForm("jobinfo/trigger", params, Void.class);
    }

    @Override
    public XxlJobInfo getJob(int jobId) {
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(jobId));
        return postForm("jobinfo/loadById", params, XxlJobInfo.class);
    }

    @Override
    public void createJobGroup(String appName, String title) {
        Map<String, String> params = new HashMap<>();
        params.put("appname", appName);
        params.put("title", StringUtils.hasLength(title) ? title : appName);
        params.put("addressType", "0");
        postForm("jobgroup/save", params, Void.class);
    }

    @Override
    public XxlJobGroup getJobGroup(String appName) {
        Map<String, String> params = new HashMap<>();
        params.put("appname", appName);
        params.put("start", "0");
        params.put("length", "10");
        JobGroupPageList list = postFormWithoutReturnT("jobgroup/pageList", params, JobGroupPageList.class);
        if (CollectionUtils.isEmpty(list.getData())) {
            return null;
        }
        return list.getData().stream()
                .filter(x -> x.getAppname().equals(appName))
                .findFirst()
                .orElse(null);
    }

    private void doLogin() {
        Map<String, String> params = new HashMap<>();
        params.put("userName", adminProperties.getUsername());
        params.put("password", adminProperties.getPassword());
        ReturnT response = okHttpUtil.postForm(adminAddress + "login", params).toObj(ReturnT.class);
        if (response.getCode() == 200) {
            log.info("xxl job admin login success");
        } else {
            throw new RuntimeException("xxlJob登录失败：" + response.getMsg());
        }
    }

    private <T> T postForm(String url, Map<String, String> params, Class<T> clazz) {
        HttpResponse response = okHttpUtil.postForm(adminAddress + url, params);
        if (response.getCode() == 302 && response.getHeader("location").endsWith("/toLogin")) {
            doLogin();
            response = okHttpUtil.postForm(adminAddress + url, params);
        }
        JavaType javaType = JsonUtils.constructGenericType(ReturnT.class, clazz);
        ReturnT<T> result = response.toObj(javaType);
        if (result == null) {
            throw new RuntimeException("操作失败");
        }
        if (result.getCode() != ReturnT.SUCCESS_CODE) {
            throw new RuntimeException("xxlJob服务操作失败：" + result.getMsg() + "(" + result.getCode() + ")");
        }
        return result.getContent();
    }

    private <T> T postFormWithoutReturnT(String url, Map<String, String> params, Class<T> clazz) {
        HttpResponse response = okHttpUtil.postForm(adminAddress + url, params);
        if (response.getCode() == 302 && response.getHeader("location").endsWith("/toLogin")) {
            doLogin();
            response = okHttpUtil.postForm(adminAddress + url, params);
        }
        T result = response.toObj(clazz);
        if (result == null) {
            throw new RuntimeException("xxl-job错误");
        }
        return result;
    }

    @Data
    public static class JobGroupPageList {
        private Integer recordsTotal;
        private Integer recordsFiltered;
        private List<XxlJobGroup> data;
    }
}
