package cn.genn.job.xxl.model.task;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskExecuteStatusEnum {

    EXECUTING("0", "执行中"),
    SUCCESS("1", "成功"),
    FAILED("2", "失败"),
    TIMEOUT("3", "超时");

    @EnumValue
    @JsonValue
    private final String code;
    private final String description;

    private static final Map<String, TaskExecuteStatusEnum> VALUES = new HashMap<>();
    static {
        for (final TaskExecuteStatusEnum item : TaskExecuteStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TaskExecuteStatusEnum of(String type) {
        return VALUES.get(type);
    }


}
