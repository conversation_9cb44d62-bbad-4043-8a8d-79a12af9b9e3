package cn.genn.job.xxl.handler;

import com.xxl.job.core.context.XxlJobContext;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 子类可以继承该类实现自定义属性
 * 也可以直接使用attachments放入属性
 * <AUTHOR>
 */
public class BaseJobContext {

    private final Map<String, Object> attachments = new HashMap<>();
    @Getter
    @Setter
    private XxlJobContext xxlJobContext;

    public Object getAttachment(String key) {
        return attachments.get(key);
    }

    public void setAttachment(String key, Object value) {
        attachments.put(key, value);
    }

    private static final InheritableThreadLocal<BaseJobContext> contextHolder = new InheritableThreadLocal<>();

    public static BaseJobContext get() {
        return contextHolder.get();
    }

    public static void setContent(BaseJobContext content) {
        contextHolder.set(content);
    }

    public static void removeContent() {
        contextHolder.remove();
    }

}
