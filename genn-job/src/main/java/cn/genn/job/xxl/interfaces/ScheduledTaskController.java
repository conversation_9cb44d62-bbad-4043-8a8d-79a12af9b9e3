package cn.genn.job.xxl.interfaces;


import cn.genn.core.model.ddd.IdCommand;
import cn.genn.job.xxl.model.task.JobScheduledTasksDTO;
import cn.genn.job.xxl.model.task.TaskQuery;
import cn.genn.job.xxl.model.task.TaskSaveOrUpdateCommand;
import cn.genn.job.xxl.model.task.UpdateStatusCommand;
import cn.genn.job.xxl.service.ScheduledTaskService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/job")
public class ScheduledTaskController {

    @Resource
    private ScheduledTaskService scheduledTaskService;

    @PostMapping("/saveOrUpdate")
    @Operation(summary = "添加或编辑定时任务")
    public void saveOrUpdate(@RequestBody @Validated TaskSaveOrUpdateCommand command) {
        scheduledTaskService.saveOrUpdate(command);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除定时任务")
    public void delete(@RequestBody @Validated IdCommand command) {
        scheduledTaskService.delete(command);
    }

    @PostMapping("/updateStatus")
    @Operation(summary = "更新状态")
    public void updateStatus(@RequestBody @Validated UpdateStatusCommand command) {
        scheduledTaskService.updateStatus(command);
    }

    @PostMapping("/query")
    @Operation(summary = "查询定时任务")
    public List<JobScheduledTasksDTO> query(@RequestBody @Validated TaskQuery query) {
        return scheduledTaskService.query(query);
    }

    @GetMapping("/test")
    public void test() {
        scheduledTaskService.execute();
    }

}
