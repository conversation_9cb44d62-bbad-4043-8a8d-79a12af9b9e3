package cn.genn.job.xxl.job;

import cn.genn.job.xxl.service.ScheduledTaskService;
import com.xxl.job.core.handler.IJobHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

@Slf4j
@Component
public class ScheduledTaskHandler extends IJobHandler {

    @Resource
    private ScheduledTaskService scheduledTaskService;

    @Override
    public void execute() {
        log.info("[ScheduledTaskHandler]开始执行");
        Instant start = Instant.now();
        scheduledTaskService.execute();
        log.info("[ScheduledTaskHandler]执行完成, 耗时" + Duration.between(start, Instant.now()).toMillis() + " 毫秒");

    }

}