package cn.genn.job.xxl.handler;

import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.hutool.extra.spring.SpringUtil;
import com.xxl.job.core.context.XxlJobContext;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 子类定时处理逻辑继承该support,需要配合BaseHandler,BaseJobContext使用
 * 1. 通过firstHandler()指定第一个处理器
 * 2. 通过contextType()指定上下文类型
 * 3. 通过getJobChainName()指定任务名称
 * <p>
 * 特性:
 * 1. 自动实现链式调用,支持多个处理器
 * 2. 自动实现上下文传递,支持多个处理器共享上下文
 *
 * <AUTHOR>
 * @see BaseJobContext
 * @see BaseHandler
 */
@Slf4j
public abstract class BaseHandlerSupport extends AbstractJobHandler {

    @Override
    public void doExecute() {
        BaseJobContext context;
        try {
            context = contextType().newInstance();
        } catch (Exception e) {
            throw new IllegalStateException("上下文实例化失败");
        }
        BaseJobContext.setContent(context);
        context.setXxlJobContext(XxlJobContext.getXxlJobContext());
        BaseHandler currHandler = null;
        try {
            log.info("begin execute task: [{}]", getJobChainName());
            beforeExecute();
            Map<Class<?>, BaseHandler> handlers = SpringUtil.getBeansOfType(BaseHandler.class)
                    .values().stream().collect(Collectors.toMap(BaseHandler::getClass, Function.identity()));
            Class<? extends BaseHandler> firstHandler = firstHandler();
            for (currHandler = handlers.get(firstHandler); currHandler != null; currHandler = handlers.get(currHandler.getNext())) {
                if (!currHandler.dealWrapper()) {
                    break;
                }
            }
            afterExecute();
            log.info("end execute task: [{}]", getJobChainName());
        } catch (Exception e) {
            log.error("execute task: [{}] error, curr handler is [{}]", getJobChainName(), currHandler == null ? "null" : currHandler.getClass().getSimpleName(), e);
            throw e;
        } finally {
            BaseJobContext.removeContent();
        }
    }

    /**
     * 第一个处理器
     */
    protected abstract Class<? extends BaseHandler> firstHandler();

    /**
     * 上下文类型
     */
    protected abstract Class<? extends BaseJobContext> contextType();

    /**
     * 任务名称
     */
    protected abstract String getJobChainName();

    /**
     * 执行前,此时上下文已经创建
     */
    protected void beforeExecute() {

    }

    /**
     * 执行后,此时上下文尚未销毁
     */
    protected void afterExecute() {

    }

}
