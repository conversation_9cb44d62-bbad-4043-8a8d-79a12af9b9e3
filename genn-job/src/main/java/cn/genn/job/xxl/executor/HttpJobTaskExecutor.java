package cn.genn.job.xxl.executor;

import cn.genn.job.xxl.model.task.JobTaskContext;
import cn.genn.job.xxl.model.task.JobTaskTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Component
public class HttpJobTaskExecutor implements JobTaskExecutor {

    @Resource
    private RestTemplate restTemplate;

    @Override
    public boolean supports(JobTaskTypeEnum taskType) {
        return JobTaskTypeEnum.HTTP.equals(taskType);
    }

    @Override
    public String execute(JobTaskContext context) throws RuntimeException {
        String url = context.getUrl();
        String method = context.getMethod();
        Map<String, Object> headers = context.getParams();
        Map<String, Object> body = context.getBody();

        // 构建请求头
        HttpHeaders httpHeaders = new HttpHeaders();
        if (headers != null) {
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                httpHeaders.set(entry.getKey(), entry.getValue().toString());
            }
        }
        if (!httpHeaders.containsKey(HttpHeaders.CONTENT_TYPE)) {
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        }
        ResponseEntity<String> responseEntity;
        // 执行请求
        if ("GET".equalsIgnoreCase(method)) {
            HttpEntity<?> requestEntity = new HttpEntity<>(httpHeaders);
            responseEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);

        } else if ("POST".equalsIgnoreCase(method)) {
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(body, httpHeaders);
            responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        } else {
            throw new UnsupportedOperationException("不支持的HTTP方法: " + method);
        }

        // 判断响应状态码
        if (!responseEntity.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("HTTP请求失败，状态码：" + responseEntity.getStatusCodeValue());
        }
        // 返回响应内容
        return responseEntity.getBody();
    }
}
