package cn.genn.job.xxl.config;

import cn.genn.database.mybatisplus.plugin.tenant.GennTenantLineHandler;

import java.util.Arrays;

/**
 * 状态相关表不需要租户处理
 *
 * <AUTHOR>
 */
public class JobTenantIgnoreStrategy implements GennTenantLineHandler.CustomIgnoreStrategy {

    private static final String[] IGNORE_TABLES = new String[]{
            "job_scheduled_tasks_log",
            "job_scheduled_tasks"};

    @Override
    public boolean ignoreTable(String tableName) {
        return Arrays.stream(IGNORE_TABLES).anyMatch(ignoreTable -> ignoreTable.equalsIgnoreCase(tableName));
    }
}
