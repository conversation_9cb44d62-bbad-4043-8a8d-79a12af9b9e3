package cn.genn.job.xxl.exception;

import cn.genn.core.exception.MessageCodeWrap;

/**
 * <AUTHOR>
 */
public enum JobErrorCode implements MessageCodeWrap {


    SCHEDULED_TASK_NOT_FOUND("201", "定时任务不存在[{0}]"),
    TASK_NAME_NOT_NULL("202", "任务名称不能为空"),
    TASK_TYPE_NOT_NULL("203", "类型不能为空"),
    CRON_NOT_NULL("204", "cron不能为空"),
    FIX_RATE_ERROR("205", "fixRate配置不正确"),
    PRIORITY_RANGE_ERROR("206", "优先级范围0~99"),
    CRON_FORMAT_ERROR("207", "cron格式不正确"),
    TASK_CONTEXT_NOT_EXIST("208", "任务内容不存在"),
    TASK_CONTEXT_ERROR("209", "任务内容参数错误"),
    TASK_STOP_UPDATE("210", "已完成任务禁止编辑"),
    TASK_PLAN_NOT_NULL("211", "执行计划为空"),
    REQ_TIME_OUT_ERROR("212", "请求超时时间超出范围"),
    ;

    private final String code;
    private final String description;

    JobErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }


    @Override
    public String getBizCode() {
        return "20";
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
