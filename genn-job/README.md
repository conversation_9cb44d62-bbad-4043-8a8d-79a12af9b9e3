# genn-job

功能描述：xxl-job链式调用，管理服务

职责范围：所有定时任务的最佳实践，不限于xxl-job

## 一. xxl-job链式调用

1. BaseHandler：处理器链路基本逻辑
2. BaseHandlerSupport：子类定时处理逻辑继承该support,需要配合BaseHandler,BaseJobContext使用
3. BaseJobContext：链式调用上下文支持
4. BaseTransactionHandlerSupport：支持事务的定时任务处理

## 二. xxl-job管理

支持对任务的增删改查，启动，停止等管理端功能

## 三.自定义定时任务
    
1. 执行频率支持两种方式:cron表达式,固定频率;
2. 执行任务支持:http请求,java方法;

### 使用须知
1. 单独在xxl-job服务配置 scheduledTaskHandler以支持定时执行生效;
2. 创建job_scheduled_tasks和job_scheduled_tasks_log表在对应项目;