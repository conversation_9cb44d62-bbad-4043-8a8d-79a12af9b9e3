CREATE TABLE `job_scheduled_tasks` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                       `name` varchar(64) NOT NULL COMMENT '任务名称',
                                       `biz_key` varchar(64) NOT NULL DEFAULT 'default' COMMENT '业务类型标记',
                                       `type` varchar(16) NOT NULL COMMENT '任务类型（CRON：表达式；FIX_RATE：固定频率）',
                                       `cron` varchar(16) DEFAULT NULL COMMENT 'cron表达式',
                                       `start_time` datetime DEFAULT NULL COMMENT '开始执行时间',
                                       `interval` bigint DEFAULT NULL COMMENT '执行频率',
                                       `unit` varchar(16) DEFAULT NULL COMMENT '频率单位（second秒，minute分，hour小时，day天）',
                                       `repeat_count` bigint DEFAULT NULL COMMENT '执行次数',
                                       `stop_time` datetime DEFAULT NULL COMMENT '截止时间',
                                       `content` varchar(1024) NOT NULL DEFAULT '' COMMENT '任务内容',
                                       `priority` int NOT NULL DEFAULT '99' COMMENT '优先级（0-99，数字越小越优先）',
                                       `status` varchar(16) NOT NULL DEFAULT 'running' COMMENT '任务状态（running,stop,finish）',
                                       `next_time` datetime DEFAULT NULL COMMENT '下次执行时间',
                                       `overlap` tinyint DEFAULT '0' COMMENT '任务是否可以重叠执行;默认false',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `create_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
                                       `create_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建者名称',
                                       `create_user_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '头像',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                       `update_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '修改人',
                                       `update_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人名称',
                                       `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除（0：未删除  1：删除）',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_biz_key` (`biz_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务表';

CREATE TABLE `job_scheduled_tasks_log` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                           `task_id` bigint NOT NULL COMMENT '任务id',
                                           `task_snapshot` text NOT NULL COMMENT '任务快照',
                                           `status` varchar(255) NOT NULL COMMENT '执行状态：0-执行中 1-成功 2-失败 3-超时',
                                           `output_result` text COMMENT '执行返回结果',
                                           `execution_time_ms` bigint DEFAULT NULL COMMENT '执行耗时（毫秒）',
                                           `execution_start_time` datetime DEFAULT NULL COMMENT '执行开始时间',
                                           `extra_info` json DEFAULT NULL COMMENT '其他日志信息',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `create_user_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
                                           `create_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人名称',
                                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                           `update_user_id` bigint NOT NULL DEFAULT '0' COMMENT '更新人',
                                           `update_user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新人名称',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_task_id` (`task_id`),
                                           KEY `idx_execution_start_tim` (`execution_start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;