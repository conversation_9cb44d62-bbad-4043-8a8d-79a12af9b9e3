# Cerebro Service 调用指南

## 概述

Cerebro Service 是一个用于与 Cerebro 工作流引擎交互的 Java 客户端，提供了完整的流式和非流式工作流执行功能。支持 SSE 事件流处理、AI 回复内容过滤和任务取消等功能。

## 主要功能

- ✅ 执行流式和非流式工作流请求
- ✅ 接收完整的 SSE 事件流
- ✅ 过滤并处理 AI 回复内容
- ✅ 任务取消功能
- ✅ 支持 API Key 和 Cookie 两种认证方式
- ✅ 完整的错误处理和日志记录

## 快速开始

### 1. 添加依赖

在你的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>cn.genn.nova</groupId>
    <artifactId>genn-ai</artifactId>
    <version>${genn.version}</version>
</dependency>
```

### 2. 创建服务实例

```java
import cn.genn.ai.cerebro.service.CerebroService;
import org.springframework.web.reactive.function.client.WebClient;

// 创建 WebClient
WebClient webClient = WebClient.builder()
    .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
    .build();

// 创建 CerebroService 实例
CerebroService cerebroService = new CerebroService(webClient, "https://cerebro-sit.genn.cn");
```

### 3. 配置认证信息

#### API Key 认证
```java
CerebroAuth auth = new CerebroAuth()
    .setType(CerebroAuth.AuthType.API_KEY)
    .setApiKey("your-api-key");
```

#### Cookie 认证
```java
CerebroAuth auth = new CerebroAuth()
    .setType(CerebroAuth.AuthType.COOKIE)
    .setCookie("your-cookie")
    .setToken("your-token"); // 可选
```

### 4. 构建请求参数

```java
CerebroRequest request = new CerebroRequest()
    .setAppId("your-app-id")           // 工作流ID（可选）
    .setChatId("demo-chat-1")          // 聊天ID
    .setTaskId("existing-task-id")     // 任务ID（可选，用于继续之前的任务）
    .setMessages(List.of(
        new CerebroMessage()
            .setRole("user")
            .setContent("你好，请介绍一下自己")
    ))
    .setVariables(Map.of("key", "value"))  // 全局变量（可选）
    .setTimeout(300);                      // 超时时间（秒）
```

## API 使用方法

### 非流式调用

#### 1. 获取完整响应
```java
CerebroNonStreamResponse response = cerebroService.executeNonStream(request, auth);
System.out.println("任务ID: " + response.getTaskId());
System.out.println("AI回复: " + response.getAiContent());
```

#### 2. 仅获取AI回复内容
```java
String aiContent = cerebroService.executeNonStreamForAiContent(request, auth);
System.out.println("AI回复: " + aiContent);
```

### 流式调用

#### 1. 获取原始响应流
```java
Flux<CerebroResponse> responseStream = cerebroService.executeStream(request, auth);
responseStream.subscribe(response -> {
    System.out.println("事件类型: " + response.getEventType());
    System.out.println("内容: " + response.getContent().data());
});
```

#### 2. 获取完整SSE事件流
```java
Flux<CerebroSseEvent> sseStream = cerebroService.executeStreamWithFullSse(request, auth);
sseStream.subscribe(event -> {
    System.out.println("事件: " + event.getEvent());
    System.out.println("数据: " + event.getData());
});
```

#### 3. 仅获取AI回复流
```java
Flux<CerebroAiResponse> aiStream = cerebroService.executeStreamForAiResponse(request, auth);
aiStream.subscribe(aiResponse -> {
    System.out.println("AI回复片段: " + aiResponse.getContent());
});
```

#### 4. 使用事件处理器
```java
Flux<CerebroSseEvent> eventStream = cerebroService.executeStreamWithHandler(request, auth, event -> {
    switch (event.getEvent()) {
        case "answer":
            System.out.println("收到AI回复: " + event.getData());
            break;
        case "workflow_started":
            System.out.println("工作流开始执行");
            break;
        case "workflow_finished":
            System.out.println("工作流执行完成");
            break;
    }
});
```

#### 5. 等待完整AI回复
```java
// 仅获取完整内容文本
String completeContent = cerebroService.executeStreamAndWaitForCompleteAiContent(request, auth);
System.out.println("完整AI回复: " + completeContent);

// 获取完整响应对象（包含元数据）
CerebroCompleteAiResponse completeResponse = cerebroService.executeStreamAndWaitForCompleteAiResponse(request, auth);
System.out.println("完整回复: " + completeResponse.getContent());
System.out.println("任务ID: " + completeResponse.getTaskId());
```

### 任务取消

```java
boolean success = cerebroService.cancelTask("app-id", "task-id", auth);
if (success) {
    System.out.println("任务取消成功");
} else {
    System.out.println("任务取消失败");
}
```

## 完整示例

```java
public class CerebroExample {
    public static void main(String[] args) {
        // 1. 创建服务
        WebClient webClient = WebClient.builder()
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build();
        CerebroService service = new CerebroService(webClient, "https://cerebro-sit.genn.cn");
        
        // 2. 配置认证
        CerebroAuth auth = new CerebroAuth()
            .setType(CerebroAuth.AuthType.API_KEY)
            .setApiKey("your-api-key");
        
        // 3. 构建请求
        CerebroRequest request = new CerebroRequest()
            .setChatId("demo-chat-1")
            .setMessages(List.of(
                new CerebroMessage()
                    .setRole("user")
                    .setContent("你好，请介绍一下自己")
            ));
        
        // 4. 执行请求
        try {
            // 非流式调用
            String aiContent = service.executeNonStreamForAiContent(request, auth);
            System.out.println("AI回复: " + aiContent);
            
            // 流式调用
            String completeContent = service.executeStreamAndWaitForCompleteAiContent(request, auth);
            System.out.println("完整回复: " + completeContent);
            
        } catch (Exception e) {
            System.err.println("调用失败: " + e.getMessage());
        }
    }
}
```

## 图片处理示例

### 处理在线图片

```java
import cn.genn.ai.cerebro.model.Content;

public class ImageProcessingExample {
    public static void main(String[] args) {
        CerebroService service = createService();
        CerebroAuth auth = createAuth();

        // 处理图片URL
        String imageUrl = "https://example.com/image.jpg";
        CerebroRequest request = new CerebroRequest()
                .setChatId("demo-chat-image")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithImages("请分析这张图片", List.of(imageUrl)))));

        // 执行请求
        String aiResponse = service.executeStreamAndWaitForCompleteAiContent(request, auth);
        System.out.println("图片分析结果: " + aiResponse);
    }
}
```

### 处理本地图片文件

```java
import java.io.File;
import java.io.IOException;

public class LocalImageExample {
    public static void processLocalImage() throws IOException {
        CerebroService service = createService();
        CerebroAuth auth = createAuth();

        File imageFile = new File("path/to/image.jpg");
        CerebroRequest request = new CerebroRequest()
                .setChatId("demo-chat-local-image")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithImageFiles("分析这个本地图片", List.of(imageFile)))));

        String result = service.executeStreamAndWaitForCompleteAiContent(request, auth);
        System.out.println("本地图片分析: " + result);
    }
}
```

## 文件处理示例

### 处理单个文档文件

```java
import cn.genn.ai.cerebro.model.Content.FileInfo;

public class FileProcessingExample {
    public static void main(String[] args) {
        CerebroService service = createService();
        CerebroAuth auth = createAuth();

        // 处理文档文件
        String fileUrl = "https://sit-genn-ai-algorithm.tos-cn-beijing.volces.com/userUpload/document.docx";
        FileInfo fileInfo = new FileInfo()
                .setUrl(fileUrl)
                .setFileName("document.docx");

        CerebroRequest request = new CerebroRequest()
                .setChatId("demo-chat-file")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithFiles("请总结这个文档的内容", List.of(fileInfo)))));

        // 流式处理文件内容
        service.executeStreamForAiResponse(request, auth)
                .doOnNext(aiResponse -> {
                    if (aiResponse.getContent() != null) {
                        System.out.print(aiResponse.getContent());
                    }
                })
                .blockLast();
    }
}
```

### 处理多个文件

```java
public class MultipleFilesExample {
    public static void processMultipleFiles() {
        CerebroService service = createService();
        CerebroAuth auth = createAuth();

        List<FileInfo> files = List.of(
                new FileInfo().setUrl("https://example.com/doc1.pdf").setFileName("报告1.pdf"),
                new FileInfo().setUrl("https://example.com/doc2.xlsx").setFileName("数据表.xlsx"),
                new FileInfo().setUrl("https://example.com/image.png").setFileName("图表.png")
        );

        CerebroRequest request = new CerebroRequest()
                .setChatId("demo-chat-multiple-files")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithFiles("请分析这些文件并给出综合报告", files))));

        String result = service.executeStreamAndWaitForCompleteAiContent(request, auth);
        System.out.println("综合分析结果: " + result);
    }
}
```

## 混合内容处理示例

### 同时处理文本、图片和文件

```java
public class MixedContentExample {
    public static void main(String[] args) {
        CerebroService service = createService();
        CerebroAuth auth = createAuth();

        // 混合处理文本、图片和文件
        List<FileInfo> mixedContent = List.of(
                // 图片文件
                new FileInfo().setUrl("https://example.com/chart.png").setFileName("数据图表.png"),
                // 文档文件
                new FileInfo().setUrl("https://example.com/report.pdf").setFileName("分析报告.pdf"),
                // Excel文件
                new FileInfo().setUrl("https://example.com/data.xlsx").setFileName("原始数据.xlsx")
        );

        CerebroRequest request = new CerebroRequest()
                .setChatId("demo-chat-mixed")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithFiles(
                                "请基于提供的图表、报告和数据，生成一份执行摘要，包括：\n" +
                                "1. 关键发现\n" +
                                "2. 数据趋势分析\n" +
                                "3. 建议措施",
                                mixedContent))));

        // 等待完整响应
        CerebroCompleteAiResponse response = service.executeStreamAndWaitForCompleteAiResponse(request, auth);
        System.out.println("任务ID: " + response.getTaskId());
        System.out.println("完整分析报告:\n" + response.getContent());
    }
}
```

## 基于CerebroTest的实际示例

### 参考测试代码中的文件处理

```java
// 基于 CerebroTest.java 中的 createRequestWithFile() 方法
public class RealWorldFileExample {
    public static void testFileProcessing() {
        CerebroService service = createService();
        CerebroAuth auth = createAuth();

        // 实际的文件URL示例（来自测试代码）
        String realFileUrl = "https://sit-genn-ai-algorithm.tos-cn-beijing.volces.com/%2FuserUpload%2Fad3ad90c-0184-4fd8-a553-0d4ed074c3cb%2F%E5%89%AF%E6%9C%AC%E5%AE%A3%E8%AE%B2%E7%A8%BF%EF%BC%88%E7%BB%88%E7%89%88%EF%BC%89.docx?X-Tos-Algorithm=TOS4-HMAC-SHA256&response-content-disposition=attachment%3Bfilename%3D%25E5%2589%25AF%25E6%259C%25AC%25E5%25AE%25A3%25E8%25AE%25B2%25E7%25A8%25BF%25EF%25BC%2588%25E7%25BB%2588%25E7%2589%2588%25EF%25BC%2589.docx&X-Tos-Credential=AKLTYmIyZGNhMGNhM2NkNGU5MTlmNmQxYjA4N2E3MjM4MTc%2F20250820%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Expires=15552000&X-Tos-Date=20250820T074413Z&X-Tos-Signature=c80eda2945517422627c46becbed70fc191e72120bc3d2ea6b664c676b8ec34e&X-Tos-SignedHeaders=host";

        CerebroRequest request = new CerebroRequest()
                .setChatId("demo-chat-2")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithFiles("总结一下", List.of(
                                new Content.FileInfo().setUrl(realFileUrl)
                        )))));

        // 执行请求并处理响应
        CerebroNonStreamResponse execute = service.execute(request, auth);
        System.out.println("taskID: " + execute.getNewVariables().getTaskId());
        System.out.println("chatID: " + execute.getId());
        System.out.println("AI回复内容: " + execute.getChoices().getFirst().getMessage().getContent());
    }
}
```

### 参考测试代码中的图片处理

```java
// 基于 CerebroTest.java 中的 createRequestWithImage() 方法
public class RealWorldImageExample {
    public static void testImageProcessing() {
        CerebroService service = createService();
        CerebroAuth auth = createAuth();

        // 实际的图片URL示例（来自测试代码）
        String realImageUrl = "https://sit-genn-ai-algorithm.tos-cn-beijing.volces.com/%2FuserUpload%2F538a85db-b54a-488f-936d-21ca4ebaeb4a%2FDoodle%E7%AE%AD%E5%A4%B4%E5%9B%BE%E6%A0%87.png?X-Tos-Algorithm=TOS4-HMAC-SHA256&response-content-disposition=attachment%3Bfilename%3DDoodle%25E7%25AE%25AD%25E5%25A4%25B4%25E5%259B%25BE%25E6%25A0%2587.png&X-Tos-Credential=AKLTYmIyZGNhMGNhM2NkNGU5MTlmNmQxYjA4N2E3MjM4MTc%2F20250820%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Expires=15552000&X-Tos-Date=20250820T071146Z&X-Tos-Signature=b71a304fc3feab8f62563b3ce8f6778c3590299eee6e47006415c66410411904&X-Tos-SignedHeaders=host";

        CerebroRequest request = new CerebroRequest()
                .setChatId("demo-chat-3")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithImages("总结一下文档", List.of(realImageUrl)))));

        String result = service.executeStreamAndWaitForCompleteAiContent(request, auth);
        System.out.println("图片分析结果: " + result);
    }
}
```

## 配置参数说明

### CerebroRequest 参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| appId | String | 否 | 工作流ID |
| chatId | String | 是 | 聊天ID，同一chatId下的消息会关联到同一对话 |
| taskId | String | 否 | 任务ID，用于继续之前的任务 |
| messages | List<CerebroMessage> | 是 | 消息列表 |
| variables | Map<String, String> | 否 | 全局变量 |
| responseChatItemId | String | 否 | AI回复的dataId |
| detail | Boolean | 否 | 是否返回详细信息，默认true |
| stream | Boolean | 否 | 是否流式返回，默认true |
| timeout | Integer | 否 | 超时时间（秒），默认300 |

### CerebroMessage 参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| dataId | String | 否 | 数据ID |
| role | String | 是 | 角色（user/assistant/system） |
| content | Object | 是 | 消息内容 |
| hideInUI | Boolean | 否 | 是否在UI中隐藏，默认false |

### CerebroAuth 参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| type | AuthType | 是 | 认证类型（API_KEY/COOKIE） |
| apiKey | String | API_KEY时必填 | API密钥 |
| cookie | String | COOKIE时必填 | Cookie值 |
| token | String | 否 | Token值 |

## 注意事项

1. **超时设置**: 建议根据工作流复杂度合理设置超时时间
2. **错误处理**: 建议使用 try-catch 捕获异常并进行适当处理
3. **资源管理**: 流式调用时注意及时释放资源
4. **认证安全**: API Key 和 Cookie 等敏感信息请妥善保管
5. **并发控制**: 大量并发请求时注意控制请求频率

## 常见问题

### Q: 如何处理超时？
A: 可以通过 `request.setTimeout()` 设置超时时间，或在流式调用中使用 `.timeout()` 操作符。

### Q: 如何获取任务ID？
A: 任务ID会在响应中返回，可通过 `response.getTaskId()` 获取。

### Q: 如何继续之前的任务？
A: 在请求中设置 `taskId` 参数即可继续之前的任务执行。

### Q: 流式和非流式调用的区别？
A: 流式调用可以实时接收响应数据，适合长时间执行的工作流；非流式调用等待完整结果返回，适合快速响应的场景。
