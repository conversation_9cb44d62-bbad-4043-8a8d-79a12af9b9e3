package cn.genn.ai.cerebro;

import cn.genn.ai.cerebro.model.*;
import cn.genn.ai.cerebro.service.CerebroService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CerebroTest {
    
    public static void testExecute() {
        // 初始化服务
        CerebroService service = createService();
        CerebroAuth auth = createAuth();
        CerebroRequest request = createRequestWithFile();

        // 执行请求并处理响应
        CerebroNonStreamResponse execute = service.execute(request, auth);
        // 打印响应内容
        System.out.println("taskID: " + execute.getNewVariables().getTaskId());
        System.out.println("chatID: " + execute.getId());
        System.out.println("AI回复内容: " + execute.getChoices().getFirst().getMessage().getContent());
        System.out.println("响应数据: " + execute.getResponseData());
        System.out.println("新变量: " + execute.getNewVariables());
    }
    
    public static void testExecuteStream() {
        // 初始化服务
        CerebroService service = createService();
        CerebroAuth auth = createAuth();
        CerebroRequest request = createRequest();

        // 执行流式请求并处理响应
        service.executeStream(request, auth)
                .doOnNext(response -> {
                    // 处理每条响应
                    System.out.println("Received response: " + response);
                })
                .blockLast();
    }
    
    public static void testExecuteStreamForAIResponse() {
        // 初始化服务
        CerebroService service = createService();
        CerebroAuth auth = createAuth();
        CerebroRequest request = createRequest();

        // 简单处理流式响应
        service.executeStreamForAiResponse(request, auth)
                .doOnNext(data -> {
                    // 处理解析后的数据
                    System.out.println("Received thinking: " + data.getAiReasoningContent());
                    System.out.println("Received AI response: " + data.getAiTextContent());
                    
                })
                .blockLast();
    }
    
    public static void testExecuteStreamWithHandler() {
        // 初始化服务
        CerebroService service = createService();
        CerebroAuth auth = createAuth();
        CerebroRequest request = createRequest();

        // 使用事件处理器处理流式响应
        service.executeStreamWithHandler(request, auth, event -> {
            switch (event.getEventType()) {
                case WORKFLOW_STARTED:
                    System.out.println("工作流开始");
                    break;
                case WORKFLOW_PROGRESS:
                    System.out.println("进度更新: " + event.getData());
                    break;
                case ANSWER:
                case FAST_ANSWER:
                    if (event.getOpenAiData() != null) {
                        String content = event.getOpenAiData().getChoices().getFirst().getDelta().getContent();
                        if (content != null) {
                            System.out.print(content);
                        }
                    }
                    break;
                case WORKFLOW_COMPLETED:
                    System.out.println("\n工作流完成");
                    break;
                default:
                    System.out.println("其他事件: " + event.getEventType());
            }
        }).blockLast();
    }
    
    public void testExecuteStreamForAiResponse() {
        // 初始化服务
        CerebroService service = createService();
        CerebroAuth auth = createAuth();
        CerebroRequest request = createRequest();

        // 仅处理 AI 回复
        StringBuilder fullResponse = new StringBuilder();
        service.executeStreamForAiResponse(request, auth)
                .doOnNext(aiResponse -> {
                    if (aiResponse.getContent() != null) {
                        fullResponse.append(aiResponse.getContent());
                        System.out.print(aiResponse.getContent());
                    }
                })
                .doOnComplete(() -> System.out.println("\n完整回复: " + fullResponse))
                .blockLast();
    }
    
    public static void testExecuteStreamWaitForAiResponse() {
        // 初始化服务
        CerebroService service = createService();
        CerebroAuth auth = createAuth();
        CerebroRequest request = createRequest();

        // 等待 AI 回复完成
        String fullResponse = service.executeStreamAndWaitForCompleteAiContent(request, auth);
        System.out.println("完整回复: " + fullResponse);
    }
    
    public static void testExecuteAllStreamWaitForAiResponse() {
        // 初始化服务
        CerebroService service = createService();
        CerebroAuth auth = createAuth();
        CerebroRequest request = createRequest();

        // 等待 AI 回复完成，设置超时时间为 10 秒
        CerebroCompleteAiResponse cerebroCompleteAiResponse = service.executeStreamAndWaitForCompleteAiResponse(request, auth);
        System.out.println("完整回复: " + cerebroCompleteAiResponse);
    }
    
    

    public static void main(String[] args) {
         testExecute();
//         testExecuteStream();
//         testExecuteStreamForAIResponse();
//         testExecuteStreamWithHandler();
//         testExecuteStreamWaitForAiResponse();
//         testExecuteAllStreamWaitForAiResponse();
    }

    
    
    private static CerebroService createService() {
        WebClient webClient = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
        return new CerebroService(webClient, "https://cerebro-sit.genn.cn");
    }

    private static CerebroAuth createAuth() {
        return new CerebroAuth()
                .setType(CerebroAuth.AuthType.API_KEY)
                .setApiKey("gennai-bE4rG0s8wegIj4V7obug3jXT3Ejj3VHdpL8NA2NpvF4bC2J0qcEhw8Gu9Pat4hsI");
    }
    
    private static CerebroRequest createRequest() {
        return new CerebroRequest()
                .setChatId("demo-chat-1")
                .setMessages(List.of(new CerebroMessage()
                        .setContent("这是啥")
                ));
    }
    
    private static CerebroRequest createRequestWithFile() {
        return new CerebroRequest()
                .setChatId("demo-chat-2")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithFiles("总结一下", List.of(new Content.FileInfo().setUrl("https://sit-genn-ai-algorithm.tos-cn-beijing.volces.com/%2FuserUpload%2Fad3ad90c-0184-4fd8-a553-0d4ed074c3cb%2F%E5%89%AF%E6%9C%AC%E5%AE%A3%E8%AE%B2%E7%A8%BF%EF%BC%88%E7%BB%88%E7%89%88%EF%BC%89.docx?X-Tos-Algorithm=TOS4-HMAC-SHA256&response-content-disposition=attachment%3Bfilename%3D%25E5%2589%25AF%25E6%259C%25AC%25E5%25AE%25A3%25E8%25AE%25B2%25E7%25A8%25BF%25EF%25BC%2588%25E7%25BB%2588%25E7%2589%2588%25EF%25BC%2589.docx&X-Tos-Credential=AKLTYmIyZGNhMGNhM2NkNGU5MTlmNmQxYjA4N2E3MjM4MTc%2F20250820%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Expires=15552000&X-Tos-Date=20250820T074413Z&X-Tos-Signature=c80eda2945517422627c46becbed70fc191e72120bc3d2ea6b664c676b8ec34e&X-Tos-SignedHeaders=host"))))
                ));
    }

    private static CerebroRequest createRequestWithImage() {
        return new CerebroRequest()
                .setChatId("demo-chat-3")
                .setMessages(List.of(new CerebroMessage()
                        .setContent(Content.textWithImages("总结一下文档", List.of("https://sit-genn-ai-algorithm.tos-cn-beijing.volces.com/%2FuserUpload%2F538a85db-b54a-488f-936d-21ca4ebaeb4a%2FDoodle%E7%AE%AD%E5%A4%B4%E5%9B%BE%E6%A0%87.png?X-Tos-Algorithm=TOS4-HMAC-SHA256&response-content-disposition=attachment%3Bfilename%3DDoodle%25E7%25AE%25AD%25E5%25A4%25B4%25E5%259B%25BE%25E6%25A0%2587.png&X-Tos-Credential=AKLTYmIyZGNhMGNhM2NkNGU5MTlmNmQxYjA4N2E3MjM4MTc%2F20250820%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Expires=15552000&X-Tos-Date=20250820T071146Z&X-Tos-Signature=b71a304fc3feab8f62563b3ce8f6778c3590299eee6e47006415c66410411904&X-Tos-SignedHeaders=host")))
                ));
    }
}
