package cn.genn.ai.tools.model;

import cn.genn.core.utils.jackson.JsonUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ToolSchemaTest {

    public static void main(String[] args) {
        // 原始用法示例
        ToolInputSchema schema1 = ToolInputSchemaBuilder.object()
                .string("orderId", p -> p.description("订单号").example("ORD-2025-001"))
                .enumType("status", List.of("OPEN", "CLOSED", "PENDING"), p -> p.description("状态").defaultValue("OPEN"))
                .integer("page", p -> p.minimum(1).defaultValue(1))
                .required("orderId")
                .build();

        System.out.println("=== 原始用法示例 ===");
        System.out.println(JsonUtils.toJson(schema1, true));

        // 便捷方法示例
        ToolInputSchema schema2 = ToolInputSchemaBuilder.object()
                .requiredString("orderId", p -> p.description("订单号").example("ORD-2025-001"))
                .optionalEnum("status", List.of("OPEN", "CLOSED", "PENDING"), p -> p.description("状态").defaultValue("OPEN"))
                .optionalInteger("page", p -> p.minimum(1).defaultValue(1))
                .stringArray("tags", p -> p.description("标签列表"))
                .requiredObject("user", p -> p.objectProps(sub ->
                        sub.requiredString("name", prop -> prop.description("用户名"))
                                .optionalString("email", prop -> prop.format("email"))
                ))
                .build();

        System.out.println("\n=== 便捷方法示例 ===");
        System.out.println(JsonUtils.toJson(schema2, true));
    }
}
