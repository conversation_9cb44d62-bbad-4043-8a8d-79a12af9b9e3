package cn.genn.ai.tools;

import cn.genn.ai.cerebro.service.CerebroService;
import cn.genn.ai.tools.model.Tool;
import cn.genn.ai.tools.model.ToolInputSchema;
import cn.genn.ai.tools.model.ToolInputSchemaBuilder;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

/**
 * Cerebro工具调用测试类
 * 测试CerebroToolCallback与Spring AI ChatClient的集成
 *
 * <AUTHOR>
 */
public class CerebroToolCallbackTest {

    // 测试配置
    private static final String CEREBRO_URL = "https://cerebro-sit.genn.cn";
    private static final String API_KEY = "gennai-bE4rG0s8wegIj4V7obug3jXT3Ejj3VHdpL8NA2NpvF4bC2J0qcEhw8Gu9Pat4hsI";
    private static final String APP_ID = "688b24190ad52e3e2cb11a81"; // 替换为实际的工作流ID
    private static final String OPENAI_API_KEY = "sk-xxx"; // 替换为实际的OpenAI API Key
    private static final String OPENAI_BASE_URL = "https://api.xxx.com"; // 替换为实际基础URL
    private static final String MODEL = "gpt-4o-mini";                       // 替换为实际的模型

    public static void main(String[] args) {
        System.out.println("=== Cerebro工具调用测试开始 ===");

        try {
            // 测试1: 基本工具调用功能
            testBasicToolCall();

            // 测试2: 与ChatClient集成测试
            testChatClientIntegration();

            // 测试3: 工具定义测试
            testToolDefinition();
            
            // 测试4: 流式工具调用测试
            testStreamToolCall();

            System.out.println("=== 所有测试完成 ===");
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
        }
    }
    
    public static void testStreamToolCall() {
        // 创建CerebroService
        CerebroService cerebroService = createCerebroService();

        // 创建工具定义
        Tool tool = createTestTool();
        // 创建CerebroToolCallback
        CerebroToolCallback toolCallback = new CerebroToolCallback(
                APP_ID, API_KEY, tool, cerebroService
        );

        // 测试工具输入转换
        String testInput = "{\"input\": \"猫\"}";
        System.out.println("测试输入: " + testInput);

        // 调用工具
        toolCallback.callStream(testInput, null).map(s -> {
            System.out.print(s);
            return s;
        }).blockLast();
    }

    /**
     * 测试基本的工具调用功能
     */
    public static void testBasicToolCall() {
        System.out.println("\n--- 测试1: 基本工具调用功能 ---");

        try {
            // 创建CerebroService
            CerebroService cerebroService = createCerebroService();

            // 创建工具定义
            Tool tool = createTestTool();

            // 创建CerebroToolCallback
            CerebroToolCallback toolCallback = new CerebroToolCallback(
                    APP_ID, API_KEY, tool, cerebroService
            );

            // 测试工具输入转换
            String testInput = "{\"input\": \"猫\"}";
            System.out.println("测试输入: " + testInput);

            // 调用工具
            try {
                String result = toolCallback.call(testInput);
                System.out.println("工具调用结果: " + result);
            } catch (Exception e) {
                System.out.println("工具调用失败（预期，因为使用的是测试工作流ID）: " + e.getMessage());
            }

            // 测试工具定义
            var toolDefinition = toolCallback.getToolDefinition();
            System.out.println("工具名称: " + toolDefinition.name());
            System.out.println("工具描述: " + toolDefinition.description());

            System.out.println("✓ 基本工具调用功能测试完成");

        } catch (Exception e) {
            System.err.println("✗ 基本工具调用功能测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试与ChatClient的集成
     */
    public static void testChatClientIntegration() {
        System.out.println("\n--- 测试2: ChatClient集成测试 ---");

        try {
            // 创建ChatClient（使用OpenAI）
            ChatClient chatClient = createChatClient();

            // 创建CerebroService和工具
            CerebroService cerebroService = createCerebroService();
            Tool tool = createTestTool();

            // 创建CerebroToolCallback
            CerebroToolCallback toolCallback = new CerebroToolCallback(
                    APP_ID, API_KEY, tool, cerebroService
            );

            // 使用ChatClient进行对话，包含工具调用
            System.out.println("创建包含工具的ChatClient...");

            var chatResponse = chatClient.prompt()
                    .user("我期望知道狗的信息")
                    .toolCallbacks(toolCallback) 
                    .call()
                    .content();

            System.out.println("ChatClient响应: " + chatResponse);
            System.out.println("✓ ChatClient集成测试完成");

        } catch (Exception e) {
            System.err.println("✗ ChatClient集成测试失败: " + e.getMessage());
            System.err.println("注意: 此测试需要有效的OpenAI API Key");
        }
    }

    /**
     * 测试工具定义功能
     */
    public static void testToolDefinition() {
        System.out.println("\n--- 测试3: 工具定义测试 ---");

        try {
            // 创建工具定义
            Tool tool = createTestTool();
            CerebroService cerebroService = createCerebroService();

            CerebroToolCallback toolCallback = new CerebroToolCallback(
                    APP_ID, API_KEY, tool, cerebroService
            );

            // 测试工具定义的各个方面
            var toolDefinition = toolCallback.getToolDefinition();

            System.out.println("工具定义详情:");
            System.out.println("- 名称: " + toolDefinition.name());
            System.out.println("- 描述: " + toolDefinition.description());
            System.out.println("- 输入Schema: " + toolDefinition.inputSchema());

            System.out.println("✓ 工具定义测试完成");

        } catch (Exception e) {
            System.err.println("✗ 工具定义测试失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建CerebroService实例
     */
    private static CerebroService createCerebroService() {
        WebClient webClient = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
        return new CerebroService(webClient, CEREBRO_URL);
    }

    /**
     * 创建ChatClient实例
     */
    private static ChatClient createChatClient() {
        OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl( OPENAI_BASE_URL )
                .apiKey(OPENAI_API_KEY)
                .build();
        OpenAiChatModel chatModel = OpenAiChatModel.builder()
                .defaultOptions(OpenAiChatOptions.builder().model(MODEL).build())
                .openAiApi(openAiApi)
                .build();
        return ChatClient.builder(chatModel).build();
    }

    /**
     * 创建测试用的工具定义
     */
    private static Tool createTestTool() {
        ToolInputSchema toolInputSchema = ToolInputSchemaBuilder
                .object()
                .string("input", p -> p.description("动物名称"))
                .build();

        return new Tool(
                "animal_search",
                "查询指定动物的信息",
                toolInputSchema
        );
    }

}
