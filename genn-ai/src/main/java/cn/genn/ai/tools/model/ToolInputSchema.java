package cn.genn.ai.tools.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_ABSENT)
@JsonIgnoreProperties(ignoreUnknown = true)
public record ToolInputSchema(
      @JsonProperty("type") String type,
      @JsonProperty("properties") Map<String, Object> properties,
      @JsonProperty("required") List<String> required,
      @JsonProperty("additionalProperties") Boolean additionalProperties,
      @JsonProperty("$defs") Map<String, Object> defs,
      @JsonProperty("definitions") Map<String, Object> definitions) {
}