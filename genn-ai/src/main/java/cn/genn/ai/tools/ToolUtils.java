package cn.genn.ai.tools;

import cn.genn.ai.tools.model.Tool;
import cn.genn.ai.tools.model.ToolInputSchema;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class ToolUtils {

    /**
     * 解析工具输入,使用Tool定义的schema进行验证和类型转换
     * @param toolInput 工具输入JSON字符串
     * @param tool 工具定义
     */
    public static Map<String, Object> parseToolInput(String toolInput, Tool tool) {
        try {
            // 首先尝试解析为JSON对象
            Map<String, Object> rawInput = JsonUtils.parseToObjectMap(toolInput);

            // 获取工具的输入模式定义
            ToolInputSchema inputSchema = tool.inputSchema();
            if (inputSchema == null || inputSchema.properties() == null) {
                log.debug("No input schema defined, returning raw input");
                return rawInput;
            }

            // 根据schema验证和转换输入
            return validateAndConvertInput(rawInput, inputSchema);

        } catch (Exception e) {
            log.warn("Failed to parse tool input as JSON: {}, error: {}", toolInput, e.getMessage());

            // 如果JSON解析失败，尝试根据schema的单一字段处理
            ToolInputSchema inputSchema = tool.inputSchema();
            if (inputSchema != null && inputSchema.properties() != null) {
                Map<String, Object> properties = inputSchema.properties();

                // 如果schema只定义了一个字段，将整个输入作为该字段的值
                if (properties.size() == 1) {
                    String fieldName = properties.keySet().iterator().next();
                    Object propertyDef = properties.get(fieldName);
                    Object convertedValue = convertValueToType(toolInput, propertyDef);
                    return Map.of(fieldName, convertedValue);
                }
            }

            // 默认情况下，使用"input"作为键
            return Map.of("input", toolInput);
        }
    }

    /**
     * 根据schema验证和转换输入数据
     */
    private static Map<String, Object> validateAndConvertInput(Map<String, Object> rawInput, ToolInputSchema inputSchema) {
        Map<String, Object> validatedInput = new HashMap<>();
        Map<String, Object> properties = inputSchema.properties();
        List<String> requiredFields = inputSchema.required();

        for (Map.Entry<String, Object> entry : properties.entrySet()) {
            String fieldName = entry.getKey();
            Object propertyDef = entry.getValue();

            Object rawValue = rawInput.get(fieldName);

            // 检查必填字段
            boolean isRequired = requiredFields != null && requiredFields.contains(fieldName);
            if (rawValue == null && isRequired) {
                throw new IllegalArgumentException("Required field '" + fieldName + "' is missing");
            }

            // 如果字段有值，进行类型转换
            if (rawValue != null) {
                try {
                    Object convertedValue = convertValueToType(rawValue, propertyDef);
                    validatedInput.put(fieldName, convertedValue);
                } catch (Exception e) {
                    String expectedType = getPropertyType(propertyDef);
                    log.error("Failed to convert field '{}' to type '{}': {}", fieldName, expectedType, e.getMessage());
                    throw new IllegalArgumentException("Invalid type for field '" + fieldName + "': expected " + expectedType + ", got " + rawValue.getClass().getSimpleName());
                }
            } else {
                // 可选字段使用默认值
                Object defaultValue = getPropertyDefaultValue(propertyDef);
                if (defaultValue != null) {
                    validatedInput.put(fieldName, defaultValue);
                }
            }
        }

        // 添加schema中未定义但输入中存在的字段（保持向后兼容）
        for (Map.Entry<String, Object> entry : rawInput.entrySet()) {
            if (!properties.containsKey(entry.getKey())) {
                log.debug("Adding undefined field '{}' to input", entry.getKey());
                validatedInput.put(entry.getKey(), entry.getValue());
            }
        }

        return validatedInput;
    }

    /**
     * 将值转换为指定类型
     */
    private static Object convertValueToType(Object value, Object propertyDef) {
        if (value == null) {
            return null;
        }

        String targetType = getPropertyType(propertyDef);
        if (targetType == null) {
            log.warn("No type defined for property, returning value as-is");
            return value;
        }

        targetType = targetType.toLowerCase();

        return switch (targetType) {
            case "string" -> convertToString(value);
            case "integer", "int" -> convertToInteger(value);
            case "number", "double", "float" -> convertToNumber(value);
            case "boolean" -> convertToBoolean(value);
            case "array" -> convertToArray(value);
            case "object" -> convertToObject(value);
            default -> {
                log.warn("Unknown type '{}', returning value as-is", targetType);
                yield value;
            }
        };
    }

    private static String convertToString(Object value) {
        return value.toString();
    }

    private static Integer convertToInteger(Object value) {
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String str) {
            if (NumberUtil.isInteger(str)) {
                return Integer.parseInt(str);
            }
        }
        throw new IllegalArgumentException("Cannot convert '" + value + "' to integer");
    }

    private static Double convertToNumber(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        if (value instanceof String str) {
            if (NumberUtil.isNumber(str)) {
                return Double.parseDouble(str);
            }
        }
        throw new IllegalArgumentException("Cannot convert '" + value + "' to number");
    }

    private static Boolean convertToBoolean(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            String str = ((String) value).toLowerCase();
            return "true".equals(str) || "yes".equals(str) || "1".equals(str);
        }
        if (value instanceof Number) {
            return ((Number) value).intValue() != 0;
        }
        throw new IllegalArgumentException("Cannot convert '" + value + "' to boolean");
    }

    @SuppressWarnings("unchecked")
    private static List<Object> convertToArray(Object value) {
        if (value instanceof List) {
            return (List<Object>) value;
        }
        if (value instanceof String) {
            try {
                // 尝试解析JSON数组
                return JsonUtils.parseToList((String) value, Object.class);
            } catch (Exception e) {
                // 如果不是JSON数组，按逗号分割
                return Arrays.asList(((String) value).split(","));
            }
        }
        throw new IllegalArgumentException("Cannot convert '" + value + "' to array");
    }

    @SuppressWarnings("unchecked")
    private static Map<String, Object> convertToObject(Object value) {
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        if (value instanceof String) {
            try {
                return JsonUtils.parseToObjectMap((String) value);
            } catch (Exception e) {
                throw new IllegalArgumentException("Cannot convert '" + value + "' to object: " + e.getMessage());
            }
        }
        throw new IllegalArgumentException("Cannot convert '" + value + "' to object");
    }

    /**
     * 从属性定义中获取类型
     */
    private static String getPropertyType(Object propertyDef) {
        if (propertyDef instanceof Map<?, ?> propertyMap) {
            Object typeObj = propertyMap.get("type");
            if (typeObj instanceof String) {
                return (String) typeObj;
            }
        }
        return null;
    }

    /**
     * 从属性定义中获取默认值
     */
    private static Object getPropertyDefaultValue(Object propertyDef) {
        if (propertyDef instanceof Map<?, ?> propertyMap) {
            return propertyMap.get("default");
        }
        return null;
    }
}
