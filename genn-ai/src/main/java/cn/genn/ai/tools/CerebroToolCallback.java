package cn.genn.ai.tools;

import cn.genn.ai.cerebro.model.CerebroAiResponse;
import cn.genn.ai.cerebro.model.CerebroAuth;
import cn.genn.ai.cerebro.model.CerebroMessage;
import cn.genn.ai.cerebro.model.CerebroRequest;
import cn.genn.ai.cerebro.service.CerebroService;
import cn.genn.ai.tools.model.Tool;
import cn.genn.core.utils.jackson.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.definition.DefaultToolDefinition;
import org.springframework.ai.tool.definition.ToolDefinition;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * cerebro平台的工具封装
 * <AUTHOR>
 */
@Slf4j
@Data
public class CerebroToolCallback implements StreamToolCallback {

    /**
     * 需要调用的工作流id
     */
    private String appId;

    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 工具定义
     */
    private Tool tool;

    /**
     * Cerebro服务实例
     */
    private CerebroService cerebroService;
    
    
    public CerebroToolCallback(String appId, String apiKey, Tool tool, CerebroService cerebroService) {
        this.appId = appId;
        this.apiKey = apiKey;
        this.tool = tool;
        this.cerebroService = cerebroService;
    }


    @Override
    public ToolDefinition getToolDefinition() {
        return DefaultToolDefinition.builder().name(this.tool.name()).description(this.tool.description()).inputSchema(JsonUtils.toJson(this.tool.inputSchema())).build();
    }

    @Override
    public String call(String toolInput) {
        CerebroAuth auth = new CerebroAuth().setType(CerebroAuth.AuthType.API_KEY).setApiKey(apiKey);
        CerebroRequest request = new CerebroRequest()
                .setAppId(appId)
                .setMessages(List.of(new CerebroMessage().setContent("")))
                .setVariables(convertToolInputToVariables(toolInput));
        return cerebroService.executeStreamAndWaitForCompleteAiContent(request, auth);
    }

    @Override
    public String call(String toolInput, ToolContext tooContext) {
        return call(toolInput);
    }

    @Override
    public Flux<String> callStream(String toolInput, ToolContext toolContext) {
        CerebroAuth auth = new CerebroAuth().setType(CerebroAuth.AuthType.API_KEY).setApiKey(apiKey);
        CerebroRequest request = new CerebroRequest()
                .setAppId(appId)
                .setMessages(List.of(new CerebroMessage().setContent("")))
                .setVariables(convertToolInputToVariables(toolInput));
        return cerebroService.executeStreamForAiResponse(request, auth).mapNotNull(CerebroAiResponse::getAiTextContent);   
    }

    /**
     * 将toolInput JSON字符串转换为CerebroRequest的variables
     * 根据toolInputSchema中的类型定义进行类型转换
     *
     * @param toolInput 模型返回的工具调用参数JSON字符串
     * @return 转换后的variables Map，所有值都转换为String类型
     */
    private Map<String, String> convertToolInputToVariables(String toolInput) {
        Map<String, String> variables = new HashMap<>();

        try {
            // 解析toolInput JSON字符串为Map
            Map<String, Object> inputMap = JsonUtils.parseToMap(toolInput, String.class, Object.class);
            if (inputMap == null || inputMap.isEmpty()) {
                log.debug("toolInput is empty or null: {}", toolInput);
                return variables;
            }

            // 获取schema中的属性定义
            Map<String, Object> properties = tool.inputSchema().properties();
            if (properties == null) {
                log.warn("Tool input schema properties is null for tool: {}", tool.name());
                // 如果没有schema定义，直接转换所有值为字符串
                inputMap.forEach((key, value) -> variables.put(key, convertValueToString(value)));
                return variables;
            }

            // 根据schema定义进行类型转换
            inputMap.forEach((key, value) -> {
                Object propertyDef = properties.get(key);
                String convertedValue = convertValueBySchema(value, propertyDef);
                variables.put(key, convertedValue);
            });

            log.debug("Converted toolInput to variables: {} -> {}", toolInput, variables);

        } catch (Exception e) {
            log.error("Failed to convert toolInput to variables: {}", toolInput, e);
            // 发生异常时，尝试简单转换
            try {
                Map<String, Object> inputMap = JsonUtils.parseToMap(toolInput, String.class, Object.class);
                if (inputMap != null) {
                    inputMap.forEach((key, value) -> variables.put(key, convertValueToString(value)));
                }
            } catch (Exception ex) {
                log.error("Failed to fallback convert toolInput: {}", toolInput, ex);
            }
        }

        return variables;
    }

    /**
     * 根据schema属性定义转换值
     *
     * @param value 原始值
     * @param propertyDef schema中的属性定义
     * @return 转换后的字符串值
     */
    private String convertValueBySchema(Object value, Object propertyDef) {
        if (value == null) {
            return null;
        }

        if (propertyDef == null) {
            return convertValueToString(value);
        }

        try {
            // 如果propertyDef是Map，尝试获取type信息
            if (propertyDef instanceof Map<?, ?> propertyMap) {
                Object typeObj = propertyMap.get("type");
                if (typeObj instanceof String type) {
                    return convertValueByType(value, type);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to convert value by schema, fallback to string conversion. Value: {}, PropertyDef: {}", value, propertyDef, e);
        }

        return convertValueToString(value);
    }

    /**
     * 根据JSON Schema类型转换值
     *
     * @param value 原始值
     * @param type JSON Schema类型 (string, number, integer, boolean, array, object)
     * @return 转换后的字符串值
     */
    private String convertValueByType(Object value, String type) {
        if (value == null) {
            return null;
        }

        try {
            return switch (type.toLowerCase()) {
                case "string" -> convertValueToString(value);
                case "number", "integer" -> {
                    // 确保数字类型的正确转换
                    if (value instanceof Number) {
                        yield value.toString();
                    } else if (value instanceof String) {
                        // 验证字符串是否为有效数字
                        try {
                            if ("integer".equalsIgnoreCase(type)) {
                                Long.parseLong((String) value);
                            } else {
                                Double.parseDouble((String) value);
                            }
                            yield (String) value;
                        } catch (NumberFormatException e) {
                            log.warn("Invalid number format for type {}: {}", type, value);
                            yield convertValueToString(value);
                        }
                    }
                    yield convertValueToString(value);
                }
                case "boolean" -> {
                    // 布尔类型转换
                    if (value instanceof Boolean) {
                        yield value.toString();
                    } else if (value instanceof String) {
                        String strValue = ((String) value).toLowerCase();
                        if ("true".equals(strValue) || "false".equals(strValue)) {
                            yield strValue;
                        }
                    }
                    yield convertValueToString(value);
                }
                case "array" -> {
                    // 数组类型转换为JSON字符串
                    if (value instanceof List<?> || value.getClass().isArray()) {
                        yield JsonUtils.toJson(value);
                    }
                    yield convertValueToString(value);
                }
                case "object" -> {
                    // 对象类型转换为JSON字符串
                    if (value instanceof Map<?, ?> ||
                            (!value.getClass().isPrimitive() &&
                                    !(value instanceof String) &&
                                    !(value instanceof Number) &&
                                    !(value instanceof Boolean))) {
                        yield JsonUtils.toJson(value);
                    }
                    yield convertValueToString(value);
                }
                default -> {
                    log.debug("Unknown schema type: {}, using string conversion", type);
                    yield convertValueToString(value);
                }
            };
        } catch (Exception e) {
            log.warn("Failed to convert value by type {}, fallback to string conversion. Value: {}", type, value, e);
            return convertValueToString(value);
        }
    }

    /**
     * 将任意值转换为字符串
     *
     * @param value 原始值
     * @return 字符串值
     */
    private String convertValueToString(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            return (String) value;
        }

        // 对于复杂对象，转换为JSON字符串
        if (value instanceof Map<?, ?> || value instanceof List<?> || value.getClass().isArray()) {
            try {
                return JsonUtils.toJson(value);
            } catch (Exception e) {
                log.warn("Failed to convert complex object to JSON, using toString(). Value: {}", value, e);
                return value.toString();
            }
        }

        return value.toString();
    }
}
