package cn.genn.ai.tools.model;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;

/**
 * A fluent builder for creating {@link ToolInputSchema} (JSON Schema for tool input).
 *
 * Typical usage:
 *
 * ToolInputSchema schema = ToolInputSchemaBuilder.object()
 *     .string("orderId", p -> p.description("工单号").example("ORD-2025-001"))
 *     .enumType("status", List.of("OPEN", "CLOSED", "PENDING"), p -> p.description("状态").defaultValue("OPEN"))
 *     .integer("page", p -> p.minimum(1).defaultValue(1))
 *     .integer("pageSize", p -> p.minimum(1).maximum(100).defaultValue(20))
 *     .required("orderId")
 *     .additionalProperties(false)
 *     .build();
 *
 * Enhanced with convenience methods:
 *
 * ToolInputSchema schema = ToolInputSchemaBuilder.object()
 *     .requiredString("orderId", p -> p.description("工单号").example("ORD-2025-001"))
 *     .optionalEnum("status", List.of("OPEN", "CLOSED", "PENDING"), p -> p.description("状态").defaultValue("OPEN"))
 *     .optionalInteger("page", p -> p.minimum(1).defaultValue(1))
 *     .stringArray("tags", p -> p.description("标签列表"))
 *     .build();
 *     
 * <AUTHOR> 
 */
public class ToolInputSchemaBuilder {

    private final String type;
    private final Map<String, Object> properties;
    private final Set<String> required; // 使用Set避免重复
    private Boolean additionalProperties;
    private Map<String, Object> defs;
    private Map<String, Object> definitions;

    private ToolInputSchemaBuilder(String type) {
        this.type = type;
        this.properties = new LinkedHashMap<>();
        this.required = new LinkedHashSet<>(); // 使用LinkedHashSet保持顺序并避免重复
        this.additionalProperties = Boolean.FALSE; // 默认不允许额外字段
    }

    public static ToolInputSchemaBuilder object() {
        return new ToolInputSchemaBuilder("object");
    }

    // -------------- Top-level options --------------

    public ToolInputSchemaBuilder additionalProperties(boolean value) {
        this.additionalProperties = value;
        return this;
    }

    public ToolInputSchemaBuilder required(String... names) {
        if (names != null) {
            for (String n : names) {
                if (n != null && !n.isBlank()) {
                    this.required.add(n); // Set会自动去重
                }
            }
        }
        return this;
    }

    public ToolInputSchemaBuilder addDef(String name, Object schemaOrRef) {
        if (this.defs == null) this.defs = new LinkedHashMap<>();
        this.defs.put(name, schemaOrRef);
        return this;
    }

    public ToolInputSchemaBuilder addDefinition(String name, Object schemaOrRef) {
        if (this.definitions == null) this.definitions = new LinkedHashMap<>();
        this.definitions.put(name, schemaOrRef);
        return this;
    }

    public ToolInputSchemaBuilder addRawProperty(String name, Map<String, Object> propertySchema) {
        Objects.requireNonNull(name, "property name");
        Objects.requireNonNull(propertySchema, "property schema");
        this.properties.put(name, propertySchema);
        return this;
    }

    // -------------- Property shortcuts --------------

    public PropertyBuilder string(String name) { return new PropertyBuilder(this, name, "string"); }

    public ToolInputSchemaBuilder string(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = string(name);
        if (spec != null) spec.accept(b);
        return b.end();
    }

    public PropertyBuilder integer(String name) { return new PropertyBuilder(this, name, "integer"); }

    public ToolInputSchemaBuilder integer(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = integer(name);
        if (spec != null) spec.accept(b);
        return b.end();
    }

    public PropertyBuilder number(String name) { return new PropertyBuilder(this, name, "number"); }

    public ToolInputSchemaBuilder number(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = number(name);
        if (spec != null) spec.accept(b);
        return b.end();
    }

    public PropertyBuilder bool(String name) { return new PropertyBuilder(this, name, "boolean"); }

    public ToolInputSchemaBuilder bool(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = bool(name);
        if (spec != null) spec.accept(b);
        return b.end();
    }

    public PropertyBuilder array(String name) { return new PropertyBuilder(this, name, "array"); }

    public ToolInputSchemaBuilder array(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = array(name);
        if (spec != null) spec.accept(b);
        return b.end();
    }

    public PropertyBuilder object(String name) { return new PropertyBuilder(this, name, "object"); }

    public ToolInputSchemaBuilder object(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = object(name);
        if (spec != null) spec.accept(b);
        return b.end();
    }

    public ToolInputSchemaBuilder enumType(String name, List<?> values) {
        return enumType(name, values, null);
    }

    public ToolInputSchemaBuilder enumType(String name, List<?> values, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = new PropertyBuilder(this, name, "string");
        b.enumValues(values);
        if (spec != null) spec.accept(b);
        return b.end();
    }

    // -------------- Convenience methods for required fields --------------

    public ToolInputSchemaBuilder requiredString(String name) {
        return string(name).required().end();
    }

    public ToolInputSchemaBuilder requiredString(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = string(name);
        if (spec != null) spec.accept(b);
        return b.required().end();
    }

    public ToolInputSchemaBuilder requiredInteger(String name) {
        return integer(name).required().end();
    }

    public ToolInputSchemaBuilder requiredInteger(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = integer(name);
        if (spec != null) spec.accept(b);
        return b.required().end();
    }

    public ToolInputSchemaBuilder requiredNumber(String name) {
        return number(name).required().end();
    }

    public ToolInputSchemaBuilder requiredNumber(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = number(name);
        if (spec != null) spec.accept(b);
        return b.required().end();
    }

    public ToolInputSchemaBuilder requiredBool(String name) {
        return bool(name).required().end();
    }

    public ToolInputSchemaBuilder requiredBool(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = bool(name);
        if (spec != null) spec.accept(b);
        return b.required().end();
    }

    public ToolInputSchemaBuilder requiredArray(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = array(name);
        if (spec != null) spec.accept(b);
        return b.required().end();
    }

    public ToolInputSchemaBuilder requiredObject(String name, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = object(name);
        if (spec != null) spec.accept(b);
        return b.required().end();
    }

    public ToolInputSchemaBuilder requiredEnum(String name, List<?> values) {
        return requiredEnum(name, values, null);
    }

    public ToolInputSchemaBuilder requiredEnum(String name, List<?> values, Consumer<PropertyBuilder> spec) {
        PropertyBuilder b = new PropertyBuilder(this, name, "string");
        b.enumValues(values);
        if (spec != null) spec.accept(b);
        return b.required().end();
    }

    // -------------- Convenience methods for optional fields --------------

    public ToolInputSchemaBuilder optionalString(String name, Consumer<PropertyBuilder> spec) {
        return string(name, spec);
    }

    public ToolInputSchemaBuilder optionalInteger(String name, Consumer<PropertyBuilder> spec) {
        return integer(name, spec);
    }

    public ToolInputSchemaBuilder optionalNumber(String name, Consumer<PropertyBuilder> spec) {
        return number(name, spec);
    }

    public ToolInputSchemaBuilder optionalBool(String name, Consumer<PropertyBuilder> spec) {
        return bool(name, spec);
    }

    public ToolInputSchemaBuilder optionalArray(String name, Consumer<PropertyBuilder> spec) {
        return array(name, spec);
    }

    public ToolInputSchemaBuilder optionalObject(String name, Consumer<PropertyBuilder> spec) {
        return object(name, spec);
    }

    public ToolInputSchemaBuilder optionalEnum(String name, List<?> values) {
        return enumType(name, values, null);
    }

    public ToolInputSchemaBuilder optionalEnum(String name, List<?> values, Consumer<PropertyBuilder> spec) {
        return enumType(name, values, spec);
    }

    // -------------- Convenience methods for common array types --------------

    public ToolInputSchemaBuilder stringArray(String name) {
        return array(name, PropertyBuilder::stringItems);
    }

    public ToolInputSchemaBuilder stringArray(String name, Consumer<PropertyBuilder> spec) {
        return array(name, p -> {
            p.stringItems();
            if (spec != null) spec.accept(p);
        });
    }

    public ToolInputSchemaBuilder integerArray(String name) {
        return array(name, PropertyBuilder::integerItems);
    }

    public ToolInputSchemaBuilder integerArray(String name, Consumer<PropertyBuilder> spec) {
        return array(name, p -> {
            p.integerItems();
            if (spec != null) spec.accept(p);
        });
    }

    public ToolInputSchemaBuilder numberArray(String name) {
        return array(name, PropertyBuilder::numberItems);
    }

    public ToolInputSchemaBuilder numberArray(String name, Consumer<PropertyBuilder> spec) {
        return array(name, p -> {
            p.numberItems();
            if (spec != null) spec.accept(p);
        });
    }

    // -------------- Build --------------

    public ToolInputSchema build() {
        List<String> req = this.required.isEmpty() ? null : new ArrayList<>(this.required);
        Map<String, Object> defsCopy = this.defs == null || this.defs.isEmpty() ? null : Map.copyOf(this.defs);
        Map<String, Object> definitionsCopy = this.definitions == null || this.definitions.isEmpty() ? null : Map.copyOf(this.definitions);
        Map<String, Object> propsCopy = this.properties.isEmpty() ? null : Map.copyOf(this.properties);
        return new ToolInputSchema(this.type, propsCopy, req, this.additionalProperties, defsCopy, definitionsCopy);
    }

    // -------------- PropertyBuilder --------------

    public static class PropertyBuilder {
        private final ToolInputSchemaBuilder parent;
        private final String name;
        private final Map<String, Object> schema;

        private PropertyBuilder(ToolInputSchemaBuilder parent, String name, String type) {
            this.parent = Objects.requireNonNull(parent);
            this.name = Objects.requireNonNull(name);
            this.schema = new LinkedHashMap<>();
            if (type != null) {
                this.schema.put("type", type);
            }
        }

        public PropertyBuilder title(String title) { if (title != null) schema.put("title", title); return this; }
        public PropertyBuilder description(String desc) { if (desc != null) schema.put("description", desc); return this; }
        public PropertyBuilder example(Object ex) { if (ex != null) schema.put("example", ex); return this; }
        public PropertyBuilder defaultValue(Object def) { if (def != null) schema.put("default", def); return this; }

        // string constraints
        public PropertyBuilder format(String format) { if (format != null) schema.put("format", format); return this; }
        public PropertyBuilder pattern(String regex) { if (regex != null) schema.put("pattern", regex); return this; }
        public PropertyBuilder minLength(Integer v) { if (v != null) schema.put("minLength", v); return this; }
        public PropertyBuilder maxLength(Integer v) { if (v != null) schema.put("maxLength", v); return this; }

        // numeric constraints
        public PropertyBuilder minimum(Number v) { if (v != null) schema.put("minimum", v); return this; }
        public PropertyBuilder maximum(Number v) { if (v != null) schema.put("maximum", v); return this; }
        public PropertyBuilder exclusiveMinimum(Number v) { if (v != null) schema.put("exclusiveMinimum", v); return this; }
        public PropertyBuilder exclusiveMaximum(Number v) { if (v != null) schema.put("exclusiveMaximum", v); return this; }

        // enum
        public PropertyBuilder enumValues(List<?> values) { if (values != null && !values.isEmpty()) schema.put("enum", values); return this; }

        // array
        public PropertyBuilder items(Consumer<PropertyBuilder> itemSpec) {
            Objects.requireNonNull(itemSpec, "itemSpec");
            PropertyBuilder item = new PropertyBuilder(parent, "__items__", null);
            itemSpec.accept(item);
            schema.put("items", item.schema);
            return this;
        }

        // 便捷的数组类型方法
        public PropertyBuilder stringItems() {
            schema.put("items", Map.of("type", "string"));
            return this;
        }

        public PropertyBuilder integerItems() {
            schema.put("items", Map.of("type", "integer"));
            return this;
        }

        public PropertyBuilder numberItems() {
            schema.put("items", Map.of("type", "number"));
            return this;
        }

        public PropertyBuilder booleanItems() {
            schema.put("items", Map.of("type", "boolean"));
            return this;
        }

        public PropertyBuilder objectItems(Consumer<ToolInputSchemaBuilder> itemSpec) {
            Objects.requireNonNull(itemSpec, "itemSpec");
            ToolInputSchemaBuilder sub = ToolInputSchemaBuilder.object();
            itemSpec.accept(sub);
            ToolInputSchema subSchema = sub.build();
            Map<String, Object> itemSchema = new LinkedHashMap<>();
            itemSchema.put("type", "object");
            if (subSchema.properties() != null) itemSchema.put("properties", subSchema.properties());
            if (subSchema.required() != null) itemSchema.put("required", subSchema.required());
            if (subSchema.additionalProperties() != null) itemSchema.put("additionalProperties", subSchema.additionalProperties());
            schema.put("items", itemSchema);
            return this;
        }

        // object (nested) - 修复类型覆盖问题
        public PropertyBuilder objectProps(Consumer<ToolInputSchemaBuilder> subSpec) {
            Objects.requireNonNull(subSpec, "subSpec");
            ToolInputSchemaBuilder sub = ToolInputSchemaBuilder.object();
            subSpec.accept(sub);
            ToolInputSchema subSchema = sub.build();

            // 不使用putAll，而是逐个设置，避免覆盖已有的type等属性
            if (subSchema.properties() != null) schema.put("properties", subSchema.properties());
            if (subSchema.required() != null) schema.put("required", subSchema.required());
            if (subSchema.additionalProperties() != null) schema.put("additionalProperties", subSchema.additionalProperties());
            if (subSchema.defs() != null) schema.put("$defs", subSchema.defs());
            if (subSchema.definitions() != null) schema.put("definitions", subSchema.definitions());
            return this;
        }

        // mark this property as required at parent level
        public PropertyBuilder required() { parent.required(this.name); return this; }

        // finish and attach to parent
        public ToolInputSchemaBuilder end() {
            parent.properties.put(this.name, Map.copyOf(this.schema));
            return parent;
        }
    }
   
}
