package cn.genn.ai.tools;

import lombok.Builder;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流式数据块
 * 
 * 表示流式输出中的一个数据片段
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class StreamChunk {
    
    /**
     * 数据块类型
     */
    private ChunkType type;
    
    /**
     * 数据内容
     */
    private String content;
    
    /**
     * 是否为最后一个数据块
     */
    private boolean isLast;
    
    /**
     * 数据块序号
     */
    private int sequence;
    
    /**
     * 时间戳
     */
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();
    
    /**
     * 额外的元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 数据块类型枚举
     */
    public enum ChunkType {
        /** 开始标记 */
        START,
        /** 数据内容 */
        DATA,
        /** 进度更新 */
        PROGRESS,
        /** 错误信息 */
        ERROR,
        /** 结束标记 */
        END
    }
    
    /**
     * 创建开始块
     */
    public static StreamChunk start(Map<String, Object> metadata) {
        return StreamChunk.builder()
            .type(ChunkType.START)
            .content("")
            .isLast(false)
            .sequence(0)
            .timestamp(LocalDateTime.now())
            .metadata(metadata)
            .build();
    }
    
    /**
     * 创建数据块
     */
    public static StreamChunk data(String content, int sequence) {
        return StreamChunk.builder()
            .type(ChunkType.DATA)
            .content(content)
            .isLast(false)
            .sequence(sequence)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * 创建进度块
     */
    public static StreamChunk progress(String progressInfo, int sequence, Map<String, Object> metadata) {
        return StreamChunk.builder()
            .type(ChunkType.PROGRESS)
            .content(progressInfo)
            .isLast(false)
            .sequence(sequence)
            .timestamp(LocalDateTime.now())
            .metadata(metadata)
            .build();
    }
    
    /**
     * 创建错误块
     */
    public static StreamChunk error(String errorMessage, int sequence) {
        return StreamChunk.builder()
            .type(ChunkType.ERROR)
            .content(errorMessage)
            .isLast(false)
            .sequence(sequence)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    /**
     * 创建结束块
     */
    public static StreamChunk end(String finalContent, int sequence) {
        return StreamChunk.builder()
            .type(ChunkType.END)
            .content(finalContent)
            .isLast(true)
            .sequence(sequence)
            .timestamp(LocalDateTime.now())
            .build();
    }
}
