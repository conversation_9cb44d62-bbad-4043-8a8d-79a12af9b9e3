package cn.genn.ai.tools.model;

import cn.genn.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public record Tool(
                    @JsonProperty("name") String name,
                    @JsonProperty("description") String description,
                    @JsonProperty("inputSchema") ToolInputSchema inputSchema) {

    public Tool(String name, String description, String schema) {
        this(name, description, parseSchema(schema));
    }

    private static ToolInputSchema parseSchema(String schema) {
        return JsonUtils.parse(schema, ToolInputSchema.class);
    }
}

