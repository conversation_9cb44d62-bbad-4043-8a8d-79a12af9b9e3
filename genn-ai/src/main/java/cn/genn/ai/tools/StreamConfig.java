package cn.genn.ai.tools;

import lombok.Builder;
import lombok.Data;
import java.time.Duration;

/**
 * 流式调用配置
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class StreamConfig {
    
    /**
     * 缓冲区大小
     */
    @Builder.Default
    private int bufferSize = 1024;
    
    /**
     * 流式输出间隔
     */
    @Builder.Default
    private Duration flushInterval = Duration.ofMillis(100);
    
    /**
     * 超时时间
     */
    @Builder.Default
    private Duration timeout = Duration.ofMinutes(5);
    
    /**
     * 是否启用压缩
     */
    @Builder.Default
    private boolean compressionEnabled = false;
    
    /**
     * 最大重试次数
     */
    @Builder.Default
    private int maxRetries = 3;
    
    /**
     * 是否启用背压控制
     */
    @Builder.Default
    private boolean backpressureEnabled = true;
    
    /**
     * 最大队列大小
     */
    @Builder.Default
    private int maxQueueSize = 1000;
    
    /**
     * 默认配置
     */
    public static StreamConfig defaultConfig() {
        return StreamConfig.builder().build();
    }
    
    /**
     * 高性能配置
     */
    public static StreamConfig highPerformanceConfig() {
        return StreamConfig.builder()
            .bufferSize(4096)
            .flushInterval(Duration.ofMillis(50))
            .timeout(Duration.ofMinutes(10))
            .compressionEnabled(true)
            .maxRetries(5)
            .maxQueueSize(2000)
            .build();
    }
    
    /**
     * 低延迟配置
     */
    public static StreamConfig lowLatencyConfig() {
        return StreamConfig.builder()
            .bufferSize(512)
            .flushInterval(Duration.ofMillis(10))
            .timeout(Duration.ofMinutes(2))
            .compressionEnabled(false)
            .maxRetries(1)
            .maxQueueSize(500)
            .build();
    }
}
