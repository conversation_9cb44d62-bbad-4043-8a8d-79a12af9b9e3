package cn.genn.ai.tools;

import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.tool.ToolCallback;

import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * 流式工具回调接口
 * 
 * 扩展Spring AI的ToolCallback，增加流式输出能力。
 * 支持传统的同步调用和新的流式调用两种模式。
 * 
 * <AUTHOR>
 */
public interface StreamToolCallback extends ToolCallback {
    
    /**
     * 流式执行工具调用
     * 
     * @param toolCall 工具调用请求
     * @param streamHandler 流式数据处理器
     * @return 异步执行结果的Future
     */
    default CompletableFuture<String> callStream(
        AssistantMessage.ToolCall toolCall,
        Consumer<StreamChunk> streamHandler
    ) {
        throw new UnsupportedOperationException("Stream execution not supported");
    }

    /**
     * 是否开启流式调用
     * 
     */
    default boolean enabledStreaming() {
        return true;
    }
}
