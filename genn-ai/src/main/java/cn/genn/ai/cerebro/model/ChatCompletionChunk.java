package cn.genn.ai.cerebro.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * OpenAI Chat Completion Chunk 响应格式
 * 用于流式响应的数据结构
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ChatCompletionChunk {

    /**
     * 响应ID
     */
    private String id;

    /**
     * 对象类型，通常为 "chat.completion.chunk"
     */
    private String object;

    /**
     * 创建时间戳
     */
    private Long created;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 系统指纹
     */
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;

    /**
     * 选择列表
     */
    private List<Choice> choices;

    /**
     * 使用情况统计
     */
    private Usage usage;

    /**
     * 选择项
     */
    @Data
    @Accessors(chain = true)
    public static class Choice {
        
        /**
         * 选择索引
         */
        private Integer index;

        /**
         * 增量消息内容
         */
        private Delta delta;

        /**
         * 日志概率
         */
        private Object logprobs;

        /**
         * 完成原因
         */
        @JsonProperty("finish_reason")
        private String finishReason;
    }

    /**
     * 增量消息内容
     */
    @Data
    @Accessors(chain = true)
    public static class Delta {
        
        /**
         * 角色
         */
        private String role;
        
        /**
         * 推理内容
         */
        @JsonProperty("reasoning_content")
        private String reasoningContent;

        /**
         * 内容
         */
        private String content;

        /**
         * 工具调用
         */
        @JsonProperty("tool_calls")
        private List<ToolCall> toolCalls;
    }

    /**
     * 工具调用
     */
    @Data
    @Accessors(chain = true)
    public static class ToolCall {
        
        /**
         * 工具调用ID
         */
        private String id;

        /**
         * 类型
         */
        private String type;

        /**
         * 函数调用
         */
        private Function function;
    }

    /**
     * 函数调用
     */
    @Data
    @Accessors(chain = true)
    public static class Function {
        
        /**
         * 函数名称
         */
        private String name;

        /**
         * 函数参数
         */
        private String arguments;
    }

    /**
     * 使用情况统计
     */
    @Data
    @Accessors(chain = true)
    public static class Usage {
        
        /**
         * 提示词令牌数
         */
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;

        /**
         * 完成令牌数
         */
        @JsonProperty("completion_tokens")
        private Integer completionTokens;

        /**
         * 总令牌数
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;

        /**
         * 提示词令牌详情
         */
        @JsonProperty("prompt_tokens_details")
        private TokenDetails promptTokensDetails;

        /**
         * 完成令牌详情
         */
        @JsonProperty("completion_tokens_details")
        private TokenDetails completionTokensDetails;
    }

    /**
     * 令牌详情
     */
    @Data
    @Accessors(chain = true)
    public static class TokenDetails {
        
        /**
         * 缓存令牌数
         */
        @JsonProperty("cached_tokens")
        private Integer cachedTokens;

        /**
         * 推理令牌数
         */
        @JsonProperty("reasoning_tokens")
        private Integer reasoningTokens;
    }
}
