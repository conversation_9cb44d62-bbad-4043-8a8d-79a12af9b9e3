package cn.genn.ai.cerebro.service;

import cn.genn.ai.cerebro.model.*;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.List;
import java.util.function.Consumer;

/**
 * Cerebro 服务客户端
 * 提供与 Cerebro 工作流引擎交互的完整功能
 *
 * 主要功能：
 * 1. 执行流式工作流请求
 * 2. 接收完整的 SSE 事件流
 * 3. 过滤并处理 AI 回复内容
 * 4. 任务取消功能
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
public class CerebroService {

    private static final String CHAT_COMPLETIONS_PATH = "/web-api/v1/chat/completions";
    private static final String CHAT_CANCEL_PATH = "/web-api/workflow/tasks/cancel";
    private static final String TOKEN_HEADER = "token";
    private static final String MAGIC_TOKEN = "magic-token";
    private static final String TASK_ID_HEADER = "task-id";
    private static final ParameterizedTypeReference<ServerSentEvent<String>> SSE_TYPE_REF =
            new ParameterizedTypeReference<>() {
            };

    private final WebClient webClient;
    private final String cerebroUrl;

    // ==================== 公共 API 方法 ====================
    
    /**
     * 执行非流式工作流请求（基础方法）
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @return 工作流响应
     */
    public CerebroNonStreamResponse execute(CerebroRequest request, CerebroAuth auth) {
        request.setStream(false);
        WebClient.RequestBodySpec requestHeadersSpec = webClient.post()
                .uri(cerebroUrl + CHAT_COMPLETIONS_PATH)
                .contentType(MediaType.APPLICATION_JSON);
        requestHeadersSpec = applyAuthentication(requestHeadersSpec, auth);
        String result = requestHeadersSpec
                .bodyValue(JsonUtils.toJsonNotNull(request))
                .retrieve()
                .bodyToMono(String.class)
                .block();

        // 解析响应
        try {
            CerebroNonStreamResponse response = JsonUtils.parse(result, CerebroNonStreamResponse.class);
            // 自动解析并设置AI文本内容和思考内容字段
            response.parseAndSetAiContent();
            return response;
        } catch (Exception e) {
            log.error("解析非流式响应失败: {}", result, e);
            throw new RuntimeException("解析非流式响应失败", e);
        }
    }

    /**
     * 执行非流式工作流请求并返回完整响应
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @return 完整的工作流响应
     */
    public CerebroNonStreamResponse executeNonStream(CerebroRequest request, CerebroAuth auth) {
        return execute(request, auth);
    }

    /**
     * 执行非流式工作流请求并仅返回AI回复内容(不包括思考过程)
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @return AI回复内容
     */
    public String executeNonStreamForAiContent(CerebroRequest request, CerebroAuth auth) {
        CerebroNonStreamResponse response = execute(request, auth);

        // 优先从choices中获取AI回复
        String aiContent = response.getAiContent();
        if (aiContent != null && !aiContent.trim().isEmpty()) {
            return aiContent;
        }
        return null;
    }

    /**
     * 执行流式工作流请求（基础方法）
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @return 原始响应流
     */
    public Flux<CerebroResponse> executeStream(CerebroRequest request, CerebroAuth auth) {
        request.setStream(true);
        return buildAuthenticatedRequest(CHAT_COMPLETIONS_PATH, auth)
                .bodyValue(JsonUtils.toJsonNotNull(request))
                .exchangeToFlux(response -> processStreamResponse(response, request))
                .timeout(Duration.ofSeconds(request.getTimeout()))
                .doOnSubscribe(subscription -> logWorkflowStart(request))
                .doOnError(error -> logWorkflowError(request, error))
                .doOnComplete(() -> logWorkflowComplete(request));
    }

    /**
     * 执行流式请求并返回完整的 SSE 事件封装
     * 包括 id、event、data 数据的完整封装，供业务使用
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @return 完整的 SSE 事件流
     */
    public Flux<CerebroSseEvent> executeStreamWithFullSse(CerebroRequest request, CerebroAuth auth) {
        return executeStream(request, auth).map(this::convertToSseEvent);
    }

    /**
     * 执行流式请求并仅返回 AI 回复内容（answer 和 fastAnswer 事件）
     * 数据格式为标准的 OpenAI 响应格式
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @return AI 回复内容流
     */
    public Flux<CerebroAiResponse> executeStreamForAiResponse(CerebroRequest request, CerebroAuth auth) {
        return executeStreamWithFullSse(request, auth)
                .filter(CerebroSseEvent::isAiResponse)
                .map(this::convertToAiResponse);
    }

    /**
     * 执行流式请求并提供事件处理回调
     * 便于业务方根据不同事件类型进行处理
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @param eventHandler 事件处理器
     * @return 完整的 SSE 事件流
     */
    public Flux<CerebroSseEvent> executeStreamWithHandler(CerebroRequest request, CerebroAuth auth,
                                                          Consumer<CerebroSseEvent> eventHandler) {
        return executeStreamWithFullSse(request, auth)
                .doOnNext(eventHandler);
    }

    /**
     * 执行流式请求并等待完成后返回完整的AI回复内容
     * 使用流式请求避免超时，但等待所有消息完成后返回完整的AI回复文本(不包含思考)
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @return 完整的AI回复内容
     */
    public String executeStreamAndWaitForCompleteAiContent(CerebroRequest request, CerebroAuth auth) {
        StringBuilder completeContent = new StringBuilder();

        executeStreamForAiResponse(request, auth)
                .doOnNext(aiResponse -> {
                    String content = aiResponse.getAiTextContent();
                    if (content != null) {
                        completeContent.append(content);
                        log.debug("接收到AI回复片段: {}", content);
                    }
                })
                .doOnComplete(() -> log.info("AI回复流式传输完成，总长度: {}", completeContent.length()))
                .doOnError(error -> log.error("AI回复流式传输出错", error))
                .blockLast(); // 等待流式传输完成

        String result = completeContent.toString();
        log.info("完整AI回复内容获取完成，长度: {}", result.length());
        return result;
    }

    /**
     * 执行流式请求并等待完成后返回完整的AI回复响应对象
     * 包含完整内容和相关元数据信息
     *
     * @param request 请求参数
     * @param auth 认证信息
     * @return 完整的AI回复响应对象
     */
    public CerebroCompleteAiResponse executeStreamAndWaitForCompleteAiResponse(CerebroRequest request, CerebroAuth auth) {
        StringBuilder completeContent = new StringBuilder();
        CerebroCompleteAiResponse.CerebroCompleteAiResponseBuilder responseBuilder = CerebroCompleteAiResponse.builder();

        executeStreamForAiResponse(request, auth)
                .doOnNext(aiResponse -> {
                    // 设置基础信息（只设置一次）
                    if (responseBuilder.getTaskId() == null) {
                        responseBuilder.taskId(aiResponse.getTaskId())
                                .appId(aiResponse.getAppId())
                                .chatId(aiResponse.getChatId())
                                .startTime(System.currentTimeMillis());
                    }

                    // 累积内容
                    String content = aiResponse.getContent();
                    if (content != null) {
                        completeContent.append(content);
                    }

                    // 更新完成状态
                    if (aiResponse.isLastChunk()) {
                        responseBuilder.finishReason(aiResponse.getFinishReason())
                                .completed(true);
                    }
                })
                .doOnComplete(() -> {
                    responseBuilder.endTime(System.currentTimeMillis())
                            .completed(true);
                    log.info("AI回复流式传输完成，总长度: {}", completeContent.length());
                })
                .doOnError(error -> {
                    responseBuilder.endTime(System.currentTimeMillis())
                            .error(error.getMessage());
                    log.error("AI回复流式传输出错", error);
                })
                .blockLast();

        return responseBuilder.content(completeContent.toString()).build();
    }

    /**
     * 取消任务执行
     *
     * @param appId 应用ID（可选）
     * @param taskId 任务ID
     * @param auth 认证信息
     * @return 取消是否成功
     */
    public boolean cancelTask(String appId, String taskId, CerebroAuth auth) {
        try {
            String cancelRequestBody = buildCancelRequestBody(appId, taskId);
            String response = buildAuthenticatedRequest(CHAT_CANCEL_PATH, auth)
                    .bodyValue(cancelRequestBody)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            log.info("工作流取消请求成功，taskId: {}, appId: {}, response: {}", taskId, appId, response);
            return true;
        } catch (Exception e) {
            log.error("停止工作流执行失败，taskId: {}, appId: {}", taskId, appId, e);
            return false;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建带认证的请求
     */
    private WebClient.RequestBodySpec buildAuthenticatedRequest(String path, CerebroAuth auth) {
        WebClient.RequestBodySpec requestBodySpec = webClient
                .post()
                .uri(cerebroUrl + path)
                .contentType(MediaType.APPLICATION_JSON);

        return applyAuthentication(requestBodySpec, auth);
    }

    /**
     * 应用认证信息到请求
     */
    private WebClient.RequestBodySpec applyAuthentication(WebClient.RequestBodySpec requestBodySpec, CerebroAuth auth) {
        switch (auth.getType()) {
            case API_KEY:
                return requestBodySpec.header(HttpHeaders.AUTHORIZATION, "Bearer " + auth.getApiKey());
            case COOKIE:
                requestBodySpec.header(HttpHeaders.COOKIE, auth.getCookie());
                if (CharSequenceUtil.isNotEmpty(auth.getToken())) {
                    requestBodySpec.header(TOKEN_HEADER, auth.getToken());
                }
                return requestBodySpec;
            case MAGIC_TOKEN:
                requestBodySpec.header(MAGIC_TOKEN, auth.getMagicTokenData());
                return requestBodySpec;
            default:
                throw new IllegalArgumentException("Unsupported auth type: " + auth.getType());
        }
    }

    /**
     * 处理流式响应
     */
    private Flux<CerebroResponse> processStreamResponse(org.springframework.web.reactive.function.client.ClientResponse response,
                                                       CerebroRequest request) {
        String taskId = response.headers().asHttpHeaders().getFirst(TASK_ID_HEADER);
        if (taskId == null) {
            return Flux.error(new RuntimeException("响应错误,未找到taskId"));
        }

        return response.bodyToFlux(SSE_TYPE_REF)
                .map(sseEvent -> createCerebroResponse(sseEvent, request, taskId))
                .doOnCancel(() -> log.info("cerebro请求已被取消，taskId: {}", taskId));
    }

    /**
     * 创建 CerebroResponse 对象
     */
    private CerebroResponse createCerebroResponse(ServerSentEvent<String> sseEvent, CerebroRequest request, String taskId) {
        log.debug("接收到SSE事件: {}", sseEvent);
        CerebroEventType eventType = CerebroEventType.fromCode(sseEvent.event());
        return new CerebroResponse()
                .setAppId(request.getAppId())
                .setChatId(request.getChatId())
                .setTaskId(taskId)
                .setEventType(eventType)
                .setContent(sseEvent)
                .setParsedData(parseEventData(sseEvent, eventType));
    }

    /**
     * 构建取消请求体
     */
    private String buildCancelRequestBody(String appId, String taskId) {
        if (CharSequenceUtil.isNotEmpty(appId)) {
            return String.format("{\"taskId\":\"%s\",\"appId\":\"%s\"}", taskId, appId);
        } else {
            return String.format("{\"taskId\":\"%s\"}", taskId);
        }
    }

    /**
     * 解析事件数据
     */
    private Object parseEventData(ServerSentEvent<String> sseEvent, CerebroEventType eventType) {
        String data = sseEvent.data();
        if (data == null || data.trim().isEmpty()) {
            return null;
        }

        // 处理 [DONE] 标记
        if ("[DONE]".equals(data)) {
            return isAiResponseEvent(eventType) ? createDoneChunk() : data;
        }

        try {
            // 对于 AI 回复事件，解析为 OpenAI 格式
            if (isAiResponseEvent(eventType)) {
                return JsonUtils.parse(data, ChatCompletionChunk.class);
            }
            // 对于其他事件，解析为通用对象
            return JsonUtils.parse(data, Object.class);
        } catch (Exception e) {
            log.warn("解析事件数据失败，事件类型: {}, 数据: {}", eventType, data, e);
            return data; // 解析失败时返回原始字符串
        }
    }

    /**
     * 判断是否为 AI 回复事件
     */
    private boolean isAiResponseEvent(CerebroEventType eventType) {
        return eventType == CerebroEventType.ANSWER || eventType == CerebroEventType.FAST_ANSWER;
    }

    /**
     * 创建表示流式结束的 ChatCompletionChunk
     */
    private ChatCompletionChunk createDoneChunk() {
        ChatCompletionChunk chunk = new ChatCompletionChunk();
        ChatCompletionChunk.Choice choice = new ChatCompletionChunk.Choice();
        choice.setIndex(0);
        choice.setFinishReason("stop");

        ChatCompletionChunk.Delta delta = new ChatCompletionChunk.Delta();
        delta.setContent(null); // [DONE] 标记时内容为空
        choice.setDelta(delta);

        chunk.setChoices(List.of(choice));
        return chunk;
    }

    /**
     * 转换为完整的 SSE 事件封装
     */
    private CerebroSseEvent convertToSseEvent(CerebroResponse response) {
        ServerSentEvent<String> sse = response.getContent();
        CerebroEventType eventType = response.getEventType();
        boolean isAiResponse = isAiResponseEvent(eventType);

        CerebroSseEvent sseEvent = new CerebroSseEvent()
                .setId(sse.id())
                .setEvent(sse.event())
                .setData(sse.data())
                .setEventType(eventType)
                .setTaskId(response.getTaskId())
                .setAppId(response.getAppId())
                .setChatId(response.getChatId())
                .setTimestamp(System.currentTimeMillis())
                .setRawData(response.getParsedData())
                .setAiResponse(isAiResponse);

        // 如果是 AI 回复事件，设置 OpenAI 格式数据
        if (isAiResponse && response.getParsedData() instanceof ChatCompletionChunk) {
            sseEvent.setOpenAiData((ChatCompletionChunk) response.getParsedData());
        }

        return sseEvent;
    }

    /**
     * 转换为 AI 回复响应
     */
    private CerebroAiResponse convertToAiResponse(CerebroSseEvent sseEvent) {
        return new CerebroAiResponse()
                .setTaskId(sseEvent.getTaskId())
                .setAppId(sseEvent.getAppId())
                .setChatId(sseEvent.getChatId())
                .setEventType(sseEvent.getEventType())
                .setEvent(sseEvent.getEvent())
                .setTimestamp(sseEvent.getTimestamp())
                .setDone(sseEvent.isDoneEvent())
                .setData(sseEvent.getOpenAiData())
                .parseAiContents();
    }

    // ==================== 日志辅助方法 ====================

    private void logWorkflowStart(CerebroRequest request) {
        log.info("开始执行Cerebro工作流，appId: {}, chatId: {}", request.getAppId(), request.getChatId());
    }

    private void logWorkflowError(CerebroRequest request, Throwable error) {
        log.error("Cerebro工作流执行出错，appId: {}, chatId: {}", request.getAppId(), request.getChatId(), error);
    }

    private void logWorkflowComplete(CerebroRequest request) {
        log.info("Cerebro工作流执行完成，appId: {}, chatId: {}", request.getAppId(), request.getChatId());
    }

}
