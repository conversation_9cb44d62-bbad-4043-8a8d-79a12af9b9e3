package cn.genn.ai.cerebro.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Cerebro 事件类型枚举
 *
 * 定义Cerebro workflow中所有可能的SSE事件类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum CerebroEventType {

    /**
     * 工作流开始
     */
    WORKFLOW_STARTED("workflow_started", "工作流开始"),

    /**
     * 流节点响应
     */
    FLOW_NODE_RESPONSE("flowNodeResponse", "流节点响应"),

    /**
     * 流节点状态
     */
    FLOW_NODE_STATUS("flowNodeStatus", "流节点状态"),

    /**
     * 工具调用
     */
    TOOL_CALL("toolCall", "工具调用"),

    /**
     * 工具参数（流式）
     */
    TOOL_PARAMS("toolParams", "工具参数"),

    /**
     * 工具响应
     */
    TOOL_RESPONSE("toolResponse", "工具响应"),

    /**
     * 答案流式输出
     */
    ANSWER("answer", "答案流式输出"),

    /**
     * 快速答案流式回复
     */
    FAST_ANSWER("fastAnswer", "答案流式输出"),

    /**
     * 调试信息
     */
    DEBUG("debug", "调试信息"),

    /**
     * 工作流完成
     */
    WORKFLOW_COMPLETED("workflow_completed", "工作流完成"),

    /**
     * 用于处理用户交互，如确认、选择等
     */
    INTERACTIVE("interactive", "交互式事件"),

    /**
     * 未知事件类型
     */
    UNKNOWN("unknown", "未知事件类型"),

    /**
     * 工作流失败
     */
    WORKFLOW_FAILED("workflow_failed", "工作流失败"),

    /**
     * 心跳事件
     */
    HEARTBEAT("heartbeat", "心跳事件"),

    /**
     * 错误事件
     */
    ERROR("error","错误事件类型"),

    WARNING("warning", "警告事件类型"),

    FLOW_RESPONSES("flowResponses", "流式内容"),

    UPDATE_VARIABLES("updateVariables", "更新变量"),

    WORKFLOW_PROGRESS("workflow_progress", "进度事件"),

    ;

    /**
     * 事件类型代码
     */
    private final String code;

    /**
     * 事件类型描述
     */
    private final String description;

    /**
     * 根据代码获取事件类型
     *
     * @param code 事件类型代码
     * @return 事件类型枚举
     */
    public static CerebroEventType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return UNKNOWN;
        }

        for (CerebroEventType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return UNKNOWN;
    }

    /**
     * 是否为工作流控制事件
     *
     * @return true if workflow control event
     */
    public boolean isWorkflowControlEvent() {
        return this == WORKFLOW_STARTED || this == WORKFLOW_COMPLETED;
    }

    /**
     * 是否为工具相关事件
     *
     * @return true if tool related event
     */
    public boolean isToolEvent() {
        return this == TOOL_CALL || this == TOOL_PARAMS || this == TOOL_RESPONSE;
    }

    /**
     * 是否为流式内容事件
     *
     * @return true if streaming content event
     */
    public boolean isStreamingEvent() {
        return this == ANSWER || this == TOOL_PARAMS;
    }
}

