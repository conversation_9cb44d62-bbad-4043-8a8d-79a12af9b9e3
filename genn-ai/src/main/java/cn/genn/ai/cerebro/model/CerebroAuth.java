package cn.genn.ai.cerebro.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 鉴权方式
 * apiKey或者cookie
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CerebroAuth {
    
    private AuthType type;
    
    private String apiKey;
    
    private String token;
    
    private String cookie;

    private String magicTokenData;
    
    public enum AuthType {
        API_KEY, COOKIE,MAGIC_TOKEN
    }
}
