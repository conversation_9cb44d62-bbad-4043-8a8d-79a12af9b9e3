package cn.genn.ai.cerebro.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Cerebro 完整AI回复响应类
 * 用于封装流式传输完成后的完整AI回复内容和相关元数据
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CerebroCompleteAiResponse {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 完整的AI回复内容
     */
    private String content;

    /**
     * 完成原因
     */
    private String finishReason;

    /**
     * 是否已完成
     */
    private boolean completed;

    /**
     * 错误信息（如果有）
     */
    private String error;

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;

    // ==================== 便利方法 ====================

    /**
     * 获取执行耗时（毫秒）
     */
    public Long getDuration() {
        if (startTime != null && endTime != null) {
            return endTime - startTime;
        }
        return null;
    }

    /**
     * 获取内容长度
     */
    public int getContentLength() {
        return content != null ? content.length() : 0;
    }

    /**
     * 判断是否执行成功
     */
    public boolean isSuccess() {
        return completed && error == null && content != null && !content.trim().isEmpty();
    }

    /**
     * 判断是否有错误
     */
    public boolean hasError() {
        return error != null && !error.trim().isEmpty();
    }

    /**
     * 判断是否正常完成（没有错误且已完成）
     */
    public boolean isNormallyCompleted() {
        return completed && !hasError();
    }

    /**
     * 获取格式化的执行信息
     */
    public String getExecutionInfo() {
        StringBuilder info = new StringBuilder();
        info.append("TaskId: ").append(taskId);
        if (getDuration() != null) {
            info.append(", Duration: ").append(getDuration()).append("ms");
        }
        info.append(", ContentLength: ").append(getContentLength());
        info.append(", Status: ").append(isSuccess() ? "Success" : (hasError() ? "Error" : "Incomplete"));
        if (finishReason != null) {
            info.append(", FinishReason: ").append(finishReason);
        }
        return info.toString();
    }

    /**
     * Builder类的自定义方法
     */
    public static class CerebroCompleteAiResponseBuilder {
        
        /**
         * 设置当前时间为开始时间
         */
        public CerebroCompleteAiResponseBuilder startNow() {
            this.startTime = System.currentTimeMillis();
            return this;
        }

        /**
         * 设置当前时间为结束时间
         */
        public CerebroCompleteAiResponseBuilder endNow() {
            this.endTime = System.currentTimeMillis();
            return this;
        }

        /**
         * 标记为已完成
         */
        public CerebroCompleteAiResponseBuilder markCompleted() {
            this.completed = true;
            return this;
        }

        /**
         * 设置错误信息并标记为未完成
         */
        public CerebroCompleteAiResponseBuilder withError(String errorMessage) {
            this.error = errorMessage;
            this.completed = false;
            return this;
        }

        /**
         * 获取当前构建器中的taskId（用于条件判断）
         */
        public String getTaskId() {
            return this.taskId;
        }
    }
}
