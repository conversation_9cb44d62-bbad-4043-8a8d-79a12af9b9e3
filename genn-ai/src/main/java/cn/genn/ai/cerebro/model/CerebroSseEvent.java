package cn.genn.ai.cerebro.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Cerebro SSE 事件封装类
 * 提供完整的 SSE 事件信息，包括 id、event、data 的完整封装
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CerebroSseEvent {

    /**
     * SSE 事件ID
     */
    private String id;

    /**
     * SSE 事件类型
     */
    private String event;

    /**
     * SSE 事件数据（原始字符串）
     */
    private String data;

    /**
     * 解析后的事件类型枚举
     */
    private CerebroEventType eventType;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 是否为 AI 回复事件（answer 或 fastAnswer）
     */
    private boolean isAiResponse;

    /**
     * 解析后的 OpenAI 格式数据（仅当 isAiResponse 为 true 时有值）
     */
    private ChatCompletionChunk openAiData;

    /**
     * 原始数据对象（JSON 解析后的对象）
     */
    private Object rawData;

    /**
     * 创建时间戳
     */
    private Long timestamp;

    /**
     * 判断是否为工作流控制事件
     */
    public boolean isWorkflowControlEvent() {
        return eventType != null && eventType.isWorkflowControlEvent();
    }

    /**
     * 判断是否为工具相关事件
     */
    public boolean isToolEvent() {
        return eventType != null && eventType.isToolEvent();
    }

    /**
     * 判断是否为进度事件
     */
    public boolean isProgressEvent() {
        return eventType == CerebroEventType.WORKFLOW_PROGRESS;
    }

    /**
     * 判断是否为错误事件
     */
    public boolean isErrorEvent() {
        return eventType == CerebroEventType.ERROR || eventType == CerebroEventType.WORKFLOW_FAILED;
    }

    /**
     * 判断是否为完成事件
     */
    public boolean isCompletedEvent() {
        return eventType == CerebroEventType.WORKFLOW_COMPLETED;
    }

    /**
     * 判断是否为流式结束标记
     */
    public boolean isDoneEvent() {
        return "[DONE]".equals(data);
    }

    /**
     * 判断是否为 AI 回复的最后一个片段
     */
    public boolean isLastAiChunk() {
        if (!isAiResponse) {
            return false;
        }

        // 检查是否为 [DONE] 标记
        if (isDoneEvent()) {
            return true;
        }

        // 检查 OpenAI 数据中的 finish_reason
        if (openAiData != null && openAiData.getChoices() != null && !openAiData.getChoices().isEmpty()) {
            String finishReason = openAiData.getChoices().get(0).getFinishReason();
            return finishReason != null && !finishReason.isEmpty();
        }

        return false;
    }
}
