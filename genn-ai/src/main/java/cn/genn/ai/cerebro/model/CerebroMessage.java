package cn.genn.ai.cerebro.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CerebroMessage {

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 角色
     */
    private String role = "user";

    /**
     * 是否在UI中隐藏
     */
    private Boolean hideInUI = false;

    /**
     * 消息内容,Content或者纯文本字符串
     * @see Content
     */
    private Object content;
}
