package cn.genn.ai.cerebro.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Cerebro AI 回复响应类
 * 专门用于封装 answer 和 fastAnswer 事件的 AI 回复内容
 * 数据格式符合标准的 OpenAI 响应格式
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CerebroAiResponse {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 事件类型（answer 或 fastAnswer）
     */
    private CerebroEventType eventType;

    /**
     * 原始事件名称
     */
    private String event;

    /**
     * OpenAI 标准格式的响应数据
     */
    private ChatCompletionChunk data;

    /**
     * 创建时间戳
     */
    private Long timestamp;

    /**
     * AI回复的纯文本内容（从data中提取，忽略思考过程）
     */
    private String aiTextContent;

    /**
     * AI的思考过程内容（从data中提取reasoning类型内容）
     */
    private String aiReasoningContent;

    /**
     * 是否为流式结束标记
     */
    private boolean isDone;
    
    
    public CerebroAiResponse parseAiContents() {
        this.aiTextContent = getContent();
        this.aiReasoningContent = getReasonContent();
        return this;
    }
    
    /**
     * 获取 AI 回复的推理内容
     */
    public String getReasonContent() {
        if (data != null && data.getChoices() != null && !data.getChoices().isEmpty()) {
            ChatCompletionChunk.Choice choice = data.getChoices().getFirst();
            if (choice.getDelta() != null) {
                return choice.getDelta().getReasoningContent();
            }
        }
        return null;
    }

    /**
     * 获取 AI 回复的文本内容
     */
    public String getContent() {
        if (data != null && data.getChoices() != null && !data.getChoices().isEmpty()) {
            ChatCompletionChunk.Choice choice = data.getChoices().getFirst();
            if (choice.getDelta() != null) {
                return choice.getDelta().getContent();
            }
        }
        return null;
    }

    /**
     * 判断是否为 [DONE] 标记的响应
     */
    public boolean isDoneMarker() {
        return isDone || (getFinishReason() != null && !"".equals(getFinishReason()));
    }

    /**
     * 获取完成原因
     */
    public String getFinishReason() {
        if (data != null && data.getChoices() != null && !data.getChoices().isEmpty()) {
            return data.getChoices().getFirst().getFinishReason();
        }
        return null;
    }

    /**
     * 判断是否为流式响应的最后一个片段
     */
    public boolean isLastChunk() {
        String finishReason = getFinishReason();
        return finishReason != null && !finishReason.isEmpty();
    }

    /**
     * 获取角色信息
     */
    public String getRole() {
        if (data != null && data.getChoices() != null && !data.getChoices().isEmpty()) {
            ChatCompletionChunk.Choice choice = data.getChoices().get(0);
            if (choice.getDelta() != null) {
                return choice.getDelta().getRole();
            }
        }
        return null;
    }

    /**
     * 判断是否为 answer 事件
     */
    public boolean isAnswerEvent() {
        return eventType == CerebroEventType.ANSWER;
    }

    /**
     * 判断是否为 fastAnswer 事件
     */
    public boolean isFastAnswerEvent() {
        return eventType == CerebroEventType.FAST_ANSWER;
    }
}
