package cn.genn.ai.cerebro.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.http.codec.ServerSentEvent;

/**
 * Cerebro 响应封装类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CerebroResponse {

    /**
     * 工作流ID
     */
    private String appId;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 事件类型
     */
    private CerebroEventType eventType;

    /**
     * 响应sse报文
     */
    private ServerSentEvent<String> content;

    /**
     * 解析后的数据内容（仅当事件类型为 answer 或 fastAnswer 时有值）
     */
    private Object parsedData;
}
