package cn.genn.ai.cerebro.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Cerebro 非流式响应封装类
 * 用于处理完整的工作流执行结果，包含完整的响应数据和AI回复内容
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CerebroNonStreamResponse {

    /**
     * 响应数据列表，包含工作流执行的详细信息
     */
    private List<ResponseData> responseData;

    /**
     * 新变量，包含任务ID等信息
     */
    private NewVariables newVariables;

    /**
     * 响应ID
     * chatId
     */
    private String id;

    /**
     * OpenAI格式的选择列表（用于兼容OpenAI API）
     */
    private List<Choice> choices;

    /**
     * AI回复的纯文本内容（从choices中提取，忽略思考过程）
     */
    private String aiTextContent;

    /**
     * AI的思考过程内容（从choices中提取reasoning类型内容）
     */
    private String aiReasoningContent;

    /**
     * 响应数据项
     */
    @Data
    @Accessors(chain = true)
    public static class ResponseData {
        
        /**
         * 响应ID
         */
        private String id;

        /**
         * 节点ID
         */
        private String nodeId;

        /**
         * 模块名称
         */
        private String moduleName;

        /**
         * 模块类型
         */
        private String moduleType;

        /**
         * 运行时间
         */
        private Double runningTime;

        /**
         * 总点数
         */
        private Integer totalPoints;

        /**
         * 输入令牌数
         */
        private Integer inputTokens;

        /**
         * 输出令牌数
         */
        private Integer outputTokens;

        /**
         * 工具调用输入令牌数
         */
        private Integer toolCallInputTokens;

        /**
         * 工具调用输出令牌数
         */
        private Integer toolCallOutputTokens;

        /**
         * 子节点总点数
         */
        private Integer childTotalPoints;

    }

    /**
     * 新变量
     */
    @Data
    @Accessors(chain = true)
    public static class NewVariables {
        
        /**
         * 任务ID
         */
        private String taskId;
    }

    /**
     * OpenAI格式的选择项
     */
    @Data
    @Accessors(chain = true)
    public static class Choice {
        
        /**
         * 消息内容
         */
        private Message message;

        /**
         * 完成原因
         */
        @JsonProperty("finish_reason")
        private String finishReason;

        /**
         * 选择索引
         */
        private Integer index;
    }

    /**
     * 消息内容
     */
    @Data
    @Accessors(chain = true)
    public static class Message {

        /**
         * 角色
         */
        private String role;

        /**
         * 内容 - 可以是字符串或包含思考过程的内容项列表
         */
        private Object content;
    }

    /**
     * 消息内容的统一接口
     */
    @JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
    @JsonSubTypes({
        @JsonSubTypes.Type(value = SimpleMessageContent.class),
        @JsonSubTypes.Type(value = ComplexMessageContent.class)
    })
    public interface MessageContent {
    }

    /**
     * 简单文本内容
     */
    @Data
    @Accessors(chain = true)
    public static class SimpleMessageContent implements MessageContent {
        private String value;

        // Jackson 反序列化构造函数
        public SimpleMessageContent() {}

        public SimpleMessageContent(String value) {
            this.value = value;
        }
    }

    /**
     * 复杂内容（包含思考过程和文本）
     */
    @Data
    @Accessors(chain = true)
    public static class ComplexMessageContent implements MessageContent {
        private List<ContentItem> items;
    }

    /**
     * 内容项基类
     */
    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
    @JsonSubTypes({
        @JsonSubTypes.Type(value = ReasoningContentItem.class, name = "reasoning"),
        @JsonSubTypes.Type(value = TextContentItem.class, name = "text")
    })
    public abstract static class ContentItem {
        @JsonProperty("type")
        public abstract String getType();
    }

    /**
     * 思考过程内容项
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    public static class ReasoningContentItem extends ContentItem {
        private ReasoningData reasoning;

        @Override
        public String getType() {
            return "reasoning";
        }
    }

    /**
     * 文本内容项
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    public static class TextContentItem extends ContentItem {
        private TextData text;

        @Override
        public String getType() {
            return "text";
        }
    }

    /**
     * 思考数据
     */
    @Data
    @Accessors(chain = true)
    public static class ReasoningData {
        private String content;
    }

    /**
     * 文本数据
     */
    @Data
    @Accessors(chain = true)
    public static class TextData {
        private String content;
    }

    // ==================== 便利方法 ====================

    /**
     * 获取任务ID
     */
    public String getTaskId() {
        if (newVariables != null) {
            return newVariables.getTaskId();
        }
        return null;
    }

    /**
     * 获取AI回复内容（从choices中提取）
     * 仅提取文本内容，忽略思考过程
     * 如果aiTextContent字段已设置，直接返回；否则从choices中提取
     */
    public String getAiContent() {
        if (aiTextContent != null) {
            return aiTextContent;
        }

        if (choices != null && !choices.isEmpty()) {
            Choice choice = choices.getFirst();
            if (choice.getMessage() != null) {
                Object content = choice.getMessage().getContent();
                return extractTextContent(content);
            }
        }
        return null;
    }

    /**
     * 获取完整的消息内容对象
     * 返回原始的content对象，可能是字符串或复杂对象数组
     */
    public Object getFullMessageContent() {
        if (choices != null && !choices.isEmpty()) {
            Choice choice = choices.getFirst();
            if (choice.getMessage() != null) {
                return choice.getMessage().getContent();
            }
        }
        return null;
    }

    /**
     * 判断内容是否包含思考过程
     */
    public boolean hasReasoningContent() {
        String reasoning = getReasoningContent();
        return reasoning != null && !reasoning.trim().isEmpty();
    }

    /**
     * 解析并设置AI文本内容和思考内容字段
     * 通常在反序列化后调用此方法来填充便利字段
     */
    public void parseAndSetAiContent() {
        if (choices != null && !choices.isEmpty()) {
            Choice choice = choices.getFirst();
            if (choice.getMessage() != null) {
                Object content = choice.getMessage().getContent();
                this.aiTextContent = extractTextContent(content);
                this.aiReasoningContent = extractReasoningContent(content);
            }
        }
    }

    /**
     * 从content中提取文本内容
     * 支持字符串和复杂对象数组两种格式
     */
    private String extractTextContent(Object content) {
        if (content == null) {
            return null;
        }

        // 如果是字符串，直接返回
        if (content instanceof String) {
            return (String) content;
        }

        // 如果是List，处理复杂对象格式
        if (content instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> contentList = (List<Object>) content;
            StringBuilder textBuilder = new StringBuilder();

            for (Object item : contentList) {
                if (item instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> itemMap = (java.util.Map<String, Object>) item;
                    String type = (String) itemMap.get("type");

                    if ("text".equals(type)) {
                        Object textObj = itemMap.get("text");
                        if (textObj instanceof java.util.Map) {
                            @SuppressWarnings("unchecked")
                            java.util.Map<String, Object> textMap = (java.util.Map<String, Object>) textObj;
                            String textContent = (String) textMap.get("content");
                            if (textContent != null) {
                                textBuilder.append(textContent);
                            }
                        }
                    }
                    // 忽略 reasoning 类型的内容，只提取 text 类型
                }
            }

            return !textBuilder.isEmpty() ? textBuilder.toString() : null;
        }

        // 其他情况，尝试转换为字符串
        return content.toString();
    }

    /**
     * 从content中提取思考过程内容
     */
    private String extractReasoningContent(Object content) {
        if (content == null || !(content instanceof List)) {
            return null;
        }

        @SuppressWarnings("unchecked")
        List<Object> contentList = (List<Object>) content;
        StringBuilder reasoningBuilder = new StringBuilder();

        for (Object item : contentList) {
            if (item instanceof java.util.Map) {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> itemMap = (java.util.Map<String, Object>) item;
                String type = (String) itemMap.get("type");

                if ("reasoning".equals(type)) {
                    Object reasoningObj = itemMap.get("reasoning");
                    if (reasoningObj instanceof java.util.Map) {
                        @SuppressWarnings("unchecked")
                        java.util.Map<String, Object> reasoningMap = (java.util.Map<String, Object>) reasoningObj;
                        String reasoningContent = (String) reasoningMap.get("content");
                        if (reasoningContent != null) {
                            reasoningBuilder.append(reasoningContent);
                        }
                    }
                }
            }
        }

        return !reasoningBuilder.isEmpty() ? reasoningBuilder.toString() : null;
    }

    /**
     * 判断内容是否为复杂格式（包含思考过程）
     */
    private boolean isComplexContent(Object content) {
        if (!(content instanceof List)) {
            return false;
        }

        @SuppressWarnings("unchecked")
        List<Object> contentList = (List<Object>) content;

        for (Object item : contentList) {
            if (item instanceof java.util.Map) {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> itemMap = (java.util.Map<String, Object>) item;
                String type = (String) itemMap.get("type");
                if ("reasoning".equals(type)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取完整的AI回复内容（包括思考过程）
     * 返回原始的content对象，可能是字符串或复杂对象数组
     */
    public Object getFullAiContent() {
        if (choices != null && !choices.isEmpty()) {
            Choice choice = choices.get(0);
            if (choice.getMessage() != null) {
                return choice.getMessage().getContent();
            }
        }
        return null;
    }

    /**
     * 获取AI的思考过程内容
     * 仅从复杂对象格式中提取reasoning类型的内容
     */
    public String getReasoningContent() {
        if (choices != null && !choices.isEmpty()) {
            Choice choice = choices.get(0);
            if (choice.getMessage() != null) {
                Object content = choice.getMessage().getContent();
                return extractReasoningContent(content);
            }
        }
        return null;
    }

    /**
     * 获取完成原因
     */
    public String getFinishReason() {
        if (choices != null && !choices.isEmpty()) {
            return choices.getFirst().getFinishReason();
        }
        return null;
    }

    /**
     * 获取总输入令牌数
     */
    public Integer getTotalInputTokens() {
        if (responseData != null) {
            return responseData.stream()
                    .mapToInt(data -> data.getInputTokens() != null ? data.getInputTokens() : 0)
                    .sum();
        }
        return 0;
    }

    /**
     * 获取总输出令牌数
     */
    public Integer getTotalOutputTokens() {
        if (responseData != null) {
            return responseData.stream()
                    .mapToInt(data -> data.getOutputTokens() != null ? data.getOutputTokens() : 0)
                    .sum();
        }
        return 0;
    }

    /**
     * 获取总运行时间
     */
    public Double getTotalRunningTime() {
        if (responseData != null) {
            return responseData.stream()
                    .mapToDouble(data -> data.getRunningTime() != null ? data.getRunningTime() : 0.0)
                    .sum();
        }
        return 0.0;
    }
}
