package cn.genn.ai.cerebro.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CerebroRequest {

    /**
     * 工作流ID
     */
    private String appId;

    /**
     * 聊天id,同一个chatId下的消息会被关联到同一个对话中
     */
    private String chatId;

    /**
     * 任务ID,如果传了taskId,则会继续之前的task
     * 常用于交互式节点继续
     */
    private String taskId;

    /**
     * 消息列表
     */
    private List<CerebroMessage> messages;

    /**
     * 全局变量
     */
    private Map<String, String> variables;

    /**
     * AI回复的dataId,如果不设置,自动生成
     */
    private String responseChatItemId;

    /**
     * 是否返回详细信息
     * true: 每条sse消息都会携带event和data'
     * false: 只返回data
     */
    private Boolean detail = true;

    /**
     * 是否流式返回
     */
    private Boolean stream = true;

    /**
     * 超时时间（秒）
     */
    private Integer timeout = 300;


}
