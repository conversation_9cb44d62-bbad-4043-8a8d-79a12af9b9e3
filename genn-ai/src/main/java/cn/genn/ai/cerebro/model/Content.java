package cn.genn.ai.cerebro.model;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * Content
 */
@Data
@Builder
@Slf4j
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class Content {
    /**
     * 符合OpenAI标准的类型定义
     * @see <a href="https://platform.openai.com/docs/guides/vision">Vision Guide</a>
     */
    private Type type;
    private String text;

    @JsonProperty("image_url")
    private ImageUrl imageUrl;

    /**
     * 当类型是文件时,传入该值
     */
    @JsonProperty("url")
    private String url;

    @JsonProperty("name")
    private String name;

    private Map<String, Object> rawData;

    @Getter
    public enum Type {
        TEXT("text"),
        IMAGE_URL("image_url"), // 图片
        FILE_URL("file_url"); // 文件

        @JsonValue
        private final String value;

        Type(String value) {
            this.value = value;
        }

    }

    public static Content ofText(String text) {
        Content content = new Content();
        content.type = Type.TEXT;
        content.text = text;
        return content;
    }

    public static Content ofImage(String urlOrDataURI) {
        Content content = new Content();
        content.type = Type.IMAGE_URL;
        content.imageUrl = new ImageUrl(urlOrDataURI);
        return content;
    }

    public static Content ofImageFile(File file) throws IOException {
        String mimeType = FileUtil.getMimeType(file.getPath());
        byte[] data = Files.readAllBytes(file.toPath());
        return ofImageBytes(data, mimeType);
    }

    public static Content ofImageBytes(byte[] data, String mimeType) {
        String base64 = Base64.getEncoder().encodeToString(data);
        String dataURI = "data:" + mimeType + ";base64," + base64;
        return ofImage(dataURI);
    }

    public static Content ofImageStream(InputStream stream, String mimeType) throws IOException {
        try (InputStream is = stream) {
            return ofImageBytes(IoUtil.readBytes(is), mimeType);
        }
    }

    public static List<Content> textWithImages(String text, List<String> imageSources) {
        List<Content> contents = new ArrayList<>();
        imageSources.forEach(url -> contents.add(ofImage(url)));
        contents.add(ofText(text));
        return contents;
    }

    public static List<Content> textWithImageFiles(String text, List<File> imageFiles) {
        List<Content> contents = new ArrayList<>();
        imageFiles.forEach(file -> {
            try {
                contents.add(ofImageFile(file));
            } catch (IOException e) {
                log.error("Failed to read image file: {}", file, e);
            }
        });
        contents.add(ofText(text));
        return contents;
    }

    // ==================== 文件相关的静态工厂方法 ====================

    /**
     * 创建文件类型的Content
     * @param url 文件URL
     * @param fileName 文件名
     * @return Content对象
     */
    public static Content ofFile(String url, String fileName) {
        return Content.builder()
                .type(Type.FILE_URL)
                .url(url)
                .name(fileName)
                .build();
    }

    /**
     * 创建文件类型的Content，带原始数据
     * @param url 文件URL
     * @param fileName 文件名
     * @param rawData 原始数据
     * @return Content对象
     */
    public static Content ofFile(String url, String fileName, Map<String, Object> rawData) {
        return Content.builder()
                .type(Type.FILE_URL)
                .url(url)
                .name(fileName)
                .rawData(rawData)
                .build();
    }

    /**
     * 创建图片类型的Content，带文件名和原始数据
     * @param url 图片URL
     * @param fileName 文件名
     * @param rawData 原始数据
     * @return Content对象
     */
    public static Content ofImage(String url, String fileName, Map<String, Object> rawData) {
        return Content.builder()
                .type(Type.IMAGE_URL)
                .imageUrl(new ImageUrl(url))
                .name(fileName)
                .rawData(rawData)
                .build();
    }

    /**
     * 根据URL自动判断类型创建Content（简化版本）
     * 根据文件扩展名判断是图片还是文件
     * @param url 文件URL
     * @param fileName 文件名
     * @return Content对象
     */
    public static Content ofAuto(String url, String fileName) {
        if (isImageUrl(url)) {
            return ofImage(url, fileName, null);
        } else {
            return ofFile(url, fileName);
        }
    }

    /**
     * 根据URL和原始数据自动判断类型创建Content
     * @param url 文件URL
     * @param fileName 文件名
     * @param rawData 原始数据
     * @return Content对象
     */
    public static Content ofAuto(String url, String fileName, Map<String, Object> rawData) {
        if (isImageUrl(url)) {
            return ofImage(url, fileName, rawData);
        } else {
            return ofFile(url, fileName, rawData);
        }
    }

    /**
     * 批量创建混合内容（文本+文件/图片）
     * @param text 文本内容
     * @param fileInfos 文件信息列表，每个Map包含url、fileName等信息
     * @return Content列表
     */
    public static List<Content> textWithFiles(String text, List<FileInfo> fileInfos) {
        List<Content> contents = new ArrayList<>();

        // 添加文件内容
        if (fileInfos != null && !fileInfos.isEmpty()) {
            fileInfos.forEach(fileInfo -> {
                String url = fileInfo.getUrl();
                String fileName = fileInfo.getFileName();
                Map<String, Object> rawData = fileInfo.getRawData();
                if (url != null) {
                    contents.add(ofAuto(url, fileName, fileInfo.getRawData()));
                }
            });
        }

        // 添加文本内容（如果有的话）
        if (text != null && !text.trim().isEmpty()) {
            contents.add(ofText(text));
        }

        return contents;
    }

    /**
     * 创建纯文件列表内容
     * @param fileInfos 文件信息列表
     * @return Content列表
     */
    public static List<Content> ofFiles(List<FileInfo> fileInfos) {
        return textWithFiles(null, fileInfos);
    }

    // ==================== 工具方法 ====================

    /**
     * 判断URL是否为图片类型
     * @param url 文件URL
     * @return 是否为图片
     */
    private static boolean isImageUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        String lowerUrl = url.toLowerCase();
        return lowerUrl.contains(".jpg") || lowerUrl.contains(".jpeg") ||
               lowerUrl.contains(".png") || lowerUrl.contains(".gif") ||
               lowerUrl.contains(".bmp") || lowerUrl.contains(".webp") ||
               lowerUrl.contains(".svg") || lowerUrl.contains(".ico");
    }

    /**
     * 检查当前Content是否为文件类型
     * @return 是否为文件类型
     */
    public boolean isFile() {
        return Type.FILE_URL.equals(this.type);
    }

    /**
     * 检查当前Content是否为图片类型
     * @return 是否为图片类型
     */
    public boolean isImage() {
        return Type.IMAGE_URL.equals(this.type);
    }

    /**
     * 检查当前Content是否为文本类型
     * @return 是否为文本类型
     */
    public boolean isText() {
        return Type.TEXT.equals(this.type);
    }

    @Data
    public static class ImageUrl {
        // 保持与API字段名一致
        private String url;

        public ImageUrl(String url) {
            this.url = url;
        }

    }
    
    @Data
    @Accessors(chain = true)
    public static class FileInfo {
        private String url;
        private String fileName;
        private Map<String, Object> rawData;
    }
}
